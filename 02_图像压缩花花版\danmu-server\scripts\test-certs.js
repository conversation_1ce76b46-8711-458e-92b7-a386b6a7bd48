#!/usr/bin/env node

/**
 * 证书管理系统测试脚本
 * 用于验证证书配置和文件状态
 */

const CertManager = require('../services/certManager');

console.log('🔍 证书管理系统测试');
console.log('='.repeat(50));

// 测试1: 获取所有商户状态
console.log('\n📊 1. 获取所有商户状态:');
try {
  const status = CertManager.getAllMerchantsStatus();
  Object.keys(status).forEach(merchantId => {
    const merchant = status[merchantId];
    console.log(`\n商户: ${merchantId}`);
    console.log(`  - 配置文件: ${merchant.hasConfig ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`  - 激活状态: ${merchant.isActive ? '✅ 已激活' : '⚠️ 未激活'}`);
    console.log(`  - 描述: ${merchant.description}`);
    
    if (merchant.validation) {
      console.log(`  - 配置验证: ${merchant.validation.isValid ? '✅ 通过' : '❌ 失败'}`);
      if (merchant.validation.errors.length > 0) {
        console.log(`    错误: ${merchant.validation.errors.join(', ')}`);
      }
      if (merchant.validation.warnings.length > 0) {
        console.log(`    警告: ${merchant.validation.warnings.join(', ')}`);
      }
    }
    
    if (merchant.certificates) {
      console.log(`  - 证书文件:`);
      console.log(`    API证书: ${merchant.certificates.cert?.exists ? '✅' : '❌'}`);
      console.log(`    API私钥: ${merchant.certificates.key?.exists ? '✅' : '❌'}`);
      console.log(`    PKCS12: ${merchant.certificates.p12?.exists ? '✅' : '⚠️'}`);
    }
  });
} catch (error) {
  console.error('❌ 获取商户状态失败:', error.message);
}

// 测试2: 获取活跃商户列表
console.log('\n📋 2. 获取活跃商户列表:');
try {
  const activeMerchants = CertManager.getActiveMerchants();
  if (activeMerchants.length > 0) {
    activeMerchants.forEach(merchant => {
      console.log(`  ✅ ${merchant.merchantId} - ${merchant.description}`);
    });
  } else {
    console.log('  ⚠️ 没有活跃的商户');
  }
} catch (error) {
  console.error('❌ 获取活跃商户失败:', error.message);
}

// 测试3: 验证各个商户配置
console.log('\n🔍 3. 验证各个商户配置:');
const merchants = ['guofuli1700692997', 'lixiang1717867742', 'haocaihua1717453427'];

merchants.forEach(merchantId => {
  console.log(`\n验证商户: ${merchantId}`);
  try {
    const validation = CertManager.validateMerchant(merchantId);
    console.log(`  状态: ${validation.isValid ? '✅ 配置有效' : '❌ 配置无效'}`);
    
    if (validation.errors.length > 0) {
      console.log(`  错误:`);
      validation.errors.forEach(error => {
        console.log(`    - ${error}`);
      });
    }
    
    if (validation.warnings.length > 0) {
      console.log(`  警告:`);
      validation.warnings.forEach(warning => {
        console.log(`    - ${warning}`);
      });
    }
    
  } catch (error) {
    console.log(`  ❌ 验证失败: ${error.message}`);
  }
});

// 测试4: 检查证书文件
console.log('\n📁 4. 检查证书文件状态:');
merchants.forEach(merchantId => {
  console.log(`\n商户: ${merchantId}`);
  try {
    const certStatus = CertManager.checkCertFiles(merchantId);
    
    Object.keys(certStatus).forEach(certType => {
      const status = certStatus[certType];
      const icon = status.exists ? '✅' : '❌';
      console.log(`  ${certType}: ${icon} ${status.exists ? '存在' : '不存在'}`);
      if (!status.exists && status.error) {
        console.log(`    错误: ${status.error}`);
      }
    });
    
  } catch (error) {
    console.log(`  ❌ 检查失败: ${error.message}`);
  }
});

// 测试5: 测试默认商户配置
console.log('\n🎯 5. 测试默认商户配置:');
try {
  const defaultConfig = CertManager.getMerchantConfig();
  console.log(`  ✅ 默认商户: ${defaultConfig.description}`);
  console.log(`  商户号: ${defaultConfig.merchantId}`);
  console.log(`  AppID: ${defaultConfig.appId}`);
  console.log(`  激活状态: ${defaultConfig.isActive ? '✅ 已激活' : '⚠️ 未激活'}`);
} catch (error) {
  console.log(`  ❌ 获取默认商户配置失败: ${error.message}`);
}

console.log('\n' + '='.repeat(50));
console.log('🎉 证书管理系统测试完成');

// 输出使用建议
console.log('\n💡 使用建议:');
console.log('1. 将证书文件放置到对应商户目录');
console.log('2. 编辑 config.json 填写正确的商户信息');
console.log('3. 设置文件权限: chmod 600 *.pem *.p12');
console.log('4. 重新运行此脚本验证配置');
console.log('5. 部署后调用 API 接口进行最终验证');

console.log('\n🔗 相关API接口:');
console.log('- GET /api/certs/status - 获取所有商户状态');
console.log('- GET /api/certs/validate/guofuli1700692997 - 验证主商户');
console.log('- GET /api/certs/validate/lixiang1717867742 - 验证备用商户2');
console.log('- GET /api/certs/validate/haocaihua1717453427 - 验证备用商户3');
console.log('- POST /api/certs/reload - 重新加载配置');
