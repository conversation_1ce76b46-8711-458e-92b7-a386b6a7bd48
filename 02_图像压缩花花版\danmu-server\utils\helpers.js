// 生成随机ID
const generateId = (length = 8) => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 获取客户端IP
const getClientIP = (req) => {
  // 优先获取真实IP（考虑代理和负载均衡）
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  const clientIP = req.connection.remoteAddress || 
                   req.socket.remoteAddress ||
                   (req.connection.socket ? req.connection.socket.remoteAddress : null);
  
  if (forwarded) {
    // x-forwarded-for 可能包含多个IP，取第一个
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  // 处理IPv6映射的IPv4地址
  if (clientIP && clientIP.startsWith('::ffff:')) {
    return clientIP.substring(7);
  }
  
  return clientIP || 'unknown';
};

// 验证分享数据
const validateShareData = (data) => {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const required = ['danmuText', 'styleType', 'speedClass', 'sizeClass', 'fontClass', 'runningMode'];

  for (const field of required) {
    if (!data.hasOwnProperty(field)) {
      return false;
    }
  }

  // 验证字段值的有效性和长度
  const validStyleTypes = ['blue-yellow', 'black-white', 'black-pink', 'shake-effect', 'blue-white', 'custom'];
  const validSpeedClasses = ['speed-none', 'speed-slow', 'speed-normal', 'speed-fast'];
  const validSizeClasses = ['size-small', 'size-normal', 'size-large', 'size-xlarge'];
  const validFontClasses = ['font-default', 'font-fashion', 'font-rock'];
  const validRunningModes = ['normal', 'mirror'];

  // 验证字段长度（防止超出数据库限制）
  const maxFieldLength = 40; // VARCHAR(40) 的限制
  if (data.styleType.length > maxFieldLength) return false;
  if (data.speedClass.length > maxFieldLength) return false;
  if (data.sizeClass.length > maxFieldLength) return false;
  if (data.fontClass.length > maxFieldLength) return false;
  if (data.runningMode.length > maxFieldLength) return false;

  // 验证自定义字段长度（如果存在）
  if (data.customShakeLevel && data.customShakeLevel.length > maxFieldLength) return false;
  if (data.customShadowType && data.customShadowType.length > maxFieldLength) return false;

  if (!validStyleTypes.includes(data.styleType)) return false;
  if (!validSpeedClasses.includes(data.speedClass)) return false;
  if (!validSizeClasses.includes(data.sizeClass)) return false;
  if (!validFontClasses.includes(data.fontClass)) return false;
  if (!validRunningModes.includes(data.runningMode)) return false;

  // 验证文字长度
  if (typeof data.danmuText !== 'string' || data.danmuText.length > 500) {
    return false;
  }

  // 如果是自定义样式，验证自定义参数
  if (data.styleType === 'custom') {
    if (!data.customTextColor || !data.customBgColor) {
      return false;
    }

    // 简单的颜色格式验证
    const colorRegex = /^#[0-9A-Fa-f]{6}$/;
    if (!colorRegex.test(data.customTextColor) || !colorRegex.test(data.customBgColor)) {
      return false;
    }
  }

  return true;
};

// 清理分享数据（移除不必要的字段）
const sanitizeShareData = (data) => {
  const allowedFields = [
    'danmuText', 'styleType', 'speedClass', 'sizeClass', 'fontClass', 'runningMode',
    'customTextColor', 'customBgColor', 'customShakeLevel', 'customShadowType'
  ];

  const sanitized = {};

  for (const field of allowedFields) {
    if (data.hasOwnProperty(field)) {
      sanitized[field] = data[field];
    }
  }

  return sanitized;
};

// 格式化错误响应
const formatError = (message, code = 'UNKNOWN_ERROR', details = null) => {
  const error = {
    success: false,
    error: message,
    code: code,
    timestamp: new Date().toISOString()
  };
  
  if (details && process.env.NODE_ENV !== 'production') {
    error.details = details;
  }
  
  return error;
};

// 格式化成功响应
const formatSuccess = (data, message = null) => {
  const response = {
    success: true,
    data: data,
    timestamp: new Date().toISOString()
  };

  if (message) {
    response.message = message;
  }

  return response;
};

// 检查数据大小
const checkDataSize = (data) => {
  const maxSize = parseInt(process.env.MAX_CONFIG_SIZE) || 10240; // 默认10KB
  const dataSize = JSON.stringify(data).length;
  
  return dataSize <= maxSize;
};

module.exports = {
  generateId,
  getClientIP,
  validateShareData,
  sanitizeShareData,
  formatError,
  formatSuccess,
  checkDataSize
};
