# 弹幕分享服务器 API 文档

## 概述

弹幕分享服务器为手持弹幕小程序提供用户额度管理、分享功能和付费服务的后端支持。

## 基础信息

- **服务器端口**: 8850
- **API 基础路径**: `/api`
- **数据库**: MySQL 8.0.36
- **认证方式**: 微信 openid

## 用户额度系统

### 免费额度
- **分享功能**: 每个新用户总共3次免费分享机会
- **全屏功能**: 每天3次免费使用，每日0点重置

### VIP会员
- **24小时卡**: ¥2.99 - 24小时内无限使用
- **终身会员**: ¥19.9 - 永久无限使用

## API 接口

### 1. 用户相关 (`/api/user`)

#### 1.1 用户登录/注册
```http
POST /api/user/login
```

**请求参数:**
```json
{
  "openid": "string",
  "userInfo": {
    "nickname": "string",
    "avatar_url": "string"
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "userId": 1,
      "openid": "string",
      "nickname": "string",
      "avatarUrl": "string",
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "quota": {
      "freeShareCount": 3,
      "dailyFullscreenCount": 3,
      "totalShareCount": 0,
      "totalFullscreenCount": 0,
      "hasVipAccess": false,
      "isVip": false,
      "vipExpireTime": null,
      "tempVipExpireTime": null
    }
  },
  "message": "登录成功"
}
```

#### 1.2 获取用户额度信息
```http
GET /api/user/quota/:userId
```

#### 1.3 检查分享权限
```http
POST /api/user/check-share
```

#### 1.4 检查全屏权限
```http
POST /api/user/check-fullscreen
```

#### 1.5 使用分享次数
```http
POST /api/user/use-share
```

#### 1.6 使用全屏次数
```http
POST /api/user/use-fullscreen
```

### 2. 订单相关 (`/api/order`)

#### 2.1 获取产品配置
```http
GET /api/order/products
```

**响应:**
```json
{
  "success": true,
  "data": {
    "temp_vip": {
      "name": "24小时无限使用",
      "price": 2.99,
      "description": "24小时内无限制使用分享和全屏功能"
    },
    "lifetime_vip": {
      "name": "终身会员",
      "price": 19.9,
      "description": "永久无限制使用所有功能"
    }
  }
}
```

#### 2.2 创建订单
```http
POST /api/order/create
```

**请求参数:**
```json
{
  "userId": 1,
  "openid": "string",
  "productType": "temp_vip" // 或 "lifetime_vip"
}
```

#### 2.3 获取订单详情
```http
GET /api/order/:orderId
```

#### 2.4 获取用户订单列表
```http
GET /api/order/user/:userId?page=1&limit=20
```

#### 2.5 模拟支付成功（测试用）
```http
POST /api/order/pay-success
```

**请求参数:**
```json
{
  "orderId": "string",
  "transactionId": "string"
}
```

### 3. 弹幕分享相关 (`/api/danmu`)

#### 3.1 保存弹幕分享配置
```http
POST /api/danmu/save
```

**请求参数:**
```json
{
  "userId": 1,
  "openid": "string",
  "danmuText": "弹幕内容",
  "styleType": "blue-yellow",
  "speedClass": "speed-normal",
  "sizeClass": "size-normal",
  "fontClass": "font-default",
  "runningMode": "normal",
  "customTextColor": "#FF00FF",
  "customBgColor": "#000000",
  "customShakeLevel": "none",
  "customShadowType": "none"
}
```

#### 3.2 获取弹幕分享配置
```http
GET /api/danmu/get/:shareId
```

### 4. 全屏相关 (`/api/fullscreen`)

#### 4.1 检查全屏使用权限
```http
POST /api/fullscreen/check
```

**请求参数:**
```json
{
  "userId": 1,
  "openid": "string"
}
```

#### 4.2 使用全屏功能
```http
POST /api/fullscreen/use
```

#### 4.3 获取全屏使用统计
```http
GET /api/fullscreen/stats?days=7
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| `MISSING_OPENID` | 缺少openid参数 |
| `INVALID_USER_ID` | 无效的用户ID |
| `INSUFFICIENT_SHARE_COUNT` | 分享次数不足 |
| `INSUFFICIENT_FULLSCREEN_COUNT` | 全屏次数不足 |
| `INVALID_PRODUCT_TYPE` | 无效的产品类型 |
| `ORDER_NOT_FOUND` | 订单不存在 |
| `ORDER_ALREADY_PAID` | 订单已支付 |

## 定时任务

系统自动运行以下定时任务：

1. **每日0点**: 重置所有用户的每日免费全屏使用次数
2. **每小时**: 清理过期订单
3. **每天2点**: 清理过期分享配置
4. **每周日3点**: 清理90天前的使用日志
5. **每小时30分**: 更新过期的VIP会员状态

## 数据库表结构

### users 表
- 用户基本信息
- 免费额度管理
- VIP会员状态

### orders 表
- 订单信息
- 支付状态
- 产品类型

### usage_logs 表
- 使用记录
- 统计分析

### danmu_shares 表
- 弹幕分享配置
- 访问统计

## 部署说明

1. 确保 MySQL 8.0.36 已安装并运行
2. 执行 `database.sql` 初始化数据库
3. 配置环境变量（.env 文件）
4. 安装依赖: `npm install`
5. 启动服务: `npm start`

## 环境变量

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=danmu_share
DB_PASSWORD=B7cJhHDSfp586DYy
DB_NAME=danmu_share

# 服务器配置
PORT=8850
NODE_ENV=production

# 分享配置
CONFIG_EXPIRE_DAYS=30
```
