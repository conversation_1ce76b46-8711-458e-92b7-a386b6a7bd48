# 弹幕分享服务器 (danmu-server)

这是手持弹幕小程序的服务器端代码，提供弹幕配置的分享和存储功能。

## 功能特点

- 🚀 基于 Express.js 的 RESTful API
- 🗄️ MySQL 数据库存储
- 🔗 短链接生成（8位随机ID）
- ⏰ 配置自动过期（默认30天）
- 📊 访问统计和热门配置
- 🛡️ 数据验证和安全防护
- 🌐 跨域支持

## 技术栈

- **框架**: Express.js 4.18+
- **数据库**: MySQL 8.0.36+
- **Node.js**: 14.0+
- **依赖管理**: npm

## 安装与配置

### 前置条件

- Node.js 14.0.0 或更高版本
- MySQL 8.0.36 数据库
- npm 包管理器

### 安装步骤

1. 安装依赖：

```bash
npm install
```

2. 配置环境变量：

复制 `.env.example` 文件为 `.env`，并填写您的配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 服务器配置
PORT=3000
NODE_ENV=production

# 数据库配置
DB_HOST=localhost
DB_USER=danmu_user
DB_PASSWORD=your_password_here
DB_NAME=danmu_share

# 功能配置
CONFIG_EXPIRE_DAYS=30
MAX_CONFIG_SIZE=10240
```

3. 创建数据库：

```sql
CREATE DATABASE danmu_share CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 启动服务器

```bash
# 生产环境
npm start

# 开发环境（自动重启）
npm run dev
```

## API 接口

### 基础信息

- **基础URL**: `http://your-domain.com/api/danmu`
- **响应格式**: JSON
- **字符编码**: UTF-8

### 接口列表

#### 1. 保存弹幕配置

```http
POST /api/danmu/save
```

**请求体**:
```json
{
  "danmuText": "你好世界",
  "styleType": "black-white",
  "speedClass": "speed-fast",
  "sizeClass": "size-large",
  "fontClass": "font-fashion",
  "runningMode": "mirror"
}
```

**响应**:
```json
{
  "success": true,
  "id": "abc12345",
  "url": "/pages/fullscreen/fullscreen?id=abc12345",
  "expires_at": "2024-02-15T10:30:00.000Z",
  "expires_in_days": 30,
  "message": "配置保存成功",
  "timestamp": "2024-01-16T10:30:00.000Z"
}
```

#### 2. 获取弹幕配置

```http
GET /api/danmu/get/:id
```

**响应**:
```json
{
  "success": true,
  "config": {
    "danmuText": "你好世界",
    "styleType": "black-white",
    "speedClass": "speed-fast",
    "sizeClass": "size-large",
    "fontClass": "font-fashion",
    "runningMode": "mirror"
  },
  "created_at": "2024-01-16T10:30:00.000Z",
  "access_count": 5,
  "message": "配置获取成功",
  "timestamp": "2024-01-16T10:30:00.000Z"
}
```

#### 3. 获取统计信息

```http
GET /api/danmu/stats
```

#### 4. 获取热门配置

```http
GET /api/danmu/popular?limit=10
```

#### 5. 获取配置历史

```http
GET /api/danmu/history?limit=20
```

#### 6. 清理过期配置

```http
DELETE /api/danmu/cleanup
```

## 数据库设计

### configs 表结构

```sql
CREATE TABLE configs (
    id VARCHAR(8) PRIMARY KEY COMMENT '短链接ID',
    config_data JSON NOT NULL COMMENT '弹幕配置数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    access_count INT DEFAULT 0 COMMENT '访问次数',
    ip_address VARCHAR(45) COMMENT '创建者IP',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at),
    INDEX idx_access_count (access_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 部署指南

### 宝塔面板部署

1. **安装 Node.js 管理器**
2. **创建 Node.js 项目**
3. **上传代码文件**
4. **安装依赖**: `npm install`
5. **配置环境变量**
6. **启动项目**: 使用 PM2 管理
7. **配置反向代理**: Nginx 配置域名和 SSL

### PM2 配置示例

```json
{
  "name": "danmu-server",
  "script": "app.js",
  "instances": 1,
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "PORT": 3000
  }
}
```

## 安全考虑

- ✅ 输入数据验证
- ✅ SQL 注入防护
- ✅ XSS 防护
- ✅ 请求大小限制
- ✅ 错误信息过滤
- ✅ IP 地址记录

## 监控和维护

- 定期清理过期配置
- 监控数据库性能
- 查看访问日志
- 备份重要数据

## 许可证

MIT License
