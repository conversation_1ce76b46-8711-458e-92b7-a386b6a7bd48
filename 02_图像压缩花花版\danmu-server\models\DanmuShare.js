const { pool } = require('../config/database');

class DanmuShare {
  // 保存分享配置（不包含user_id）
  static async save(shareId, shareData, creatorIP, creatorOpenid, creatorUserAgent) {
    const expireDays = parseInt(process.env.CONFIG_EXPIRE_DAYS) || 30;
    const expiresAt = new Date(Date.now() + expireDays * 24 * 60 * 60 * 1000);

    const [result] = await pool.execute(
      `INSERT INTO danmu_shares (
        share_id, danmu_text, style_type, speed_class, size_class, font_class, running_mode,
        custom_text_color, custom_bg_color, custom_shake_level, custom_shadow_type,
        expires_at, creator_ip, creator_openid, creator_user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        shareId,
        shareData.danmuText,
        shareData.styleType,
        shareData.speedClass,
        shareData.sizeClass,
        shareData.fontClass,
        shareData.runningMode,
        shareData.customTextColor || null,
        shareData.customBgColor || null,
        shareData.customShakeLevel || null,
        shareData.customShadowType || null,
        expiresAt,
        creatorIP,
        creatorOpenid || null,
        creatorUserAgent
      ]
    );

    return result;
  }

  // 根据分享ID获取配置
  static async getByShareId(shareId) {
    const [rows] = await pool.execute(
      `SELECT
        share_id, danmu_text, style_type, speed_class, size_class, font_class, running_mode,
        custom_text_color, custom_bg_color, custom_shake_level, custom_shadow_type,
        created_at, expires_at, access_count
       FROM danmu_shares
       WHERE share_id = ? AND (expires_at IS NULL OR expires_at > NOW())`,
      [shareId]
    );

    if (rows.length > 0) {
      const row = rows[0];
      return {
        shareId: row.share_id,
        danmuText: row.danmu_text,
        styleType: row.style_type,
        speedClass: row.speed_class,
        sizeClass: row.size_class,
        fontClass: row.font_class,
        runningMode: row.running_mode,
        customTextColor: row.custom_text_color,
        customBgColor: row.custom_bg_color,
        customShakeLevel: row.custom_shake_level,
        customShadowType: row.custom_shadow_type,
        createdAt: row.created_at,
        expiresAt: row.expires_at,
        accessCount: row.access_count
      };
    }

    return null;
  }

  // 更新访问次数
  static async incrementAccessCount(shareId) {
    const [result] = await pool.execute(
      'UPDATE danmu_shares SET access_count = access_count + 1 WHERE share_id = ?',
      [shareId]
    );

    return result.affectedRows > 0;
  }

  // 检查分享ID是否存在
  static async exists(shareId) {
    const [rows] = await pool.execute(
      'SELECT 1 FROM danmu_shares WHERE share_id = ? LIMIT 1',
      [shareId]
    );

    return rows.length > 0;
  }

  // 清理过期分享
  static async cleanExpired() {
    const [result] = await pool.execute(
      'DELETE FROM danmu_shares WHERE expires_at IS NOT NULL AND expires_at < NOW()'
    );

    return result.affectedRows;
  }

  // 获取分享统计
  static async getStats() {
    const connection = await pool.getConnection();

    try {
      const [totalRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM danmu_shares'
      );

      const [activeRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM danmu_shares WHERE expires_at IS NULL OR expires_at > NOW()'
      );

      const [expiredRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM danmu_shares WHERE expires_at IS NOT NULL AND expires_at <= NOW()'
      );

      const [recentRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM danmu_shares WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)'
      );

      const [styleStats] = await connection.execute(
        `SELECT
          style_type,
          COUNT(*) as count
         FROM danmu_shares
         WHERE expires_at IS NULL OR expires_at > NOW()
         GROUP BY style_type
         ORDER BY count DESC`
      );

      return {
        total: totalRows[0].count,
        active: activeRows[0].count,
        expired: expiredRows[0].count,
        recent24h: recentRows[0].count,
        styleStats: styleStats
      };
    } finally {
      connection.release();
    }
  }

  // 获取热门分享（按访问次数排序）
  static async getPopular(limit = 10) {
    const [rows] = await pool.execute(
      `SELECT
        share_id, danmu_text, style_type, speed_class, size_class, font_class, running_mode,
        custom_text_color, custom_bg_color, custom_shake_level, custom_shadow_type,
        access_count, created_at
       FROM danmu_shares
       WHERE expires_at IS NULL OR expires_at > NOW()
       ORDER BY access_count DESC
       LIMIT ?`,
      [limit]
    );

    return rows.map(row => ({
      shareId: row.share_id,
      danmuText: row.danmu_text,
      styleType: row.style_type,
      speedClass: row.speed_class,
      sizeClass: row.size_class,
      fontClass: row.font_class,
      runningMode: row.running_mode,
      customTextColor: row.custom_text_color,
      customBgColor: row.custom_bg_color,
      customShakeLevel: row.custom_shake_level,
      customShadowType: row.custom_shadow_type,
      accessCount: row.access_count,
      createdAt: row.created_at
    }));
  }

  // 根据IP获取用户的分享列表
  static async getByCreatorIP(ipAddress, limit = 20) {
    const [rows] = await pool.execute(
      `SELECT
        share_id, danmu_text, style_type, speed_class, size_class, font_class, running_mode,
        custom_text_color, custom_bg_color, custom_shake_level, custom_shadow_type,
        created_at, access_count
       FROM danmu_shares
       WHERE creator_ip = ? AND (expires_at IS NULL OR expires_at > NOW())
       ORDER BY created_at DESC
       LIMIT ?`,
      [ipAddress, limit]
    );

    return rows.map(row => ({
      shareId: row.share_id,
      danmuText: row.danmu_text,
      styleType: row.style_type,
      speedClass: row.speed_class,
      sizeClass: row.size_class,
      fontClass: row.font_class,
      runningMode: row.running_mode,
      customTextColor: row.custom_text_color,
      customBgColor: row.custom_bg_color,
      customShakeLevel: row.custom_shake_level,
      customShadowType: row.custom_shadow_type,
      createdAt: row.created_at,
      accessCount: row.access_count
    }));
  }
}

module.exports = DanmuShare;
