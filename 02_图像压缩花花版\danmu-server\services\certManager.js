const fs = require('fs');
const path = require('path');

/**
 * 证书管理服务
 */
class CertManager {
  constructor() {
    this.certsPath = path.join(__dirname, '../certs');
    this.merchants = ['guofuli1700692997', 'lixiang1717867742', 'haocaihua1717453427'];
    this.configs = new Map();
    this.loadConfigs();
  }

  /**
   * 加载所有商户配置
   */
  loadConfigs() {
    this.merchants.forEach(merchantId => {
      try {
        const configPath = path.join(this.certsPath, merchantId, 'config.json');
        if (fs.existsSync(configPath)) {
          const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
          this.configs.set(merchantId, config);

          // 记录微信配置加载情况
          const wechatConfigStatus = config.wechat_config ?
            `微信配置: ${config.wechat_config.app_id}` :
            '无微信配置';

          console.log(`✅ 加载商户配置成功: ${merchantId} - ${config.description} (${wechatConfigStatus})`);
        } else {
          console.warn(`⚠️ 商户配置文件不存在: ${configPath}`);
        }
      } catch (error) {
        console.error(`❌ 加载商户配置失败: ${merchantId}`, error.message);
      }
    });
  }

  /**
   * 获取商户配置
   * @param {string} merchantId 商户ID，默认为guofuli1700692997
   * @returns {object} 商户配置
   */
  getMerchantConfig(merchantId = 'guofuli1700692997') {
    const config = this.configs.get(merchantId);
    if (!config) {
      throw new Error(`商户配置不存在: ${merchantId}`);
    }
    if (!config.isActive) {
      throw new Error(`商户未激活: ${merchantId}`);
    }
    return config;
  }

  /**
   * 获取活跃的商户列表
   * @returns {Array} 活跃商户列表
   */
  getActiveMerchants() {
    const activeMerchants = [];
    this.configs.forEach((config, merchantId) => {
      if (config.isActive) {
        activeMerchants.push({
          merchantId,
          description: config.description,
          merchantNumber: config.merchantId
        });
      }
    });
    return activeMerchants;
  }

  /**
   * 获取证书文件路径
   * @param {string} merchantId 商户ID
   * @param {string} certType 证书类型 (cert|key|p12)
   * @returns {string} 证书文件路径
   */
  getCertPath(merchantId, certType) {
    const certFiles = {
      cert: 'apiclient_cert.pem',
      key: 'apiclient_key.pem',
      p12: 'apiclient_cert.p12'
    };

    const fileName = certFiles[certType];
    if (!fileName) {
      throw new Error(`不支持的证书类型: ${certType}`);
    }

    const certPath = path.join(this.certsPath, merchantId, fileName);
    if (!fs.existsSync(certPath)) {
      throw new Error(`证书文件不存在: ${certPath}`);
    }

    return certPath;
  }

  /**
   * 读取证书文件内容
   * @param {string} merchantId 商户ID
   * @param {string} certType 证书类型
   * @returns {Buffer} 证书文件内容
   */
  readCert(merchantId, certType) {
    const certPath = this.getCertPath(merchantId, certType);
    return fs.readFileSync(certPath);
  }

  /**
   * 检查证书文件是否存在
   * @param {string} merchantId 商户ID
   * @returns {object} 证书文件存在状态
   */
  checkCertFiles(merchantId) {
    const certTypes = ['cert', 'key', 'p12'];
    const status = {};

    certTypes.forEach(certType => {
      try {
        const certPath = this.getCertPath(merchantId, certType);
        status[certType] = {
          exists: fs.existsSync(certPath),
          path: certPath
        };
      } catch (error) {
        status[certType] = {
          exists: false,
          error: error.message
        };
      }
    });

    return status;
  }

  /**
   * 验证商户配置完整性
   * @param {string} merchantId 商户ID
   * @returns {object} 验证结果
   */
  validateMerchant(merchantId) {
    const result = {
      isValid: false,
      errors: [],
      warnings: [],
      verifyMethod: 'unknown'
    };

    try {
      // 检查配置文件
      const config = this.getMerchantConfig(merchantId);
      result.verifyMethod = config.verifyMethod || 'platform_cert';

      // 检查必要字段
      const requiredFields = ['mchid', 'appid', 'api_v3_key', 'serial_no'];
      requiredFields.forEach(field => {
        if (!config[field] || config[field].includes('请填写')) {
          result.errors.push(`缺少或未配置字段: ${field}`);
        }
      });

      // 检查验签方式配置
      if (config.verifyMethod === 'public_key') {
        // 微信支付公钥验签
        if (!config.serial_no || !config.serial_no.startsWith('PUB_KEY_ID_')) {
          result.errors.push('公钥验签方式需要配置正确的公钥ID（格式：PUB_KEY_ID_...）');
        }

        // 检查微信支付公钥文件
        const publicKeyPath = path.join(this.certsPath, merchantId, config.public_key_path || 'wechatpay_public_key.pem');
        if (!fs.existsSync(publicKeyPath)) {
          result.errors.push('微信支付公钥文件不存在: ' + (config.public_key_path || 'wechatpay_public_key.pem'));
        }
      } else {
        // 平台证书验签
        if (config.serial_no && config.serial_no.startsWith('PUB_KEY_ID_')) {
          result.warnings.push('平台证书验签方式不应使用公钥ID格式的序列号');
        }
      }

      // 检查商户API证书文件
      const certStatus = this.checkCertFiles(merchantId);
      if (!certStatus.cert.exists) {
        result.errors.push('API证书文件不存在');
      }
      if (!certStatus.key.exists) {
        result.errors.push('API私钥文件不存在');
      }
      if (!certStatus.p12.exists) {
        result.warnings.push('PKCS12证书文件不存在（可选）');
      }

      result.isValid = result.errors.length === 0;

    } catch (error) {
      result.errors.push(error.message);
    }

    return result;
  }

  /**
   * 获取所有商户状态
   * @returns {object} 所有商户状态
   */
  getAllMerchantsStatus() {
    const status = {};

    this.merchants.forEach(merchantId => {
      try {
        const config = this.configs.get(merchantId);
        const validation = this.validateMerchant(merchantId);
        const certStatus = this.checkCertFiles(merchantId);

        status[merchantId] = {
          hasConfig: !!config,
          isActive: config?.isActive || false,
          description: config?.description || '未配置',
          validation: validation,
          certificates: certStatus
        };
      } catch (error) {
        status[merchantId] = {
          hasConfig: false,
          isActive: false,
          description: '配置错误',
          error: error.message
        };
      }
    });

    return status;
  }

  /**
   * 重新加载配置
   */
  reload() {
    this.configs.clear();
    this.loadConfigs();
    console.log('🔄 证书配置已重新加载');
  }
}

module.exports = new CertManager();
