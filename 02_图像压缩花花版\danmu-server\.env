# ==========================================
# 弹幕分享服务器环境配置文件
# ==========================================
# 生产环境配置

# ==========================================
# 服务器配置
# ==========================================
# 服务器运行端口
PORT=8850

# 运行环境
NODE_ENV=production

# 日志级别
LOG_LEVEL=info

# ==========================================
# 数据库配置
# ==========================================
# 数据库主机地址
DB_HOST=127.0.0.1

# 数据库端口
DB_PORT=3306

# 数据库用户名
DB_USER=danmu_share

# 数据库密码
DB_PASSWORD=B7cJhHDSfp586DYy

# 数据库名称
DB_NAME=danmu_share

# 数据库字符集
DB_CHARSET=utf8mb4

# 数据库时区
DB_TIMEZONE=+08:00

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0

# ==========================================
# 业务配置
# ==========================================
# 分享配置过期天数
CONFIG_EXPIRE_DAYS=30

# 单个配置数据最大大小（字节）
MAX_CONFIG_SIZE=10240

# 单IP每小时最大创建分享数量（防滥用）
MAX_SHARES_PER_HOUR=100

# 分享ID长度
SHARE_ID_LENGTH=8

# ==========================================
# 安全配置
# ==========================================
# CORS允许的域名
CORS_ORIGIN=*

# 请求体大小限制
BODY_LIMIT=1mb

# 是否启用请求日志
ENABLE_REQUEST_LOG=true

# 是否启用IP记录
ENABLE_IP_LOGGING=true

# ==========================================
# 缓存配置
# ==========================================
# 热门分享缓存时间（秒）
POPULAR_CACHE_TTL=300

# ==========================================
# 监控配置
# ==========================================
# 是否启用性能监控
ENABLE_MONITORING=false

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# ==========================================
# 微信小程序配置
# ==========================================
WECHAT_APP_ID=wxbd7109f36cd77b2b
WECHAT_APP_SECRET=f97e7c1a1cc722fa5168e848bd21e593

# ==========================================
# 开发调试配置
# ==========================================
# 是否启用调试模式
DEBUG=false

# 是否启用SQL查询日志
ENABLE_SQL_LOG=false

# 是否启用详细错误信息
ENABLE_DETAILED_ERRORS=false
