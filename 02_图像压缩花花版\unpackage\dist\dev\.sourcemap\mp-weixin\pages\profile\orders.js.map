{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/orders.vue?e474", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/orders.vue?9d70", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/orders.vue?e3c8", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/orders.vue?3059", "uni-app:///pages/profile/orders.vue", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/orders.vue?20e4", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/orders.vue?1827"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "navHeight", "orderList", "onReady", "windowInfo", "methods", "goBack", "uni", "loadOrderData", "loadMockData", "id", "title", "originalPrice", "actualPrice", "payMethod", "status", "createTime", "getStatusText", "viewOrderDetail", "content", "showCancel", "confirmText", "copyOrderId", "success", "icon", "duration", "gotoMemberPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC6L;AAC7L,gBAAgB,iMAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAAquB,CAAgB,otBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8EzvB;EACAC;IACA;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAC;cACA;cACA;cACA;;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA,kBACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAX;QACAI;QACAQ;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAf;QACAR;QACAwB;UACAhB;YACAI;YACAa;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAnB;QACAI;QACAa;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpLA;AAAA;AAAA;AAAA;AAAw3C,CAAgB,mxCAAG,EAAC,C;;;;;;;;;;;ACA54C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/orders.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/orders.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orders.vue?vue&type=template&id=f1ff8578&\"\nvar renderjs\nimport script from \"./orders.vue?vue&type=script&lang=js&\"\nexport * from \"./orders.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orders.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/orders.vue\"\nexport default component.exports", "export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=template&id=f1ff8578&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.orderList, function (order, __i0__) {\n          var $orig = _vm.__get_orig(order)\n          var m0 = _vm.getStatusText(order.status)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"orders-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-nav glassmorphism\">\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<view class=\"nav-content\">\n\t\t\t\t<view class=\"back-button-container\">\n\t\t\t\t\t<button class=\"back-btn\" @tap=\"goBack\">\n\t\t\t\t\t\t<text class=\"back-icon\">‹</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"nav-title\">订单查询</text>\n\t\t\t\t<view class=\"nav-right-buttons\">\n\t\t\t\t\t<!-- 保留容器用于布局平衡 -->\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 主要内容区域 -->\n\t\t<view class=\"main-content\" :style=\"{ paddingTop: navHeight + 'px' }\">\n\t\t\t<!-- 订单列表 -->\n\t\t\t<view class=\"orders-section\">\n\n\t\t\t\t<!-- 订单列表 -->\n\t\t\t\t<view class=\"order-list\" v-if=\"orderList.length > 0\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"order-item neumorphism\" \n\t\t\t\t\t\tv-for=\"order in orderList\" \n\t\t\t\t\t\t:key=\"order.id\"\n\t\t\t\t\t\t@tap=\"viewOrderDetail(order)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"order-header\">\n\t\t\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t\t\t<text class=\"order-title\">{{ order.title }}</text>\n\t\t\t\t\t\t\t\t<text class=\"order-time\">{{ order.createTime }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"order-status\" :class=\"'status-' + order.status\">\n\t\t\t\t\t\t\t\t<text class=\"status-text\">{{ getStatusText(order.status) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"order-details\">\n\t\t\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t\t\t<text class=\"detail-label\">订单号</text>\n\t\t\t\t\t\t\t\t<view class=\"detail-value\">\n\t\t\t\t\t\t\t\t\t<text class=\"order-id\">{{ order.id }}</text>\n\t\t\t\t\t\t\t\t\t<button class=\"copy-btn\" @tap.stop=\"copyOrderId(order.id)\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/copy.svg\" mode=\"aspectFit\" class=\"copy-icon\"></image>\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t\t\t<text class=\"detail-label\">支付金额</text>\n\t\t\t\t\t\t\t\t<view class=\"amount-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"current-price\">¥{{ order.actualPrice }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"original-price\" v-if=\"order.originalPrice !== order.actualPrice\">¥{{ order.originalPrice }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<view class=\"empty-state neumorphism\" v-else>\n\t\t\t\t\t<image src=\"/static/empty-orders.svg\" mode=\"aspectFit\" class=\"empty-icon\"></image>\n\t\t\t\t\t<text class=\"empty-title\">暂无订单记录</text>\n\t\t\t\t\t<text class=\"empty-desc\">您还没有购买过会员服务</text>\n\t\t\t\t\t<button class=\"goto-member-btn\" @tap=\"gotoMemberPage\">\n\t\t\t\t\t\t<text>立即开通会员</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tstatusBarHeight: 0,\n\t\t\tnavHeight: 0,\n\t\t\t// 订单列表（预留接口，后续从会员功能获取）\n\t\t\torderList: []\n\t\t}\n\t},\n\tasync onReady() {\n\t\t// 获取状态栏高度\n\t\tconst windowInfo = uni.getWindowInfo()\n\t\tthis.statusBarHeight = windowInfo.statusBarHeight\n\t\t// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）\n\t\tthis.navHeight = this.statusBarHeight + 44\n\t\t\n\t\t// 加载订单数据\n\t\tthis.loadOrderData()\n\t},\n\tmethods: {\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack()\n\t\t},\n\t\t\n\t\t// 加载订单数据（预留接口）\n\t\tloadOrderData() {\n\t\t\t// TODO: 后续从会员功能模块获取真实数据\n\t\t\t// 这里先使用模拟数据展示UI效果\n\t\t\tthis.loadMockData()\n\t\t},\n\t\t\n\t\t// 模拟数据（仅用于UI展示）\n\t\tloadMockData() {\n\t\t\tthis.orderList = [\n\t\t\t\t{\n\t\t\t\t\tid: 'M20240115001',\n\t\t\t\t\ttitle: '周卡会员',\n\t\t\t\t\toriginalPrice: 28.00,\n\t\t\t\t\tactualPrice: 19.90,\n\t\t\t\t\tpayMethod: '微信支付',\n\t\t\t\t\tstatus: 'completed',\n\t\t\t\t\tcreateTime: '2024-01-15 14:30'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 'M20240110002',\n\t\t\t\t\ttitle: '日卡会员',\n\t\t\t\t\toriginalPrice: 6.00,\n\t\t\t\t\tactualPrice: 3.90,\n\t\t\t\t\tpayMethod: '微信支付',\n\t\t\t\t\tstatus: 'completed',\n\t\t\t\t\tcreateTime: '2024-01-10 16:45'\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'completed': '已完成',\n\t\t\t\t'processing': '处理中',\n\t\t\t\t'refunded': '已退款',\n\t\t\t\t'cancelled': '已取消'\n\t\t\t}\n\t\t\treturn statusMap[status] || '未知状态'\n\t\t},\n\t\t\n\t\t// 查看订单详情\n\t\tviewOrderDetail(order) {\n\t\t\t// TODO: 实现订单详情查看功能\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '订单详情',\n\t\t\t\tcontent: `订单号：${order.id}\\n商品：${order.title}\\n金额：¥${order.actualPrice}\\n状态：${this.getStatusText(order.status)}`,\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '知道了'\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 复制订单号\n\t\tcopyOrderId(orderId) {\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: orderId,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '订单号已复制',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 跳转到会员页面（预留接口）\n\t\tgotoMemberPage() {\n\t\t\t// TODO: 后续实现跳转到会员购买页面\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '会员功能开发中...',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 2000\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n// 使用uni.scss中定义的主题色变量\n$primary-color: $uni-color-primary;\n$primary-gradient: $theme-color-primary-gradient;\n$bg-color: $uni-bg-color-grey;\n$text-primary: $uni-text-color;\n$text-secondary: $theme-text-secondary;\n$text-tertiary: $uni-text-color-grey;\n$border-color: $uni-border-color;\n$shadow-dark: $theme-shadow-dark;\n$shadow-light: $theme-shadow-light;\n\npage {\n\tbackground-color: $bg-color;\n}\n\n// 新拟物风格的混入\n@mixin neumorphism {\n\tbackground: $bg-color;\n\tbox-shadow: 12px 12px 24px $shadow-dark,\n\t\t\t\t-8px -8px 20px $shadow-light,\n\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.5),\n\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\n\tborder: 1px solid rgba(255, 255, 255, 0.8);\n}\n\n// 磨砂玻璃风格的混入\n@mixin glassmorphism {\n\tbackground: rgba($bg-color, 0.98);\n\tbackdrop-filter: blur(10px);\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.neumorphism {\n\t@include neumorphism;\n}\n\n.glassmorphism {\n\t@include glassmorphism;\n}\n\n.orders-container {\n\tpadding: 30rpx;\n\n\t.custom-nav {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 9999;\n\t\tpadding: 0 30rpx;\n\n\t\t.nav-content {\n\t\t\theight: 44px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.back-button-container {\n\t\t\t\tmin-width: 44px;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.back-btn {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\twidth: 44px;\n\t\t\t\t\theight: 44px;\n\t\t\t\t\tbackground: transparent !important;\n\t\t\t\t\tbackground-color: transparent !important;\n\t\t\t\t\tborder: none !important;\n\t\t\t\t\tborder-radius: 0 !important;\n\t\t\t\t\tbox-shadow: none !important;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\toutline: none;\n\t\t\t\t\ttransition: opacity 0.2s ease;\n\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\topacity: 0.6;\n\t\t\t\t\t\tbackground: transparent !important;\n\t\t\t\t\t\tbackground-color: transparent !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.back-icon {\n\t\t\t\t\t\tfont-size: 36px;\n\t\t\t\t\t\tcolor: #000000;\n\t\t\t\t\t\tfont-weight: normal;\n\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t\tmargin-left: -2px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.nav-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: $text-primary;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t.nav-right-buttons {\n\t\t\t\tmin-width: 44px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main-content {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tpadding-bottom: 40rpx;\n\t\tmargin-top: -40rpx;\n\n\n\n\t\t// 订单列表区域\n\t\t.orders-section {\n\t\t\tmargin: 30rpx 0;\n\n\t\t\t.order-list {\n\t\t\t\t.order-item {\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tpadding: 30rpx;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t}\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.order-header {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: flex-start;\n\t\t\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t\t\t.order-info {\n\t\t\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t\t\t.order-title {\n\t\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.order-time {\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\tcolor: $text-tertiary;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.order-status {\n\t\t\t\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\n\t\t\t\t\t\t\t&.status-completed {\n\t\t\t\t\t\t\t\tbackground: rgba(7, 193, 96, 0.1);\n\t\t\t\t\t\t\t\tcolor: #07C160;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.status-processing {\n\t\t\t\t\t\t\t\tbackground: rgba(250, 157, 59, 0.1);\n\t\t\t\t\t\t\t\tcolor: #FA9D3B;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.status-refunded {\n\t\t\t\t\t\t\t\tbackground: rgba(250, 81, 81, 0.1);\n\t\t\t\t\t\t\t\tcolor: #FA5151;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t&.status-cancelled {\n\t\t\t\t\t\t\t\tbackground: rgba(153, 153, 153, 0.1);\n\t\t\t\t\t\t\t\tcolor: #999999;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.status-text {\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.order-details {\n\t\t\t\t\t\t.detail-row {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.detail-label {\n\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\t\t\t\tmin-width: 120rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.detail-value {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\tcolor: $text-primary;\n\n\t\t\t\t\t\t\t\t.order-id {\n\t\t\t\t\t\t\t\t\tfont-family: 'Courier New', monospace;\n\t\t\t\t\t\t\t\t\tmargin-right: 12rpx;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.copy-btn {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\t\twidth: 48rpx;\n\t\t\t\t\t\t\t\t\theight: 48rpx;\n\t\t\t\t\t\t\t\t\tbackground: rgba($primary-color, 0.1);\n\t\t\t\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\t\t\t\tmargin: 0;\n\n\t\t\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\t\t\tbackground: rgba($primary-color, 0.2);\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t.copy-icon {\n\t\t\t\t\t\t\t\t\t\twidth: 24rpx;\n\t\t\t\t\t\t\t\t\t\theight: 24rpx;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.amount-info {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t\t\t\t.current-price {\n\t\t\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\t\t\t\tmargin-right: 12rpx;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.original-price {\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\tcolor: $text-tertiary;\n\t\t\t\t\t\t\t\t\ttext-decoration: line-through;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 空状态\n\t\t\t.empty-state {\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tpadding: 60rpx 30rpx;\n\t\t\t\ttext-align: center;\n\n\t\t\t\t.empty-icon {\n\t\t\t\t\twidth: 200rpx;\n\t\t\t\t\theight: 200rpx;\n\t\t\t\t\tmargin: 0 auto 30rpx;\n\t\t\t\t\topacity: 0.6;\n\t\t\t\t}\n\n\t\t\t\t.empty-title {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\n\t\t\t\t.empty-desc {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $text-tertiary;\n\t\t\t\t\tmargin-bottom: 40rpx;\n\t\t\t\t\tline-height: 1.5;\n\t\t\t\t}\n\n\t\t\t\t.goto-member-btn {\n\t\t\t\t\twidth: 300rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tbackground: linear-gradient(135deg, $primary-color, #06AD56);\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\tcolor: white;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);\n\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground: linear-gradient(135deg, #06AD56, #059C4F);\n\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orders.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752290702452\n      var cssReload = require(\"D:/1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}