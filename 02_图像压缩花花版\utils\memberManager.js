// utils/memberManager.js - 会员状态管理工具

// 存储键名
const MEMBER_INFO_KEY = 'member_info'
const MEMBER_ORDERS_KEY = 'member_orders'

// 会员类型枚举
export const MEMBER_TYPES = {
  NORMAL: 'normal',      // 普通用户
  DAY: 'day',           // 日卡会员
  PERMANENT: 'permanent' // 永久会员
}

// 获取今天的日期字符串（YYYY-MM-DD格式）
function getTodayDateString() {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取指定天数后的日期字符串
function getDateAfterDays(days) {
  const date = new Date()
  date.setDate(date.getDate() + days)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 检查日期是否已过期
function isDateExpired(dateString) {
  if (!dateString) return false
  const today = new Date(getTodayDateString())
  const expireDate = new Date(dateString)
  return today > expireDate
}

// 获取会员信息
export async function getMemberInfo() {
  try {
    // 从本地存储获取会员信息
    const memberInfo = uni.getStorageSync(MEMBER_INFO_KEY)
    
    if (!memberInfo) {
      // 如果没有会员信息，返回默认的普通用户状态
      return {
        type: MEMBER_TYPES.NORMAL,
        expireDate: null,
        isExpired: false
      }
    }
    
    // 检查会员是否过期（仅对日卡会员）
    if (memberInfo.type === MEMBER_TYPES.DAY) {
      const isExpired = isDateExpired(memberInfo.expireDate)
      if (isExpired) {
        // 如果已过期，更新为普通用户
        const expiredMemberInfo = {
          type: MEMBER_TYPES.NORMAL,
          expireDate: null,
          isExpired: true
        }
        uni.setStorageSync(MEMBER_INFO_KEY, expiredMemberInfo)
        return expiredMemberInfo
      }
    }
    
    return {
      ...memberInfo,
      isExpired: false
    }
  } catch (error) {
    console.error('获取会员信息失败:', error)
    return {
      type: MEMBER_TYPES.NORMAL,
      expireDate: null,
      isExpired: false
    }
  }
}

// 更新会员信息
export async function updateMemberInfo(memberType, days = null) {
  try {
    let memberInfo = {
      type: memberType,
      expireDate: null,
      isExpired: false
    }
    
    // 如果是日卡会员，计算到期时间
    if (memberType === MEMBER_TYPES.DAY && days) {
      memberInfo.expireDate = getDateAfterDays(days)
    }
    
    // 保存到本地存储
    uni.setStorageSync(MEMBER_INFO_KEY, memberInfo)
    
    return memberInfo
  } catch (error) {
    console.error('更新会员信息失败:', error)
    throw error
  }
}

// 检查用户是否为有效会员
export async function isValidMember() {
  const memberInfo = await getMemberInfo()
  return memberInfo.type !== MEMBER_TYPES.NORMAL && !memberInfo.isExpired
}

// 检查用户是否为永久会员
export async function isPermanentMember() {
  const memberInfo = await getMemberInfo()
  return memberInfo.type === MEMBER_TYPES.PERMANENT
}

// 检查用户是否为日卡会员
export async function isDayMember() {
  const memberInfo = await getMemberInfo()
  return memberInfo.type === MEMBER_TYPES.DAY && !memberInfo.isExpired
}

// 获取会员剩余天数（仅对日卡会员有效）
export async function getMemberRemainingDays() {
  const memberInfo = await getMemberInfo()
  
  if (memberInfo.type !== MEMBER_TYPES.DAY || !memberInfo.expireDate) {
    return 0
  }
  
  const today = new Date(getTodayDateString())
  const expireDate = new Date(memberInfo.expireDate)
  const diffTime = expireDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return Math.max(0, diffDays)
}

// 添加会员订单记录
export async function addMemberOrder(orderInfo) {
  try {
    const orders = uni.getStorageSync(MEMBER_ORDERS_KEY) || []
    
    const newOrder = {
      id: generateOrderId(),
      ...orderInfo,
      createTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      status: 'completed' // 默认为已完成状态
    }
    
    orders.unshift(newOrder) // 添加到数组开头
    uni.setStorageSync(MEMBER_ORDERS_KEY, orders)
    
    return newOrder
  } catch (error) {
    console.error('添加会员订单失败:', error)
    throw error
  }
}

// 获取会员订单列表
export async function getMemberOrders() {
  try {
    return uni.getStorageSync(MEMBER_ORDERS_KEY) || []
  } catch (error) {
    console.error('获取会员订单失败:', error)
    return []
  }
}

// 生成订单ID
function generateOrderId() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `M${year}${month}${day}${random}`
}

// 会员套餐配置（预留接口）
export const MEMBER_PACKAGES = {
  DAY_CARD: {
    type: MEMBER_TYPES.DAY,
    name: '日卡会员',
    days: 1,
    originalPrice: 6.00,
    currentPrice: 3.90,
    description: '24小时无限压缩'
  },
  PERMANENT: {
    type: MEMBER_TYPES.PERMANENT,
    name: '永久会员',
    days: null,
    originalPrice: 98.00,
    currentPrice: 68.00,
    description: '永久无限压缩'
  }
}

// 购买会员（预留接口）
export async function purchaseMember(packageType) {
  try {
    // TODO: 这里后续集成真实的支付接口
    console.log('购买会员套餐:', packageType)
    
    // 模拟支付成功后的处理
    const packageInfo = MEMBER_PACKAGES[packageType]
    if (!packageInfo) {
      throw new Error('无效的会员套餐类型')
    }
    
    // 更新会员状态
    const memberInfo = await updateMemberInfo(packageInfo.type, packageInfo.days)
    
    // 添加订单记录
    const order = await addMemberOrder({
      title: packageInfo.name,
      originalPrice: packageInfo.originalPrice,
      actualPrice: packageInfo.currentPrice,
      payMethod: '微信支付'
    })
    
    return {
      success: true,
      memberInfo,
      order
    }
  } catch (error) {
    console.error('购买会员失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 重置会员状态（用于测试）
export async function resetMemberStatus() {
  try {
    uni.removeStorageSync(MEMBER_INFO_KEY)
    uni.removeStorageSync(MEMBER_ORDERS_KEY)
    return true
  } catch (error) {
    console.error('重置会员状态失败:', error)
    return false
  }
}
