const mysql = require('mysql2/promise');
const { getConnection } = require('../config/database');
const crypto = require('crypto');

class Order {
  /**
   * 生成订单ID
   * @returns {string} 32位订单ID
   */
  static generateOrderId() {
    const timestamp = Date.now().toString();
    const random = crypto.randomBytes(8).toString('hex');
    return `${timestamp}${random}`.substring(0, 32);
  }
  
  /**
   * 创建订单
   * @param {object} orderData 订单数据
   * @returns {object} 创建的订单信息
   */
  static async createOrder(orderData) {
    const connection = await getConnection();
    
    try {
      const orderId = this.generateOrderId();
      const expireAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期
      
      const [result] = await connection.execute(
        `INSERT INTO orders (
          order_id, user_id, openid, product_type, product_name, amount, expire_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          orderId,
          orderData.userId,
          orderData.openid,
          orderData.productType,
          orderData.productName,
          orderData.amount,
          expireAt
        ]
      );
      
      // 返回创建的订单信息
      const [orders] = await connection.execute(
        'SELECT * FROM orders WHERE order_id = ?',
        [orderId]
      );
      
      return orders[0];
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取订单信息
   * @param {string} orderId 订单ID
   * @returns {object|null} 订单信息
   */
  static async getOrder(orderId) {
    const connection = await getConnection();

    try {
      const [orders] = await connection.execute(
        'SELECT * FROM orders WHERE order_id = ?',
        [orderId]
      );

      return orders.length > 0 ? orders[0] : null;

    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取用户订单列表
   * @param {number} userId 用户ID
   * @param {number} limit 限制数量
   * @param {number} offset 偏移量
   * @returns {array} 订单列表
   */
  static async getUserOrders(userId, limit = 20, offset = 0) {
    const connection = await getConnection();

    try {
      // 确保参数是整数类型，并构建SQL语句
      const limitInt = parseInt(limit);
      const offsetInt = parseInt(offset);
      const userIdInt = parseInt(userId);

      // 使用字符串拼接而不是参数绑定来处理LIMIT和OFFSET
      const sql = `SELECT
          order_id,
          product_type,
          product_name,
          amount,
          payment_status,
          created_at,
          paid_at,
          expire_at
         FROM orders
         WHERE user_id = ?
         ORDER BY created_at DESC
         LIMIT ${limitInt} OFFSET ${offsetInt}`;

      const [orders] = await connection.execute(sql, [userIdInt]);

      return orders;

    } finally {
      await connection.release();
    }
  }
  
  /**
   * 更新订单支付状态
   * @param {string} orderId 订单ID
   * @param {string} status 支付状态
   * @param {object} paymentInfo 支付信息
   * @returns {boolean} 是否更新成功
   */
  static async updatePaymentStatus(orderId, status, paymentInfo = {}) {
    const connection = await getConnection();
    
    try {
      let updateSql = 'UPDATE orders SET payment_status = ?';
      let params = [status];
      
      if (status === 'paid') {
        updateSql += ', paid_at = NOW()';
        
        if (paymentInfo.transactionId) {
          updateSql += ', transaction_id = ?';
          params.push(paymentInfo.transactionId);
        }
        
        if (paymentInfo.paymentMethod) {
          updateSql += ', payment_method = ?';
          params.push(paymentInfo.paymentMethod);
        }
      }
      
      updateSql += ' WHERE order_id = ?';
      params.push(orderId);
      
      const [result] = await connection.execute(updateSql, params);
      
      return result.affectedRows > 0;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 处理订单支付成功
   * @param {string} orderId 订单ID
   * @param {object} paymentInfo 支付信息
   * @returns {boolean} 是否处理成功
   */
  static async processPaymentSuccess(orderId, paymentInfo = {}) {
    const connection = await getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 获取订单信息
      const [orders] = await connection.execute(
        'SELECT * FROM orders WHERE order_id = ?',
        [orderId]
      );
      
      if (orders.length === 0) {
        throw new Error('订单不存在');
      }
      
      const order = orders[0];
      
      if (order.payment_status === 'paid') {
        throw new Error('订单已支付');
      }
      
      // 更新订单状态
      await this.updatePaymentStatus(orderId, 'paid', paymentInfo);
      
      // 激活用户VIP
      const User = require('./User');
      const vipType = order.product_type === 'temp_vip' ? 'temp' : 'lifetime';
      const vipResult = await User.activateVip(order.user_id, vipType);

      // 记录VIP激活结果
      console.log('💎 VIP激活结果:', vipResult);

      if (!vipResult.success) {
        // VIP激活失败（如重复购买），但订单仍标记为已支付
        console.warn('⚠️ VIP激活失败，但订单已支付:', vipResult.message);
        // 可以考虑在这里添加退款逻辑或其他处理
      }
      
      await connection.commit();
      return true;
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 清理过期订单
   * @returns {number} 清理的订单数量
   */
  static async cleanExpiredOrders() {
    const connection = await getConnection();
    
    try {
      const [result] = await connection.execute(
        `UPDATE orders 
         SET payment_status = 'failed' 
         WHERE payment_status = 'pending' 
         AND expire_at < NOW()`
      );
      
      return result.affectedRows;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取订单统计信息
   * @param {number} userId 用户ID（可选）
   * @returns {object} 统计信息
   */
  static async getOrderStats(userId = null) {
    const connection = await getConnection();
    
    try {
      let sql = `
        SELECT 
          COUNT(*) as total_orders,
          COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
          COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_orders,
          COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_orders,
          SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_revenue,
          COUNT(CASE WHEN product_type = 'temp_vip' AND payment_status = 'paid' THEN 1 END) as temp_vip_sales,
          COUNT(CASE WHEN product_type = 'lifetime_vip' AND payment_status = 'paid' THEN 1 END) as lifetime_vip_sales
        FROM orders
      `;
      
      let params = [];
      
      if (userId) {
        sql += ' WHERE user_id = ?';
        params.push(userId);
      }
      
      const [stats] = await connection.execute(sql, params);
      
      return stats[0];
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取产品配置（从数据库读取）
   * @returns {object} 产品配置信息
   */
  static async getProductConfig() {
    const connection = await getConnection();

    try {
      const [products] = await connection.execute(
        `SELECT product_type, product_name, price, description
         FROM products
         WHERE is_active = TRUE
         ORDER BY sort_order ASC`
      );

      // 转换为对象格式
      const config = {};
      products.forEach(product => {
        config[product.product_type] = {
          name: product.product_name,
          price: parseFloat(product.price),
          description: product.description
        };
      });

      return config;

    } finally {
      await connection.release();
    }
  }

  /**
   * 获取单个产品配置
   * @param {string} productType 产品类型
   * @returns {object|null} 产品配置
   */
  static async getProductByType(productType) {
    const connection = await getConnection();

    try {
      const [products] = await connection.execute(
        `SELECT product_type, product_name, price, description
         FROM products
         WHERE product_type = ? AND is_active = TRUE`,
        [productType]
      );

      if (products.length === 0) {
        return null;
      }

      const product = products[0];
      return {
        name: product.product_name,
        price: parseFloat(product.price),
        description: product.description
      };

    } finally {
      connection.release();
    }
  }

  /**
   * 更新产品价格
   * @param {string} productType 产品类型
   * @param {number} newPrice 新价格
   * @returns {boolean} 是否更新成功
   */
  static async updateProductPrice(productType, newPrice) {
    const connection = await getConnection();

    try {
      const [result] = await connection.execute(
        'UPDATE products SET price = ?, updated_at = NOW() WHERE product_type = ?',
        [newPrice, productType]
      );

      return result.affectedRows > 0;

    } finally {
      connection.release();
    }
  }
}

module.exports = Order;
