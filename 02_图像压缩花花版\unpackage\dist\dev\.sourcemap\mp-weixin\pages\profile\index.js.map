{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/index.vue?0dc9", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/index.vue?371a", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/index.vue?dfda", "uni-app:///pages/profile/index.vue", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/index.vue?8eb4", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/profile/index.vue?28d1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "memberPackageModal", "data", "statusBarHeight", "navHeight", "memberInfo", "type", "expireDate", "isExpired", "showMemberModal", "onReady", "windowInfo", "methods", "goBack", "uni", "loadMemberInfo", "console", "getMemberTypeText", "getMemberExpireText", "getMemberButtonText", "handleRenewMember", "handlePurchaseSuccess", "title", "icon", "duration", "handleMenuClick", "url", "content", "confirmText", "cancelText", "success", "showCancel", "showTestOptions", "itemList", "res"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,iMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAouB,CAAgB,mtBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgIxvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACA;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAC;cACA;cACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;gBACA;kBACAV;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAF;kBACAQ;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;QACA;QACAX;UACAY;QACA;QACA;MACA;MAEA;QACA;QACAZ;UACAQ;UACAK;UACAC;UACAC;UACAC;YACA;cACA;cACAhB;gBACAZ;gBACA4B;kBACAhB;oBACAQ;oBACAC;oBACAC;kBACA;gBACA;cACA;YACA;UACA;QACA;QACA;MACA;MAEA;QACA;QACAV;UACAQ;UACAK,oaAQA;UACAI;UACAH;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAI;MAAA;MACAlB;QACAmB;QACAH;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,eACAI;oBAAA,kCACA,yBASA,yBASA;oBAAA;kBAAA;oBAAA;oBAAA,OAhBA;kBAAA;oBAAA;oBAAA,OACA;kBAAA;oBACApB;sBACAQ;sBACAC;oBACA;oBAAA;kBAAA;oBAAA;oBAAA,OAIA;kBAAA;oBAAA;oBAAA,OACA;kBAAA;oBACAT;sBACAQ;sBACAC;oBACA;oBAAA;kBAAA;oBAAA;oBAAA,OAIA;kBAAA;oBAAA;oBAAA,OACA;kBAAA;oBACAT;sBACAQ;sBACAC;oBACA;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5UA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=14bc1b43&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=14bc1b43&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getMemberTypeText()\n  var m1 = _vm.getMemberTypeText()\n  var m2 = _vm.getMemberExpireText()\n  var m3 = _vm.getMemberButtonText()\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showMemberModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"profile-container\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-nav glassmorphism\">\r\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n\t\t\t<view class=\"nav-content\">\r\n\t\t\t\t<view class=\"back-button-container\">\r\n\t\t\t\t\t<button class=\"back-btn\" @tap=\"goBack\">\r\n\t\t\t\t\t\t<text class=\"back-icon\">‹</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"nav-title\">我的</text>\r\n\t\t\t\t<view class=\"nav-right-buttons\">\r\n\t\t\t\t\t<!-- 移除自定义按钮，使用微信官方按钮 -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要内容区域 -->\r\n\t\t<view class=\"main-content\" :style=\"{ paddingTop: navHeight + 'px' }\">\r\n\t\t\t<!-- 用户信息和会员卡片 -->\r\n\t\t\t<view class=\"user-info-card neumorphism\">\r\n\t\t\t\t<!-- 用户基本信息 -->\r\n\t\t\t\t<view class=\"user-header\">\r\n\t\t\t\t\t<view class=\"user-avatar\">\r\n\t\t\t\t\t\t<image src=\"/static/user-avatar.svg\" mode=\"aspectFit\" class=\"avatar-image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"user-details\">\r\n\t\t\t\t\t\t<view class=\"user-name\">微信用户</view>\r\n\t\t\t\t\t\t<view class=\"member-badge\">{{ getMemberTypeText() }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"member-icon\">\r\n\t\t\t\t\t\t<image src=\"/static/crown.svg\" mode=\"aspectFit\" class=\"crown-image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 会员特权信息 -->\r\n\t\t\t\t<view class=\"member-section\">\r\n\t\t\t\t\t<view class=\"member-header\">\r\n\t\t\t\t\t\t<text class=\"member-title\">{{ getMemberTypeText() }}</text>\r\n\t\t\t\t\t\t<text class=\"member-subtitle\">尊享会员特权</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 分隔线 -->\r\n\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t\t<view class=\"privileges-grid\">\r\n\t\t\t\t\t\t<view class=\"privilege-item\">\r\n\t\t\t\t\t\t\t<view class=\"check-icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"check-mark\">✓</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"privilege-text\">无限次图片压缩</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"privilege-item\">\r\n\t\t\t\t\t\t\t<view class=\"check-icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"check-mark\">✓</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"privilege-text\">批量压缩处理</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"privilege-item\">\r\n\t\t\t\t\t\t\t<view class=\"check-icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"check-mark\">✓</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"privilege-text\">高级压缩算法</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"privilege-item\">\r\n\t\t\t\t\t\t\t<view class=\"check-icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"check-mark\">✓</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"privilege-text\">专属会员服务</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 分隔线 -->\r\n\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t\t<view class=\"member-validity\">\r\n\t\t\t\t\t\t<text>会员有效期: {{ getMemberExpireText() }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\tclass=\"renew-btn\"\r\n\t\t\t\t\t\t:class=\"{ 'heartbeat-animation': memberInfo.type === 'normal' }\"\r\n\t\t\t\t\t\t@tap=\"handleRenewMember\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{{ getMemberButtonText() }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 功能菜单 -->\r\n\t\t\t<view class=\"menu-section\">\r\n\t\t\t\t<view class=\"menu-item neumorphism\" @tap=\"handleMenuClick('order')\">\r\n\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t<image src=\"/static/order.svg\" mode=\"aspectFit\" class=\"icon-image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"menu-text\">订单查询</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"menu-item neumorphism\" @tap=\"handleMenuClick('feedback')\">\r\n\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t<image src=\"/static/feedback.svg\" mode=\"aspectFit\" class=\"icon-image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"menu-text\">联系我们</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"menu-item neumorphism\" @tap=\"handleMenuClick('about')\">\r\n\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t<image src=\"/static/about.svg\" mode=\"aspectFit\" class=\"icon-image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"menu-text\">关于我们</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 测试按钮（开发阶段使用） -->\r\n\t\t\t\t<view class=\"menu-item neumorphism\" @tap=\"handleMenuClick('test')\">\r\n\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t<text style=\"font-size: 48rpx;\">🔧</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"menu-text\">测试会员功能</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 会员套餐选择弹窗 -->\r\n\t\t\t<member-package-modal\r\n\t\t\t\t:visible=\"showMemberModal\"\r\n\t\t\t\t@close=\"showMemberModal = false\"\r\n\t\t\t\t@purchase-success=\"handlePurchaseSuccess\"\r\n\t\t\t></member-package-modal>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { getMemberInfo, updateMemberInfo, resetMemberStatus } from '@/utils/memberManager.js'\r\nimport memberPackageModal from '@/components/memberPackageModal.vue'\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tmemberPackageModal\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstatusBarHeight: 0,\r\n\t\t\tnavHeight: 0,\r\n\t\t\t// 会员信息\r\n\t\t\tmemberInfo: {\r\n\t\t\t\ttype: 'normal', // normal: 普通用户, day: 日卡会员, permanent: 永久会员\r\n\t\t\t\texpireDate: null, // 到期时间，永久会员为null\r\n\t\t\t\tisExpired: false // 是否已过期\r\n\t\t\t},\r\n\t\t\t// 控制会员套餐选择弹窗显示\r\n\t\t\tshowMemberModal: false\r\n\t\t}\r\n\t},\r\n\tasync onReady() {\r\n\t\t// 获取状态栏高度\r\n\t\tconst windowInfo = uni.getWindowInfo()\r\n\t\tthis.statusBarHeight = windowInfo.statusBarHeight\r\n\t\t// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）\r\n\t\tthis.navHeight = this.statusBarHeight + 44\r\n\r\n\t\t// 加载会员信息\r\n\t\tawait this.loadMemberInfo()\r\n\t},\r\n\tmethods: {\r\n\t\t// 返回上一页\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack()\r\n\t\t},\r\n\r\n\t\t// 加载会员信息\r\n\t\tasync loadMemberInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.memberInfo = await getMemberInfo()\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载会员信息失败:', error)\r\n\t\t\t\t// 使用默认值\r\n\t\t\t\tthis.memberInfo = {\r\n\t\t\t\t\ttype: 'normal',\r\n\t\t\t\t\texpireDate: null,\r\n\t\t\t\t\tisExpired: false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 获取会员类型显示文本\r\n\t\tgetMemberTypeText() {\r\n\t\t\tswitch (this.memberInfo.type) {\r\n\t\t\t\tcase 'day':\r\n\t\t\t\t\treturn '日卡会员'\r\n\t\t\t\tcase 'permanent':\r\n\t\t\t\t\treturn '永久会员'\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn '普通用户'\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 获取会员到期时间显示文本\r\n\t\tgetMemberExpireText() {\r\n\t\t\tif (this.memberInfo.type === 'permanent') {\r\n\t\t\t\treturn '永久有效'\r\n\t\t\t} else if (this.memberInfo.type === 'day' && this.memberInfo.expireDate) {\r\n\t\t\t\treturn this.memberInfo.expireDate\r\n\t\t\t} else {\r\n\t\t\t\treturn '未开通会员'\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 获取会员按钮文本\r\n\t\tgetMemberButtonText() {\r\n\t\t\t// 根据用户会员状态返回不同的按钮文本\r\n\t\t\tif (this.memberInfo.type === 'normal') {\r\n\t\t\t\treturn '开通会员'\r\n\t\t\t} else {\r\n\t\t\t\treturn '续费会员'\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理续费会员按钮点击\r\n\t\thandleRenewMember() {\r\n\t\t\tthis.showMemberModal = true\r\n\t\t},\r\n\r\n\t\t// 处理购买成功\r\n\t\tasync handlePurchaseSuccess(data) {\r\n\t\t\tconsole.log('购买成功:', data)\r\n\r\n\t\t\t// 重新加载会员信息\r\n\t\t\tawait this.loadMemberInfo()\r\n\r\n\t\t\t// 显示成功提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '会员开通成功',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 2000\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 处理菜单点击\r\n\t\thandleMenuClick(type) {\r\n\t\t\tif (type === 'order') {\r\n\t\t\t\t// 跳转到订单查询页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/profile/orders'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tif (type === 'feedback') {\r\n\t\t\t\t// 联系我们 - 询问是否复制客服微信号\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '客服微信',\r\n\t\t\t\t\tcontent: '客服微信号：gbw6646\\n是否复制到剪贴板？',\r\n\t\t\t\t\tconfirmText: '复制',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 用户点击复制\r\n\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\tdata: 'gbw6646',\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已复制到剪贴板',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tif (type === 'about') {\r\n\t\t\t\t// 关于我们 - 显示应用信息\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '关于我们',\r\n\t\t\t\t\tcontent: `图像压缩花花版 v1.0\r\n\r\n🌸 让图片压缩变得简单高效\r\n\r\n📱 支持多种压缩方式\r\n💾 智能优化图片大小\r\n✨ 为您节省珍贵的存储空间\r\n\r\n谢谢使用，祝您生活愉快！`,\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tif (type === 'test') {\r\n\t\t\t\t// 测试会员功能\r\n\t\t\t\tthis.showTestOptions()\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 显示测试选项\r\n\t\tshowTestOptions() {\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['重置会员状态', '模拟购买日卡', '模拟购买永久会员'],\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tswitch (res.tapIndex) {\r\n\t\t\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t\t\t// 重置会员状态\r\n\t\t\t\t\t\t\t\tawait resetMemberStatus()\r\n\t\t\t\t\t\t\t\tawait this.loadMemberInfo()\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '已重置为普通用户',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\t\t\t// 模拟购买日卡\r\n\t\t\t\t\t\t\t\tawait updateMemberInfo('day', 1)\r\n\t\t\t\t\t\t\t\tawait this.loadMemberInfo()\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '已开通日卡会员',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\t\t\t// 模拟购买永久会员\r\n\t\t\t\t\t\t\t\tawait updateMemberInfo('permanent')\r\n\t\t\t\t\t\t\t\tawait this.loadMemberInfo()\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '已开通永久会员',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n// 使用uni.scss中定义的主题色变量\r\n$primary-color: $uni-color-primary;\r\n$primary-gradient: $theme-color-primary-gradient;\r\n$bg-color: $uni-bg-color-grey;\r\n$text-primary: $uni-text-color;\r\n$text-secondary: $theme-text-secondary;\r\n$text-tertiary: $uni-text-color-grey;\r\n$border-color: $uni-border-color;\r\n$shadow-dark: $theme-shadow-dark;\r\n$shadow-light: $theme-shadow-light;\r\n\r\npage {\r\n\tbackground-color: $bg-color;\r\n}\r\n\r\n// 新拟物风格的混入\r\n@mixin neumorphism {\r\n\tbackground: $bg-color;\r\n\tbox-shadow: 12px 12px 24px $shadow-dark,\r\n\t\t\t\t-8px -8px 20px $shadow-light,\r\n\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.5),\r\n\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n// 磨砂玻璃风格的混入\r\n@mixin glassmorphism {\r\n\tbackground: rgba($bg-color, 0.98);\r\n\tbackdrop-filter: blur(10px);\r\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.neumorphism {\r\n\t@include neumorphism;\r\n}\r\n\r\n.glassmorphism {\r\n\t@include glassmorphism;\r\n}\r\n\r\n.profile-container {\r\n\tpadding: 30rpx;\r\n\r\n\t.custom-nav {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 9999;\r\n\t\tpadding: 0 30rpx;\r\n\r\n\t\t.nav-content {\r\n\t\t\theight: 44px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t.back-button-container {\r\n\t\t\t\tmin-width: 44px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.back-btn {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\twidth: 44px;\r\n\t\t\t\t\theight: 44px;\r\n\t\t\t\t\tbackground: transparent !important;\r\n\t\t\t\t\tbackground-color: transparent !important;\r\n\t\t\t\t\tborder: none !important;\r\n\t\t\t\t\tborder-radius: 0 !important;\r\n\t\t\t\t\tbox-shadow: none !important;\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\toutline: none;\r\n\t\t\t\t\ttransition: opacity 0.2s ease;\r\n\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tdisplay: none !important;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\topacity: 0.6;\r\n\t\t\t\t\t\tbackground: transparent !important;\r\n\t\t\t\t\t\tbackground-color: transparent !important;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.back-icon {\r\n\t\t\t\t\t\tfont-size: 36px;\r\n\t\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\t\tfont-weight: normal;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\tmargin-left: -2px; // 微调位置，使箭头更居中\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nav-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: $text-primary;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\r\n\t\t\t.nav-right-buttons {\r\n\t\t\t\t/* 保留容器用于布局平衡 */\r\n\t\t\t\tmin-width: 44px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.main-content {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tpadding-bottom: 40rpx;\r\n\t\tmargin-top: -40rpx;\r\n\r\n\t\t.user-info-card {\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: 20rpx 0;\r\n\t\t\tcolor: #333333;\r\n\r\n\t\t\t.user-header {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\t\t.user-avatar {\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\tborder-radius: 60rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\r\n\t\t\t\t\t.avatar-image {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-details {\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t.user-name {\r\n\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\tcolor: $text-primary;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.member-badge {\r\n\t\t\t\t\t\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tpadding: 8rpx 16rpx;\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.member-icon {\r\n\t\t\t\t\t.crown-image {\r\n\t\t\t\t\t\twidth: 90rpx;\r\n\t\t\t\t\t\theight: 90rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.member-section {\r\n\t\t\t\tborder-top: 1px solid #f0f0f0;\r\n\t\t\t\tpadding-top: 30rpx;\r\n\r\n\t\t\t\t.member-header {\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t\t.member-title {\r\n\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.member-subtitle {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.divider-line {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 1px;\r\n\t\t\t\t\tborder-top: 1px solid #f0f0f0;\r\n\t\t\t\t\tmargin: 20rpx 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.privileges-grid {\r\n\t\t\t\t\tdisplay: grid;\r\n\t\t\t\t\tgrid-template-columns: 1fr 1fr;\r\n\t\t\t\t\tgap: 20rpx;\r\n\t\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-items: center;\r\n\t\t\t\t\tpadding: 0 20rpx;\r\n\r\n\t\t\t\t\t.privilege-item {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tpadding-left: 60rpx;\r\n\r\n\t\t\t\t\t\t.check-icon {\r\n\t\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t\t\t\tbackground: linear-gradient(135deg, #07C160, #06AD56);\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\tflex-shrink: 0;\r\n\r\n\t\t\t\t\t\t\t.check-mark {\r\n\t\t\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.privilege-text {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.member-validity {\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.renew-btn {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #07C160, #06AD56);\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);\r\n\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground: linear-gradient(135deg, #06AD56, #059C4F);\r\n\t\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* 呼吸心跳动效 - 仅对普通用户显示 */\r\n\t\t\t\t\t&.heartbeat-animation {\r\n\t\t\t\t\t\tanimation: heartbeat 2s ease-in-out infinite;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.menu-section {\r\n\t\t\tmargin: 30rpx 0;\r\n\r\n\t\t\t.menu-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.menu-icon {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\r\n\t\t\t\t\t.icon-image {\r\n\t\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.menu-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: $text-primary;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t}\r\n}\r\n\r\n/* 呼吸心跳动画关键帧 */\r\n@keyframes heartbeat {\r\n\t0% {\r\n\t\ttransform: scale(1);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);\r\n\t}\r\n\t14% {\r\n\t\ttransform: scale(1.05);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.4);\r\n\t}\r\n\t28% {\r\n\t\ttransform: scale(1);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);\r\n\t}\r\n\t42% {\r\n\t\ttransform: scale(1.05);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.4);\r\n\t}\r\n\t70% {\r\n\t\ttransform: scale(1);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752292123682\n      var cssReload = require(\"D:/1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}