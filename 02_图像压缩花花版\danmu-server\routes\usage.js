const express = require('express');
const router = express.Router();
const { formatSuccess, formatError, getClientIP } = require('../utils/helpers');
const { getConnection } = require('../config/database');

/**
 * 记录使用日志
 * POST /api/usage/log
 */
router.post('/log', async (req, res) => {
  try {
    const { actionType, timestamp, userId, openid } = req.body;
    
    if (!actionType) {
      return res.status(400).json(formatError(
        '缺少操作类型',
        'MISSING_ACTION_TYPE'
      ));
    }
    
    console.log(`📝 记录使用日志:`, {
      actionType,
      userId,
      openid,
      timestamp
    });
    
    const connection = await getConnection();
    
    try {
      // 插入使用记录
      await connection.execute(
        `INSERT INTO usage_logs (user_id, openid, action_type, is_free, ip_address, user_agent, created_at)
         VALUES (?, ?, ?, TRUE, ?, ?, ?)`,
        [
          userId || null,
          openid || null,
          actionType,
          getClientIP(req),
          req.get('User-Agent') || '',
          timestamp ? new Date(timestamp) : new Date()
        ]
      );
      
      console.log(`✅ 使用日志记录成功: ${actionType}`);
      
      res.json(formatSuccess(null, '使用日志记录成功'));
      
    } finally {
      await connection.release();
    }
    
  } catch (error) {
    console.error('记录使用日志失败:', error);
    res.status(500).json(formatError(
      '记录使用日志失败',
      'LOG_USAGE_FAILED',
      error.message
    ));
  }
});

module.exports = router;
