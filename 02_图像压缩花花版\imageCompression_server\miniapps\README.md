# 图像压缩小程序配置管理

## 📁 文件夹结构

```
miniapps/
├── README.md                       # 说明文档
├── imagecomp_huahua/              # 图像压缩花花版
│   ├── config.json                # 小程序配置信息
│   ├── apiclient_key.pem          # 商户API私钥（必需）
│   └── pub_key.pem                # 微信支付公钥（必需）
├── compress_assistant_huahua/     # 压缩图片助手花花版
│   ├── config.json
│   ├── apiclient_key.pem
│   └── pub_key.pem
└── imagecomp_wangwang/            # 图像压缩图片汪汪版
    ├── config.json
    ├── apiclient_key.pem
    └── pub_key.pem
```

## 🔧 配置说明

### 配置文件 (config.json) - 公钥验签版
```json
{
  "mchid": "请填写您的商户号",
  "appid": "wx_imagecomp_huahua_001",
  "api_v3_key": "请填写您的API_V3密钥32位字符",
  "public_key_id": "请填写您的公钥ID",
  "private_key_path": "/www/wwwroot/imageCompression_server/miniapps/imagecomp_huahua/apiclient_key.pem",
  "public_key_path": "/www/wwwroot/imageCompression_server/miniapps/imagecomp_huahua/pub_key.pem",
  "description": "图像压缩花花版小程序",
  "isActive": true,
  "notifyUrl": "https://your-domain.com/api/payment/notify",
  "supportedAppIds": [
    "wx_imagecomp_huahua_001"
  ],
  "businessName": "图像压缩花花版",
  "wechat_config": {
    "app_id": "wx_imagecomp_huahua_001",
    "app_secret": "请填写您的AppSecret"
  }
}
```

#### 参数说明

**基础信息**
- **appid**: 小程序AppID（必需，唯一标识）
- **businessName**: 业务显示名称（必需）
- **description**: 小程序描述
- **isActive**: 是否启用该小程序配置

**支付配置（公钥验签）**
- **mchid**: 微信商户号（必需）
- **api_v3_key**: 微信支付API V3密钥（必需）
- **public_key_id**: 微信支付公钥ID（必需）

**证书路径**
- **private_key_path**: 商户API私钥文件绝对路径（必需）
- **public_key_path**: 微信支付公钥文件绝对路径（必需）

**微信小程序配置**
- **wechat_config.app_id**: 微信小程序AppID
- **wechat_config.app_secret**: 微信小程序AppSecret

**其他配置**
- **notifyUrl**: 支付回调通知URL
- **supportedAppIds**: 支持的AppID列表（用于兼容性）

**商品配置**
- 商品价格现在在数据库 `products` 表中管理
- 每个小程序可以设置不同的价格
- 支持运营人员动态调价

**系统设置**
- **notifyUrl**: 支付回调通知URL
- **returnUrl**: 支付完成返回URL

## 📋 配置步骤

### 1. 创建小程序目录
```bash
mkdir miniapps/your_app_name
```

### 2. 创建配置文件
复制模板配置文件并修改相应参数：
```bash
cp miniapps/imagecomp_huahua/config.json miniapps/your_app_name/config.json
```

### 3. 配置微信支付（公钥验签）
配置微信支付功能：
1. 从微信商户平台下载商户API私钥 `apiclient_key.pem`
2. 从微信商户平台获取微信支付公钥 `pub_key.pem`
3. 将证书文件放入对应小程序目录
4. 配置公钥ID和文件路径

### 4. 设置商品价格
在数据库 `products` 表中为该小程序添加商品记录：
```sql
INSERT INTO products (app_id, product_code, product_name, description, price, duration_hours, sort_order) VALUES
('your_app_id', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('your_app_id', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2);
```

### 5. 激活小程序
将 `isActive` 设置为 `true`

## 🚀 使用方式

### 自动商户匹配
系统会根据小程序的AppID自动匹配对应的商户配置：

```javascript
// 前端传入AppID
const appId = "wx123456789abcdef";

// 后端自动匹配商户
const merchantConfig = MerchantManager.getMerchantByAppId(appId);
```

### 手动指定商户
```javascript
const merchantConfig = MerchantManager.getMerchantById("merchant_001");
```

## 🔒 安全注意事项

1. **文件权限**
   ```bash
   chmod 600 *.pem
   chmod 644 config.json
   ```

2. **敏感信息**
   - AppSecret、API密钥等敏感信息不应提交到版本控制
   - 建议使用环境变量或加密存储

3. **证书管理**
   - 定期检查证书有效期
   - 及时更新过期证书

## 📊 多商户支持

### 支持场景
- 5套不同的图像压缩小程序
- 每套小程序对应不同的商户号
- 不同商户可以设置不同的会员价格
- 支持相同商户号对应多个小程序

### 当前配置的小程序
```
miniapps/
├── imagecomp_huahua/           # 图像压缩花花版
├── compress_assistant_huahua/  # 压缩图片助手花花版
└── imagecomp_wangwang/         # 图像压缩图片汪汪版
```

### AppID 分配
- **图像压缩花花版**: `wx_imagecomp_huahua_001`
- **压缩图片助手花花版**: `wx_compress_assistant_huahua_002`
- **图像压缩图片汪汪版**: `wx_imagecomp_wangwang_003`

## 🛠️ 管理工具

### 配置验证
```bash
node scripts/validateMerchants.js
```

### 商户列表
```bash
node scripts/listMerchants.js
```

### 配置测试
```bash
node scripts/testMerchantConfig.js merchant_001
```

## 📞 技术支持

配置问题请联系：
- 项目技术负责人
- 微信支付技术支持（支付相关）

## 🔄 配置更新

修改配置后需要重启服务：
```bash
pm2 restart image-compression-server
```

或者使用热重载（如果支持）：
```bash
pm2 reload image-compression-server
```
