# 图像压缩小程序配置管理

## 📁 文件夹结构

```
miniapps/
├── README.md                       # 说明文档
├── imagecomp_huahua/              # 图像压缩花花版
│   ├── config.json                # 小程序配置信息
│   ├── apiclient_cert.pem         # 微信支付API证书（可选）
│   └── apiclient_key.pem          # 微信支付API私钥（可选）
├── imagecomp_meimei/              # 图像压缩美美版
│   ├── config.json
│   ├── apiclient_cert.pem
│   └── apiclient_key.pem
├── imagecomp_liangliang/          # 图像压缩亮亮版
│   ├── config.json
│   ├── apiclient_cert.pem
│   └── apiclient_key.pem
├── imagecomp_xixi/                # 图像压缩西西版
│   ├── config.json
│   ├── apiclient_cert.pem
│   └── apiclient_key.pem
└── imagecomp_dongdong/            # 图像压缩东东版
    ├── config.json
    ├── apiclient_cert.pem
    └── apiclient_key.pem
```

## 🔧 配置说明

### 配置文件 (config.json)
```json
{
  "appId": "wx123456789abcdef",
  "appName": "imagecomp_huahua",
  "businessName": "图像压缩花花版",
  "description": "图像压缩小程序花花版",
  "isActive": true,
  "merchant": {
    "merchantId": "1234567890",
    "merchantName": "某某科技有限公司"
  },
  "wechat": {
    "appId": "wx123456789abcdef",
    "appSecret": "your_app_secret_here",
    "mchId": "1234567890",
    "apiKey": "your_api_key_32_characters_here",
    "apiV3Key": "your_api_v3_key_32_characters",
    "serialNo": "certificate_serial_number",
    "certPath": "./apiclient_cert.pem",
    "keyPath": "./apiclient_key.pem"
  },
  "products": {
    "dayCard": {
      "name": "日卡会员",
      "price": 4.99,
      "durationHours": 24,
      "description": "24小时内无限制压缩图片"
    },
    "permanent": {
      "name": "永久会员",
      "price": 19.99,
      "durationHours": null,
      "description": "永久无限制压缩图片"
    }
  },
  "settings": {
    "notifyUrl": "https://your-domain.com/api/payment/notify",
    "returnUrl": "https://your-domain.com/api/payment/return"
  }
}
```

#### 参数说明

**基础信息**
- **appId**: 小程序AppID（必需，唯一标识）
- **appName**: 小程序应用名称（必需，文件夹名称）
- **businessName**: 业务显示名称（必需）
- **description**: 小程序描述
- **isActive**: 是否启用该小程序配置

**商户信息**
- **merchant.merchantId**: 微信商户号（必需）
- **merchant.merchantName**: 商户公司名称

**微信配置**
- **appId**: 微信小程序AppID
- **appSecret**: 微信小程序AppSecret
- **mchId**: 微信商户号
- **apiKey**: 微信支付API密钥（V2）
- **apiV3Key**: 微信支付API V3密钥
- **serialNo**: 商户API证书序列号
- **certPath**: 商户API证书文件路径（相对路径）
- **keyPath**: 商户API私钥文件路径（相对路径）

**商品配置**
- **dayCard**: 日卡会员配置
- **permanent**: 永久会员配置
- 每个小程序可以设置不同的价格

**系统设置**
- **notifyUrl**: 支付回调通知URL
- **returnUrl**: 支付完成返回URL

## 📋 配置步骤

### 1. 创建小程序目录
```bash
mkdir miniapps/your_app_name
```

### 2. 创建配置文件
复制模板配置文件并修改相应参数：
```bash
cp miniapps/imagecomp_huahua/config.json miniapps/your_app_name/config.json
```

### 3. 配置微信支付（可选）
如果需要微信支付功能：
1. 从微信商户平台下载API证书
2. 将证书文件放入小程序目录
3. 配置证书路径和序列号

### 4. 设置商品价格
根据不同小程序的定价策略，在 `products` 中设置价格。

### 5. 激活小程序
将 `isActive` 设置为 `true`

## 🚀 使用方式

### 自动商户匹配
系统会根据小程序的AppID自动匹配对应的商户配置：

```javascript
// 前端传入AppID
const appId = "wx123456789abcdef";

// 后端自动匹配商户
const merchantConfig = MerchantManager.getMerchantByAppId(appId);
```

### 手动指定商户
```javascript
const merchantConfig = MerchantManager.getMerchantById("merchant_001");
```

## 🔒 安全注意事项

1. **文件权限**
   ```bash
   chmod 600 *.pem
   chmod 644 config.json
   ```

2. **敏感信息**
   - AppSecret、API密钥等敏感信息不应提交到版本控制
   - 建议使用环境变量或加密存储

3. **证书管理**
   - 定期检查证书有效期
   - 及时更新过期证书

## 📊 多商户支持

### 支持场景
- 5套不同的图像压缩小程序
- 每套小程序对应不同的商户号
- 不同商户可以设置不同的会员价格
- 支持相同商户号对应多个小程序

### 配置示例
```
merchants/
├── imagecomp_a/     # 图像压缩A版 - 商户1
├── imagecomp_b/     # 图像压缩B版 - 商户2  
├── imagecomp_c/     # 图像压缩C版 - 商户1（共用）
├── imagecomp_d/     # 图像压缩D版 - 商户3
└── imagecomp_e/     # 图像压缩E版 - 商户3（共用）
```

## 🛠️ 管理工具

### 配置验证
```bash
node scripts/validateMerchants.js
```

### 商户列表
```bash
node scripts/listMerchants.js
```

### 配置测试
```bash
node scripts/testMerchantConfig.js merchant_001
```

## 📞 技术支持

配置问题请联系：
- 项目技术负责人
- 微信支付技术支持（支付相关）

## 🔄 配置更新

修改配置后需要重启服务：
```bash
pm2 restart image-compression-server
```

或者使用热重载（如果支持）：
```bash
pm2 reload image-compression-server
```
