const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const AppManager = require('../services/MerchantManager');

/**
 * 根据AppID获取商户信息
 */
router.get('/info/:appId', async (req, res) => {
  try {
    const { appId } = req.params;

    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'AppID不能为空'
      });
    }

    // 使用小程序管理器获取小程序信息
    const appConfig = AppManager.getAppByAppId(appId);

    if (!appConfig) {
      return res.status(404).json({
        success: false,
        error: '未找到对应的小程序配置'
      });
    }

    res.json({
      success: true,
      data: {
        appid: appConfig.appid,
        appName: appConfig.appName,
        businessName: appConfig.businessName,
        description: appConfig.description,
        mchid: appConfig.mchid,
        isActive: appConfig.isActive,
        hasWechatConfig: !!(appConfig.wechat_config?.app_secret && !appConfig.wechat_config.app_secret.includes('请填写')),
        wechatAppId: appConfig.wechat_config?.app_id,
        loadedAt: appConfig.loadedAt
      }
    });

  } catch (error) {
    console.error('获取商户信息失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取商户统计信息
 */
router.get('/stats/:merchantId', async (req, res) => {
  try {
    const { merchantId } = req.params;

    // 获取用户统计
    const userStats = await query(
      `SELECT 
         COUNT(DISTINCT u.user_id) as total_users,
         COUNT(DISTINCT CASE WHEN u.last_login_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.user_id END) as active_users_7d,
         COUNT(DISTINCT CASE WHEN u.last_login_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN u.user_id END) as active_users_30d
       FROM users u
       JOIN merchants m ON u.app_id = m.app_id
       WHERE m.merchant_id = ?`,
      [merchantId]
    );

    // 获取订单统计
    const orderStats = await query(
      `SELECT 
         COUNT(*) as total_orders,
         COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
         SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_revenue,
         COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as orders_7d,
         COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as orders_30d
       FROM orders WHERE merchant_id = ?`,
      [merchantId]
    );

    // 获取会员统计
    const membershipStats = await query(
      `SELECT 
         COUNT(*) as total_memberships,
         COUNT(CASE WHEN status = 'active' THEN 1 END) as active_memberships,
         COUNT(CASE WHEN membership_type = 'day_card' THEN 1 END) as day_card_count,
         COUNT(CASE WHEN membership_type = 'permanent' THEN 1 END) as permanent_count
       FROM memberships m
       JOIN users u ON m.user_id = u.user_id
       JOIN merchants mer ON u.app_id = mer.app_id
       WHERE mer.merchant_id = ?`,
      [merchantId]
    );

    // 获取商品统计
    const productStats = await query(
      'SELECT COUNT(*) as total_products, COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_products FROM products WHERE merchant_id = ?',
      [merchantId]
    );

    res.json({
      success: true,
      data: {
        merchantId,
        users: userStats[0],
        orders: {
          ...orderStats[0],
          total_revenue: parseFloat(orderStats[0].total_revenue || 0)
        },
        memberships: membershipStats[0],
        products: productStats[0]
      }
    });

  } catch (error) {
    console.error('获取商户统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取所有商户列表
 */
router.get('/list', async (req, res) => {
  try {
    const { page = 1, limit = 10, isActive } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = '';
    let queryParams = [];

    if (isActive !== undefined) {
      whereClause = 'WHERE is_active = ?';
      queryParams.push(isActive === 'true');
    }

    // 获取商户列表
    const merchants = await query(
      `SELECT merchant_id, app_id, business_name, is_active, created_at 
       FROM merchants 
       ${whereClause}
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    // 获取总数
    const totalResult = await query(
      `SELECT COUNT(*) as total FROM merchants ${whereClause}`,
      queryParams
    );

    res.json({
      success: true,
      data: {
        merchants: merchants.map(m => ({
          merchantId: m.merchant_id,
          appId: m.app_id,
          businessName: m.business_name,
          isActive: m.is_active,
          createdAt: m.created_at
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          totalPages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取商户列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 检查AppID是否支持
 */
router.get('/check/:appId', async (req, res) => {
  try {
    const { appId } = req.params;

    const appConfig = AppManager.getAppByAppId(appId);
    const isSupported = !!appConfig;
    const isActive = isSupported ? appConfig.isActive : false;

    res.json({
      success: true,
      data: {
        appid: appId,
        isSupported,
        isActive,
        mchid: isSupported ? appConfig.mchid : null,
        businessName: isSupported ? appConfig.businessName : null
      }
    });

  } catch (error) {
    console.error('检查AppID失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取商户配置统计
 */
router.get('/config/stats', async (req, res) => {
  try {
    const stats = AppManager.getStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取商户统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 重新加载商户配置
 */
router.post('/config/reload', async (req, res) => {
  try {
    AppManager.reload();

    res.json({
      success: true,
      message: '小程序配置重新加载成功',
      data: AppManager.getStats()
    });

  } catch (error) {
    console.error('重新加载商户配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 验证商户配置
 */
router.get('/config/validate/:merchantId', async (req, res) => {
  try {
    const { merchantId } = req.params;
    const validation = AppManager.validateApp(merchantId);

    res.json({
      success: true,
      data: {
        merchantId,
        ...validation
      }
    });

  } catch (error) {
    console.error('验证商户配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取商户的商品配置
 */
router.get('/:merchantId/products', async (req, res) => {
  try {
    const { merchantId } = req.params;

    const products = await query(
      `SELECT * FROM products 
       WHERE merchant_id = ? 
       ORDER BY sort_order ASC, created_at DESC`,
      [merchantId]
    );

    res.json({
      success: true,
      data: {
        merchantId,
        products: products.map(p => ({
          productId: p.product_id,
          productCode: p.product_code,
          productName: p.product_name,
          description: p.description,
          price: parseFloat(p.price),
          durationHours: p.duration_hours,
          isActive: p.is_active,
          sortOrder: p.sort_order,
          createdAt: p.created_at
        }))
      }
    });

  } catch (error) {
    console.error('获取商户商品失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取系统概览统计
 */
router.get('/system/overview', async (req, res) => {
  try {
    // 获取总体统计
    const totalStats = await query(`
      SELECT 
        (SELECT COUNT(*) FROM merchants WHERE is_active = TRUE) as active_merchants,
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM orders WHERE payment_status = 'paid') as total_paid_orders,
        (SELECT SUM(amount) FROM orders WHERE payment_status = 'paid') as total_revenue,
        (SELECT COUNT(*) FROM memberships WHERE status = 'active') as active_memberships
    `);

    // 获取今日统计
    const todayStats = await query(`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as new_users_today,
        (SELECT COUNT(*) FROM orders WHERE DATE(created_at) = CURDATE()) as orders_today,
        (SELECT COUNT(*) FROM orders WHERE DATE(paid_at) = CURDATE() AND payment_status = 'paid') as paid_orders_today,
        (SELECT SUM(amount) FROM orders WHERE DATE(paid_at) = CURDATE() AND payment_status = 'paid') as revenue_today
    `);

    res.json({
      success: true,
      data: {
        total: {
          ...totalStats[0],
          total_revenue: parseFloat(totalStats[0].total_revenue || 0)
        },
        today: {
          ...todayStats[0],
          revenue_today: parseFloat(todayStats[0].revenue_today || 0)
        }
      }
    });

  } catch (error) {
    console.error('获取系统概览失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
