const axios = require('axios');

// 测试订单API
async function testOrdersAPI() {
  const baseURL = 'http://localhost:8850';
  
  console.log('🧪 开始测试订单API...\n');
  
  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ 健康检查成功:', healthResponse.data.status);
    
    // 2. 测试获取产品配置
    console.log('\n2. 测试获取产品配置...');
    const productsResponse = await axios.get(`${baseURL}/api/order/products`);
    console.log('✅ 产品配置:', JSON.stringify(productsResponse.data, null, 2));
    
    // 3. 测试获取用户订单（使用一个测试用户ID）
    console.log('\n3. 测试获取用户订单...');
    const testUserId = 1; // 假设用户ID为1
    
    try {
      const ordersResponse = await axios.get(`${baseURL}/api/order/user/${testUserId}`);
      console.log('✅ 用户订单:', JSON.stringify(ordersResponse.data, null, 2));
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('ℹ️  用户暂无订单记录');
      } else {
        throw error;
      }
    }
    
    // 4. 测试创建订单
    console.log('\n4. 测试创建订单...');
    const orderData = {
      userId: testUserId,
      openid: 'test_openid_123',
      productType: 'temp_vip'
    };
    
    try {
      const createResponse = await axios.post(`${baseURL}/api/order/create`, orderData);
      console.log('✅ 创建订单成功:', JSON.stringify(createResponse.data, null, 2));
      
      // 5. 测试获取订单详情
      if (createResponse.data.success && createResponse.data.data.orderId) {
        console.log('\n5. 测试获取订单详情...');
        const orderId = createResponse.data.data.orderId;
        const orderDetailResponse = await axios.get(`${baseURL}/api/order/${orderId}`);
        console.log('✅ 订单详情:', JSON.stringify(orderDetailResponse.data, null, 2));
        
        // 6. 再次获取用户订单列表，应该能看到新创建的订单
        console.log('\n6. 再次获取用户订单列表...');
        const updatedOrdersResponse = await axios.get(`${baseURL}/api/order/user/${testUserId}`);
        console.log('✅ 更新后的用户订单:', JSON.stringify(updatedOrdersResponse.data, null, 2));
      }
    } catch (error) {
      console.log('⚠️  创建订单失败:', error.response?.data || error.message);
    }
    
    console.log('\n🎉 订单API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testOrdersAPI();
}

module.exports = testOrdersAPI;
