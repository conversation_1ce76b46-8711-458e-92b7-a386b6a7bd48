const CertManager = require('./certManager');

/**
 * 智能商户匹配服务
 * 根据前端小程序AppID自动匹配对应的商户配置
 */
class MerchantMatcher {
  constructor() {
    this.appIdToMerchantMap = new Map();
    this.loadMerchantMappings();
  }

  /**
   * 加载商户映射关系
   */
  loadMerchantMappings() {
    console.log('🔄 正在加载商户映射关系...');
    
    const merchants = ['guofuli1700692997', 'haocaihua1717453427', 'lixiang1717867742'];
    
    merchants.forEach(merchantId => {
      try {
        const config = CertManager.configs.get(merchantId);
        if (config && config.supportedAppIds) {
          config.supportedAppIds.forEach(appId => {
            if (appId && !appId.includes('请填写')) {
              this.appIdToMerchantMap.set(appId, {
                merchantId,
                config,
                businessName: config.businessName || merchantId
              });
              console.log(`✅ 映射关系: ${appId} -> ${merchantId} (${config.businessName})`);
            }
          });
        }
      } catch (error) {
        console.warn(`⚠️ 加载商户 ${merchantId} 映射失败:`, error.message);
      }
    });

    console.log(`📊 总共加载了 ${this.appIdToMerchantMap.size} 个AppID映射关系`);
  }

  /**
   * 根据AppID获取对应的商户ID
   * @param {string} appId 小程序AppID
   * @returns {string|null} 商户ID
   */
  getMerchantIdByAppId(appId) {
    if (!appId) {
      console.warn('⚠️ AppID为空，使用默认商户');
      return this.getDefaultMerchant();
    }

    const mapping = this.appIdToMerchantMap.get(appId);
    if (mapping) {
      console.log(`🎯 AppID ${appId} 匹配到商户: ${mapping.merchantId} (${mapping.businessName})`);
      return mapping.merchantId;
    }

    console.warn(`⚠️ 未找到AppID ${appId} 对应的商户，使用默认商户`);
    return this.getDefaultMerchant();
  }

  /**
   * 获取默认商户（第一个激活的商户）
   * @returns {string} 默认商户ID
   */
  getDefaultMerchant() {
    // 优先返回激活的商户
    for (const [appId, mapping] of this.appIdToMerchantMap) {
      if (mapping.config.isActive) {
        console.log(`🔧 使用默认激活商户: ${mapping.merchantId} (${mapping.businessName})`);
        return mapping.merchantId;
      }
    }

    // 如果没有激活的商户，返回第一个
    const firstMapping = this.appIdToMerchantMap.values().next().value;
    if (firstMapping) {
      console.log(`🔧 使用第一个可用商户: ${firstMapping.merchantId} (${firstMapping.businessName})`);
      return firstMapping.merchantId;
    }

    // 最后的兜底方案
    console.warn('⚠️ 没有可用的商户配置，使用硬编码默认值');
    return 'guofuli1700692997';
  }

  /**
   * 根据AppID获取商户配置
   * @param {string} appId 小程序AppID
   * @returns {object} 商户配置
   */
  getMerchantConfigByAppId(appId) {
    const merchantId = this.getMerchantIdByAppId(appId);
    return CertManager.getMerchantConfig(merchantId);
  }

  /**
   * 获取所有支持的AppID列表
   * @returns {Array} AppID列表
   */
  getSupportedAppIds() {
    return Array.from(this.appIdToMerchantMap.keys());
  }

  /**
   * 获取商户映射统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    const stats = {
      totalMappings: this.appIdToMerchantMap.size,
      activeMerchants: 0,
      inactiveMerchants: 0,
      merchants: []
    };

    const merchantStats = new Map();

    for (const [appId, mapping] of this.appIdToMerchantMap) {
      if (!merchantStats.has(mapping.merchantId)) {
        merchantStats.set(mapping.merchantId, {
          merchantId: mapping.merchantId,
          businessName: mapping.businessName,
          isActive: mapping.config.isActive,
          appIds: [],
          description: mapping.config.description
        });
      }
      merchantStats.get(mapping.merchantId).appIds.push(appId);
    }

    for (const merchantInfo of merchantStats.values()) {
      if (merchantInfo.isActive) {
        stats.activeMerchants++;
      } else {
        stats.inactiveMerchants++;
      }
      stats.merchants.push(merchantInfo);
    }

    return stats;
  }

  /**
   * 重新加载映射关系
   */
  reload() {
    this.appIdToMerchantMap.clear();
    CertManager.reload();
    this.loadMerchantMappings();
    console.log('🔄 商户映射关系已重新加载');
  }

  /**
   * 验证AppID是否支持
   * @param {string} appId 小程序AppID
   * @returns {boolean} 是否支持
   */
  isAppIdSupported(appId) {
    return this.appIdToMerchantMap.has(appId);
  }

  /**
   * 根据商户ID获取支持的AppID列表
   * @param {string} merchantId 商户ID
   * @returns {Array} AppID列表
   */
  getAppIdsByMerchant(merchantId) {
    const appIds = [];
    for (const [appId, mapping] of this.appIdToMerchantMap) {
      if (mapping.merchantId === merchantId) {
        appIds.push(appId);
      }
    }
    return appIds;
  }

  /**
   * 添加新的AppID映射
   * @param {string} appId 小程序AppID
   * @param {string} merchantId 商户ID
   * @returns {boolean} 是否成功
   */
  addAppIdMapping(appId, merchantId) {
    try {
      const config = CertManager.getMerchantConfig(merchantId);
      this.appIdToMerchantMap.set(appId, {
        merchantId,
        config,
        businessName: config.businessName || merchantId
      });
      console.log(`✅ 添加映射: ${appId} -> ${merchantId}`);
      return true;
    } catch (error) {
      console.error(`❌ 添加映射失败: ${appId} -> ${merchantId}`, error.message);
      return false;
    }
  }

  /**
   * 移除AppID映射
   * @param {string} appId 小程序AppID
   * @returns {boolean} 是否成功
   */
  removeAppIdMapping(appId) {
    const existed = this.appIdToMerchantMap.has(appId);
    this.appIdToMerchantMap.delete(appId);
    if (existed) {
      console.log(`🗑️ 移除映射: ${appId}`);
    }
    return existed;
  }
}

module.exports = new MerchantMatcher();
