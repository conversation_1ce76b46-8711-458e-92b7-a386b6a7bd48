const cron = require('node-cron');
const { getConnection } = require('../config/database');
const Order = require('../models/Order');
const UsageLog = require('../models/UsageLog');
const DanmuShare = require('../models/DanmuShare');

class Scheduler {
  /**
   * 启动所有定时任务
   */
  static start() {
    console.log('🕐 启动定时任务调度器...');
    
    // 每日0点重置用户全屏次数
    this.scheduleResetDailyCount();
    
    // 每小时清理过期订单
    this.scheduleCleanExpiredOrders();
    
    // 每日清理过期分享
    this.scheduleCleanExpiredShares();
    
    // 每周清理旧的使用日志
    this.scheduleCleanOldLogs();
    
    // 每小时更新VIP状态
    this.scheduleUpdateVipStatus();
    
    console.log('✅ 定时任务调度器启动完成');
  }
  
  /**
   * 每日0点重置用户全屏次数
   */
  static scheduleResetDailyCount() {
    // 每天0点执行
    cron.schedule('0 0 * * *', async () => {
      try {
        console.log('🔄 开始重置每日全屏次数...');
        
        const connection = await getConnection();
        const [result] = await connection.execute(
          `UPDATE users 
           SET daily_fullscreen_count = 3, last_fullscreen_reset = CURDATE() 
           WHERE last_fullscreen_reset < CURDATE()`
        );
        await connection.end();
        
        console.log(`✅ 重置完成，影响用户数: ${result.affectedRows}`);
        
      } catch (error) {
        console.error('❌ 重置每日全屏次数失败:', error);
      }
    }, {
      timezone: 'Asia/Shanghai'
    });
    
    console.log('📅 已设置每日全屏次数重置任务 (每天0点)');
  }
  
  /**
   * 每小时清理过期订单
   */
  static scheduleCleanExpiredOrders() {
    // 每小时执行
    cron.schedule('0 * * * *', async () => {
      try {
        console.log('🧹 开始清理过期订单...');
        
        const cleanedCount = await Order.cleanExpiredOrders();
        
        if (cleanedCount > 0) {
          console.log(`✅ 清理过期订单完成，清理数量: ${cleanedCount}`);
        }
        
      } catch (error) {
        console.error('❌ 清理过期订单失败:', error);
      }
    });
    
    console.log('📅 已设置过期订单清理任务 (每小时)');
  }
  
  /**
   * 每日清理过期分享
   */
  static scheduleCleanExpiredShares() {
    // 每天凌晨2点执行
    cron.schedule('0 2 * * *', async () => {
      try {
        console.log('🧹 开始清理过期分享...');
        
        const cleanedCount = await DanmuShare.cleanExpired();
        
        if (cleanedCount > 0) {
          console.log(`✅ 清理过期分享完成，清理数量: ${cleanedCount}`);
        }
        
      } catch (error) {
        console.error('❌ 清理过期分享失败:', error);
      }
    }, {
      timezone: 'Asia/Shanghai'
    });
    
    console.log('📅 已设置过期分享清理任务 (每天2点)');
  }
  
  /**
   * 每周清理旧的使用日志
   */
  static scheduleCleanOldLogs() {
    // 每周日凌晨3点执行
    cron.schedule('0 3 * * 0', async () => {
      try {
        console.log('🧹 开始清理旧的使用日志...');
        
        const cleanedCount = await UsageLog.cleanOldLogs(90); // 保留90天
        
        if (cleanedCount > 0) {
          console.log(`✅ 清理旧日志完成，清理数量: ${cleanedCount}`);
        }
        
      } catch (error) {
        console.error('❌ 清理旧日志失败:', error);
      }
    }, {
      timezone: 'Asia/Shanghai'
    });
    
    console.log('📅 已设置旧日志清理任务 (每周日3点)');
  }
  
  /**
   * 每小时更新VIP状态
   */
  static scheduleUpdateVipStatus() {
    // 每小时执行
    cron.schedule('30 * * * *', async () => {
      try {
        console.log('🔄 开始更新VIP状态...');

        const connection = await getConnection();

        // 清理过期的会员记录（使用新的user_memberships表）
        const [result] = await connection.execute(
          `UPDATE user_memberships
           SET status = 'expired', updated_at = NOW()
           WHERE status = 'active'
           AND expire_date IS NOT NULL
           AND expire_date < NOW()`
        );

        await connection.release();

        if (result.affectedRows > 0) {
          console.log(`✅ VIP状态更新完成，过期会员数: ${result.affectedRows}`);
        } else {
          console.log('✅ VIP状态检查完成，无过期会员');
        }

      } catch (error) {
        console.error('❌ 更新VIP状态失败:', error);
      }
    });

    console.log('📅 已设置VIP状态更新任务 (每小时30分)');
  }
  
  /**
   * 手动执行所有清理任务
   */
  static async runAllCleanupTasks() {
    console.log('🚀 开始执行所有清理任务...');
    
    try {
      // 重置每日全屏次数
      const connection = await getConnection();
      const [resetResult] = await connection.execute(
        `UPDATE users 
         SET daily_fullscreen_count = 3, last_fullscreen_reset = CURDATE() 
         WHERE last_fullscreen_reset < CURDATE()`
      );
      await connection.release();
      console.log(`✅ 重置每日全屏次数完成，影响用户数: ${resetResult.affectedRows}`);
      
      // 清理过期订单
      const expiredOrders = await Order.cleanExpiredOrders();
      console.log(`✅ 清理过期订单完成，清理数量: ${expiredOrders}`);
      
      // 清理过期分享
      const expiredShares = await DanmuShare.cleanExpired();
      console.log(`✅ 清理过期分享完成，清理数量: ${expiredShares}`);
      
      // 清理旧日志
      const oldLogs = await UsageLog.cleanOldLogs(90);
      console.log(`✅ 清理旧日志完成，清理数量: ${oldLogs}`);
      
      console.log('🎉 所有清理任务执行完成');
      
      return {
        resetUsers: resetResult.affectedRows,
        expiredOrders: expiredOrders,
        expiredShares: expiredShares,
        oldLogs: oldLogs
      };
      
    } catch (error) {
      console.error('❌ 执行清理任务失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取定时任务状态
   */
  static getStatus() {
    return {
      isRunning: true,
      tasks: [
        {
          name: '重置每日全屏次数',
          schedule: '每天0点',
          description: '重置所有用户的每日免费全屏使用次数'
        },
        {
          name: '清理过期订单',
          schedule: '每小时',
          description: '将超过30分钟未支付的订单标记为失败'
        },
        {
          name: '清理过期分享',
          schedule: '每天2点',
          description: '删除过期的弹幕分享配置'
        },
        {
          name: '清理旧日志',
          schedule: '每周日3点',
          description: '删除90天前的使用日志记录'
        },
        {
          name: '更新VIP状态',
          schedule: '每小时30分',
          description: '检查并更新过期的VIP会员状态'
        }
      ]
    };
  }
}

module.exports = Scheduler;
