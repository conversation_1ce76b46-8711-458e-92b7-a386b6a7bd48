const https = require('https');

/**
 * 微信内容安全检测服务
 */
class ContentSecurityService {
  constructor() {
    // 微信API基础URL
    this.baseUrl = 'https://api.weixin.qq.com';
  }

  /**
   * 检测文本内容安全
   * @param {string} content 要检测的文本内容
   * @param {string} openid 用户openid
   * @param {string} accessToken 微信access_token
   * @returns {Promise<object>} 检测结果
   */
  async checkText(content, openid, accessToken) {
    try {
      console.log('🔍 开始文本安全检测:', { 
        contentLength: content.length, 
        openid: openid ? openid.substring(0, 8) + '***' : 'null' 
      });

      // 构建请求数据
      const requestData = {
        content: content,
        version: 2,
        scene: 4, // 社交日志场景，适合弹幕应用
        openid: openid
      };

      // 调用微信安全检测API
      const result = await this.callWechatSecurityAPI(accessToken, requestData);
      
      console.log('✅ 文本安全检测完成:', {
        suggest: result.result?.suggest,
        label: result.result?.label
      });

      return {
        safe: result.result?.suggest === 'pass',
        suggest: result.result?.suggest,
        label: result.result?.label,
        trace_id: result.trace_id
      };

    } catch (error) {
      console.error('❌ 文本安全检测失败:', error);
      
      // API异常时默认允许通过，避免影响用户体验
      return {
        safe: true,
        error: error.message
      };
    }
  }

  /**
   * 调用微信安全检测API
   * @param {string} accessToken 访问令牌
   * @param {object} data 请求数据
   * @returns {Promise<object>} API响应
   */
  async callWechatSecurityAPI(accessToken, data) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(data);
      const url = `${this.baseUrl}/wxa/msg_sec_check?access_token=${accessToken}`;
      
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      console.log('📤 调用微信安全检测API:', url.replace(accessToken, '***'));

      const req = https.request(url, options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const result = JSON.parse(responseData);
            
            if (result.errcode === 0) {
              console.log('✅ 微信安全检测API调用成功');
              resolve(result);
            } else {
              console.error('❌ 微信安全检测API返回错误:', result);
              reject(new Error(`微信API错误: ${result.errcode} - ${result.errmsg}`));
            }
          } catch (error) {
            console.error('❌ 解析微信API响应失败:', error);
            reject(new Error('解析API响应失败'));
          }
        });
      });

      req.on('error', (error) => {
        console.error('❌ 请求微信安全检测API失败:', error);
        reject(new Error('网络请求失败'));
      });

      req.write(postData);
      req.end();
    });
  }
}

module.exports = ContentSecurityService;
