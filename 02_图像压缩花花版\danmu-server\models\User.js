const mysql = require('mysql2/promise');
const { getConnection } = require('../config/database');

class User {
  /**
   * 根据openid获取或创建用户
   * @param {string} openid 微信openid
   * @returns {object} 用户信息
   */
  static async getOrCreateUser(openid) {
    const connection = await getConnection();
    
    try {
      // 先尝试获取用户
      const [users] = await connection.execute(
        'SELECT * FROM users WHERE openid = ?',
        [openid]
      );
      
      if (users.length > 0) {
        // 用户存在，更新最后登录时间
        await connection.execute(
          'UPDATE users SET last_login_at = NOW() WHERE openid = ?',
          [openid]
        );
        
        return users[0];
      }
      
      // 用户不存在，创建新用户（确保正确设置默认值）
      const [result] = await connection.execute(
        `INSERT INTO users (openid, last_login_at, free_share_count, daily_fullscreen_count, last_fullscreen_reset)
         VALUES (?, NOW(), 3, 3, CURDATE())`,
        [openid]
      );
      
      // 返回新创建的用户信息
      const [newUser] = await connection.execute(
        'SELECT * FROM users WHERE user_id = ?',
        [result.insertId]
      );
      
      return newUser[0];
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 检查用户是否有VIP权限（通过会员表查询）
   * @param {number} userId 用户ID
   * @returns {object} VIP状态信息
   */
  static async checkVipStatus(userId) {
    const connection = await getConnection();

    try {
      // 查询用户的有效会员记录
      const [memberships] = await connection.execute(
        `SELECT
          membership_type,
          expire_date,
          status
         FROM user_memberships
         WHERE user_id = ?
           AND status = 'active'
           AND (expire_date IS NULL OR expire_date > NOW())
         ORDER BY
           CASE WHEN expire_date IS NULL THEN 1 ELSE 0 END DESC,
           expire_date DESC
         LIMIT 1`,
        [userId]
      );

      if (memberships.length === 0) {
        return {
          hasVipAccess: false,
          membershipType: null,
          expireDate: null
        };
      }

      const membership = memberships[0];
      return {
        hasVipAccess: true,
        membershipType: membership.membership_type,
        expireDate: membership.expire_date,
        isLifetime: membership.expire_date === null
      };

    } finally {
      await connection.release();
    }
  }
  
  /**
   * 检查并重置每日全屏次数
   * @param {number} userId 用户ID
   * @param {object} connection 数据库连接（可选，如果不传则创建新连接）
   * @returns {object} 重置结果
   */
  static async checkAndResetDailyCount(userId, connection = null) {
    const shouldReleaseConnection = !connection;
    if (!connection) {
      connection = await getConnection();
    }

    try {
      // 检查是否需要重置
      const [users] = await connection.execute(
        'SELECT last_fullscreen_reset, daily_fullscreen_count FROM users WHERE user_id = ?',
        [userId]
      );
      
      if (users.length === 0) {
        throw new Error('用户不存在');
      }
      
      const user = users[0];
      const today = new Date().toISOString().split('T')[0];
      const lastReset = user.last_fullscreen_reset ?
        user.last_fullscreen_reset.toISOString().split('T')[0] : null;

      // 简化日志，因为现在主要使用本地计算
      console.log(`🔍 服务器端检查用户 ${userId} 重置:`, {
        today,
        lastReset,
        needReset: lastReset !== today
      });

      if (lastReset !== today) {
        // 需要重置（服务器端备份逻辑，主要使用本地计算）
        await connection.execute(
          `UPDATE users
           SET daily_fullscreen_count = 3, last_fullscreen_reset = CURDATE()
           WHERE user_id = ?`,
          [userId]
        );

        return { reset: true, dailyCount: 3 };
      }

      return { reset: false, dailyCount: user.daily_fullscreen_count };

    } finally {
      if (shouldReleaseConnection) {
        await connection.release();
      }
    }
  }
  
  /**
   * 使用分享次数
   * @param {number} userId 用户ID
   * @returns {boolean} 是否成功使用
   */
  static async useShareCount(userId) {
    const connection = await getConnection();
    
    try {
      // 检查VIP状态
      const vipStatus = await this.checkVipStatus(userId);
      if (vipStatus.hasVipAccess) {
        // VIP用户无限制，直接记录使用
        await connection.execute(
          'UPDATE users SET total_share_count = total_share_count + 1 WHERE user_id = ?',
          [userId]
        );
        return true;
      }
      
      // 非VIP用户检查免费次数
      const [result] = await connection.execute(
        `UPDATE users 
         SET free_share_count = free_share_count - 1, total_share_count = total_share_count + 1 
         WHERE user_id = ? AND free_share_count > 0`,
        [userId]
      );
      
      return result.affectedRows > 0;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 使用全屏次数
   * @param {number} userId 用户ID
   * @returns {boolean} 是否成功使用
   */
  static async useFullscreenCount(userId) {
    const connection = await getConnection();
    
    try {
      // 先检查并重置每日次数（使用同一个连接）
      await this.checkAndResetDailyCount(userId, connection);
      
      // 检查VIP状态
      const vipStatus = await this.checkVipStatus(userId);
      if (vipStatus.hasVipAccess) {
        // VIP用户无限制，直接记录使用
        await connection.execute(
          'UPDATE users SET total_fullscreen_count = total_fullscreen_count + 1 WHERE user_id = ?',
          [userId]
        );
        return true;
      }
      
      // 非VIP用户检查每日免费次数（服务器端备份逻辑）
      const [result] = await connection.execute(
        `UPDATE users
         SET daily_fullscreen_count = daily_fullscreen_count - 1, total_fullscreen_count = total_fullscreen_count + 1
         WHERE user_id = ? AND daily_fullscreen_count > 0`,
        [userId]
      );

      return result.affectedRows > 0;
      
    } finally {
      await connection.release();
    }
  }

  /**
   * 根据openid获取用户信息
   * @param {string} openid 微信openid
   * @returns {object|null} 用户信息
   */
  static async getByOpenid(openid) {
    const connection = await getConnection();

    try {
      const [users] = await connection.execute(
        'SELECT * FROM users WHERE openid = ?',
        [openid]
      );

      return users.length > 0 ? users[0] : null;
    } finally {
      await connection.release();
    }
  }

  /**
   * 获取用户额度信息
   * @param {number} userId 用户ID
   * @returns {object} 用户额度信息
   */
  static async getUserQuota(userId) {
    const connection = await getConnection();

    try {
      // 获取用户基础信息
      const [users] = await connection.execute(
        `SELECT
          free_share_count,
          daily_fullscreen_count,
          total_share_count,
          total_fullscreen_count
         FROM users WHERE user_id = ?`,
        [userId]
      );

      if (users.length === 0) {
        throw new Error('用户不存在');
      }

      // 检查VIP状态（通过user_memberships表）
      const [memberships] = await connection.execute(
        `SELECT membership_type, expire_date, status
         FROM user_memberships
         WHERE user_id = ? AND status = 'active'
         AND (expire_date IS NULL OR expire_date > NOW())
         ORDER BY created_at DESC LIMIT 1`,
        [userId]
      );

      // 默认非VIP状态
      let hasVipAccess = false;
      let membershipType = null;
      let expireDate = null;

      if (memberships.length > 0) {
        const membership = memberships[0];
        hasVipAccess = true;
        membershipType = membership.membership_type;
        expireDate = membership.expire_date;
      }

      const userData = users[0];

      // 返回驼峰命名格式，兼容前端
      return {
        // 原始下划线命名（保持兼容性）
        ...userData,
        is_vip: hasVipAccess && membershipType === 'lifetime_vip',
        vip_expire_time: membershipType === 'lifetime_vip' ? null : (membershipType === 'temp_vip' ? null : expireDate),
        temp_vip_expire_time: membershipType === 'temp_vip' ? expireDate : null,
        has_vip_access: hasVipAccess,

        // 驼峰命名格式（前端期望）
        freeShareCount: userData.free_share_count,
        dailyFullscreenCount: userData.daily_fullscreen_count,
        totalShareCount: userData.total_share_count,
        totalFullscreenCount: userData.total_fullscreen_count,
        hasVipAccess: hasVipAccess,
        isVip: hasVipAccess && membershipType === 'lifetime_vip',
        vipExpireTime: membershipType === 'lifetime_vip' ? null : (membershipType === 'temp_vip' ? null : expireDate),
        tempVipExpireTime: membershipType === 'temp_vip' ? expireDate : null
      };

    } finally {
      await connection.release();
    }
  }
  
  /**
   * 激活VIP会员（支持时间累加）
   * @param {number} userId 用户ID
   * @param {string} type 会员类型：'temp' 或 'lifetime'
   * @returns {object} 激活结果
   */
  static async activateVip(userId, type) {
    const connection = await getConnection();

    try {
      await connection.beginTransaction();

      // 1. 检查用户当前的会员状态
      const [existingMemberships] = await connection.execute(
        `SELECT membership_type, expire_date, status
         FROM user_memberships
         WHERE user_id = ? AND status = 'active'
         AND (expire_date IS NULL OR expire_date > NOW())
         ORDER BY
           CASE WHEN expire_date IS NULL THEN 1 ELSE 0 END DESC,
           expire_date DESC
         LIMIT 1`,
        [userId]
      );

      const existingMembership = existingMemberships.length > 0 ? existingMemberships[0] : null;

      let membershipType, expireDate, action;

      if (type === 'temp') {
        membershipType = 'temp_vip';

        if (existingMembership) {
          if (existingMembership.membership_type === 'lifetime_vip') {
            // 已是终身会员，继续购买日卡 - 允许支付但保持终身状态
            action = 'already_lifetime_buy_temp';
            membershipType = 'lifetime_vip'; // 保持终身会员状态
            expireDate = null;
            console.log(`✅ 终身会员购买日卡: ${userId} - 支付成功但保持终身状态`);
            // 不需要创建新记录，保持现有终身状态
          } else if (existingMembership.membership_type === 'temp_vip') {
            // 已有日卡，累加24小时
            const currentExpire = new Date(existingMembership.expire_date);
            const newExpire = new Date(currentExpire.getTime() + 24 * 60 * 60 * 1000);

            // 更新现有记录的过期时间
            await connection.execute(
              `UPDATE user_memberships
               SET expire_date = ?, updated_at = NOW()
               WHERE user_id = ? AND status = 'active' AND membership_type = 'temp_vip'`,
              [newExpire, userId]
            );

            expireDate = newExpire;
            action = 'extended';
            console.log(`✅ 日卡时间累加: ${userId}, 新过期时间: ${newExpire}`);
          }
        } else {
          // 新用户，创建24小时日卡
          expireDate = new Date(Date.now() + 24 * 60 * 60 * 1000);

          await connection.execute(
            `INSERT INTO user_memberships (user_id, membership_type, expire_date, status)
             VALUES (?, ?, ?, 'active')`,
            [userId, membershipType, expireDate]
          );

          action = 'created';
          console.log(`✅ 创建新日卡: ${userId}, 过期时间: ${expireDate}`);
        }

      } else if (type === 'lifetime') {
        membershipType = 'lifetime_vip';
        expireDate = null;

        if (existingMembership) {
          if (existingMembership.membership_type === 'lifetime_vip') {
            // 已是终身会员，重复购买终身卡 - 允许购买但不改变状态
            action = 'already_lifetime';
            console.log(`✅ 终身会员重复购买终身卡: ${userId} - 支付成功但状态不变`);
            // 不需要创建新记录，保持现有状态
          } else {
            // 有日卡，升级为终身会员（将现有日卡设为过期）
            await connection.execute(
              `UPDATE user_memberships
               SET status = 'upgraded', updated_at = NOW()
               WHERE user_id = ? AND status = 'active' AND membership_type = 'temp_vip'`,
              [userId]
            );

            action = 'upgraded';
            console.log(`✅ 日卡升级为终身会员: ${userId}`);

            // 创建终身会员记录
            await connection.execute(
              `INSERT INTO user_memberships (user_id, membership_type, expire_date, status)
               VALUES (?, ?, ?, 'active')`,
              [userId, membershipType, expireDate]
            );
          }
        } else {
          action = 'created';
          console.log(`✅ 创建新终身会员: ${userId}`);

          // 创建终身会员记录
          await connection.execute(
            `INSERT INTO user_memberships (user_id, membership_type, expire_date, status)
             VALUES (?, ?, ?, 'active')`,
            [userId, membershipType, expireDate]
          );
        }

      } else {
        throw new Error('无效的VIP类型');
      }

      await connection.commit();

      return {
        success: true,
        action: action,
        membershipType: membershipType,
        expireDate: expireDate,
        message: this.getActivationMessage(action, membershipType, expireDate)
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.release();
    }
  }

  /**
   * 获取激活成功的消息
   */
  static getActivationMessage(action, membershipType, expireDate) {
    if (membershipType === 'lifetime_vip') {
      if (action === 'upgraded') {
        return '已升级为终身会员';
      } else if (action === 'already_lifetime') {
        return '支付成功，您已是终身会员';
      } else if (action === 'already_lifetime_buy_temp') {
        return '支付成功，您已是终身会员（无需日卡）';
      } else {
        return '终身会员激活成功';
      }
    } else {
      if (action === 'extended') {
        const expireStr = expireDate.toLocaleString('zh-CN');
        return `日卡时间已累加，有效期至：${expireStr}`;
      } else {
        return '24小时会员激活成功';
      }
    }
  }
}

module.exports = User;
