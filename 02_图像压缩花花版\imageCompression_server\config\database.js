const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'image_compression_service',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 20,
  queueLimit: 0,
  idleTimeout: 60000,         // 空闲60秒后释放连接
  acquireTimeout: 60000       // 获取连接超时时间60秒
});

// 测试数据库连接
const testConnection = async () => {
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection();
    
    // 执行简单查询测试连接
    await connection.execute('SELECT 1');
    
    console.log('数据库连接测试成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      host: dbConfig.host,
      database: dbConfig.database,
      user: dbConfig.user
    });
    throw error;
  }
};

// 初始化数据库表
const initDatabase = async () => {
  try {
    const connection = await pool.getConnection();

    console.log('正在检查数据库表...');
    
    // 检查关键表是否存在
    const tables = ['users', 'merchants', 'products', 'memberships', 'orders'];

    for (const tableName of tables) {
      const [rows] = await connection.execute(
        `SELECT COUNT(*) as count FROM information_schema.tables
         WHERE table_schema = DATABASE() AND table_name = ?`,
        [tableName]
      );

      if (rows[0].count === 0) {
        console.warn(`⚠️  表 ${tableName} 不存在，请执行 database.sql 文件`);
      } else {
        console.log(`✅ 表 ${tableName} 存在`);
      }
    }

    console.log('数据库表检查完成');
    connection.release();
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    throw error;
  }
};

// 获取数据库连接的便捷方法
const getConnection = async () => {
  return await pool.getConnection();
};

// 执行查询的便捷方法
const query = async (sql, params = []) => {
  const connection = await pool.getConnection();
  try {
    const [rows] = await connection.execute(sql, params);
    return rows;
  } finally {
    connection.release();
  }
};

// 执行事务的便捷方法
const transaction = async (callback) => {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

module.exports = {
  pool,
  getConnection,
  testConnection,
  initDatabase,
  query,
  transaction
};
