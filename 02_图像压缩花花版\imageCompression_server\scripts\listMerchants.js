#!/usr/bin/env node

/**
 * 小程序配置列表工具
 * 显示所有已配置的小程序信息
 */

const path = require('path');
// 加载环境变量
require('dotenv').config();
const AppManager = require('../services/MerchantManager');

function displayApps() {
  console.log('📋 图像压缩服务器 - 小程序配置列表');
  console.log('================================\n');

  const stats = AppManager.getStats();
  
  console.log('📊 统计信息:');
  console.log(`   总小程序数: ${stats.totalApps}`);
  console.log(`   激活小程序: ${stats.activeApps}`);
  console.log('');

  if (stats.apps.length === 0) {
    console.log('⚠️  没有找到任何小程序配置');
    console.log('请在 miniapps/ 目录下添加小程序配置文件');
    return;
  }

  console.log('📱 小程序列表:');
  console.log('');

  stats.apps.forEach((app, index) => {
    const status = app.isActive ? '🟢 激活' : '🔴 停用';
    const wechatStatus = app.hasWechatConfig ? '✅ 已配置' : '⚠️  未配置';

    console.log(`${index + 1}. ${app.businessName}`);
    console.log(`   小程序名: ${app.appName}`);
    console.log(`   AppID: ${app.appId}`);
    console.log(`   商户号: ${app.merchantId || '未配置'}`);
    console.log(`   状态: ${status}`);
    console.log(`   微信配置: ${wechatStatus}`);
    console.log('');
  });

  console.log('');
  console.log('💡 提示:');
  console.log('   - 使用 node scripts/validateMerchants.js 验证配置');
  console.log('   - 使用 npm run miniapps:list 查看配置列表');
}

function main() {
  try {
    displayApps();
  } catch (error) {
    console.error('❌ 获取小程序列表失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { displayApps };
