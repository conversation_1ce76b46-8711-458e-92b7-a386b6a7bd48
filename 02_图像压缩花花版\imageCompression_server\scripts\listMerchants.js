#!/usr/bin/env node

/**
 * 商户配置列表工具
 * 显示所有已配置的商户信息
 */

const path = require('path');
const MerchantManager = require('../services/MerchantManager');

function displayMerchants() {
  console.log('📋 图像压缩服务器 - 商户配置列表');
  console.log('================================\n');

  const stats = MerchantManager.getStats();
  
  console.log('📊 统计信息:');
  console.log(`   总商户数: ${stats.totalMerchants}`);
  console.log(`   激活商户: ${stats.activeMerchants}`);
  console.log(`   AppID映射: ${stats.appIdMappings}`);
  console.log('');

  if (stats.merchants.length === 0) {
    console.log('⚠️  没有找到任何商户配置');
    console.log('请在 merchants/ 目录下添加商户配置文件');
    return;
  }

  console.log('🏪 商户列表:');
  console.log('');

  stats.merchants.forEach((merchant, index) => {
    const status = merchant.isActive ? '🟢 激活' : '🔴 停用';
    const wechatStatus = merchant.hasWechatConfig ? '✅ 已配置' : '⚠️  未配置';
    
    console.log(`${index + 1}. ${merchant.businessName}`);
    console.log(`   商户ID: ${merchant.merchantId}`);
    console.log(`   AppID: ${merchant.appId}`);
    console.log(`   状态: ${status}`);
    console.log(`   微信配置: ${wechatStatus}`);
    console.log('');
  });

  // 显示AppID映射关系
  console.log('🔗 AppID映射关系:');
  const allMerchants = MerchantManager.getAllMerchants();
  allMerchants.forEach(config => {
    if (config.appId && config.appId !== 'wx_default_app_id') {
      console.log(`   ${config.appId} → ${config.merchantId} (${config.businessName})`);
    }
  });

  console.log('');
  console.log('💡 提示:');
  console.log('   - 使用 node scripts/validateMerchants.js 验证配置');
  console.log('   - 使用 node scripts/testMerchantConfig.js <merchantId> 测试配置');
}

function main() {
  try {
    displayMerchants();
  } catch (error) {
    console.error('❌ 获取商户列表失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { displayMerchants };
