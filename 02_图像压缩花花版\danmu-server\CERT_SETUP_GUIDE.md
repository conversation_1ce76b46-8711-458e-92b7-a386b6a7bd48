# 微信支付证书配置快速指南

## 🎯 概述

本指南帮助您快速配置微信支付证书，支持3个商户：
- **guofuli1700692997** (主商户，已激活)
- **lixiang1717867742** (备用商户，未激活)
- **haocaihua1717453427** (备用商户，未激活)

## 📋 配置步骤

### 1. 准备证书文件

从微信商户平台下载以下文件：
- `apiclient_cert.pem` - API证书（公钥）
- `apiclient_key.pem` - API私钥
- `apiclient_cert.p12` - PKCS12证书（可选）

### 2. 放置证书文件

```bash
# 主商户 guofuli1700692997
cp apiclient_cert.pem danmu-server/certs/guofuli1700692997/
cp apiclient_key.pem danmu-server/certs/guofuli1700692997/
cp apiclient_cert.p12 danmu-server/certs/guofuli1700692997/

# 设置文件权限
chmod 600 danmu-server/certs/guofuli1700692997/*.pem
chmod 600 danmu-server/certs/guofuli1700692997/*.p12
```

### 3. 配置商户信息

编辑 `danmu-server/certs/guofuli1700692997/config.json`：

```json
{
  "merchantId": "你的商户号",
  "appId": "wxbd7109f36cd77b2b",
  "apiKey": "你的32位API密钥",
  "apiV3Key": "你的32位API_v3密钥",
  "serialNo": "你的证书序列号",
  "description": "主商户 - guofuli1700692997",
  "isActive": true,
  "notifyUrl": "https://danmu-server.gbw8848.cn/api/payment/notify",
  "products": {
    "temp_vip": {
      "name": "24小时无限使用",
      "description": "24小时内无限制使用分享和全屏功能"
    },
    "lifetime_vip": {
      "name": "终身会员",
      "description": "永久无限制使用所有功能"
    }
  }
}
```

### 4. 获取证书序列号

```bash
# 方法1: 使用openssl命令
openssl x509 -in apiclient_cert.pem -noout -serial

# 方法2: 在微信商户平台查看
# 登录商户平台 → 账户中心 → API安全 → API证书
```

### 5. 验证配置

```bash
# 运行测试脚本
cd danmu-server
node scripts/test-certs.js

# 或者部署后调用API
curl https://danmu-server.gbw8848.cn/api/certs/validate/guofuli1700692997
```

## 🔧 配置多个商户

### 激活备用商户

1. **配置 lixiang1717867742**：
   ```bash
   # 放置证书文件
   cp apiclient_cert.pem danmu-server/certs/lixiang1717867742/
   cp apiclient_key.pem danmu-server/certs/lixiang1717867742/
   
   # 编辑配置文件
   vi danmu-server/certs/lixiang1717867742/config.json
   # 将 "isActive": false 改为 "isActive": true
   ```

2. **配置 haocaihua1717453427**：
   ```bash
   # 同样的步骤
   cp apiclient_cert.pem danmu-server/certs/haocaihua1717453427/
   cp apiclient_key.pem danmu-server/certs/haocaihua1717453427/
   
   # 编辑配置并激活
   vi danmu-server/certs/haocaihua1717453427/config.json
   ```

## 🔍 验证和测试

### 本地测试

```bash
# 运行证书测试脚本
cd danmu-server
node scripts/test-certs.js
```

### API测试

```bash
# 获取所有商户状态
curl https://danmu-server.gbw8848.cn/api/certs/status

# 验证特定商户
curl https://danmu-server.gbw8848.cn/api/certs/validate/guofuli1700692997

# 获取活跃商户列表
curl https://danmu-server.gbw8848.cn/api/certs/merchants

# 重新加载配置
curl -X POST https://danmu-server.gbw8848.cn/api/certs/reload
```

## ⚠️ 注意事项

### 安全要求
- 证书文件权限必须设置为 600
- 不要将证书文件提交到Git仓库
- 定期检查证书有效期

### 配置要求
- 商户号必须是数字
- API密钥必须是32位字符串
- 证书序列号必须正确

### 故障排除
1. **配置验证失败**：检查config.json中的字段是否完整
2. **证书文件不存在**：确认文件路径和权限
3. **商户未激活**：将isActive设置为true

## 📞 技术支持

如遇问题，请检查：
1. 证书文件是否正确放置
2. 配置文件格式是否正确
3. 文件权限是否设置为600
4. 商户信息是否填写完整

相关API文档：`GET /api/certs/help`
