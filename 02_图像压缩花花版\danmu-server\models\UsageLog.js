const mysql = require('mysql2/promise');
const { getConnection } = require('../config/database');

class UsageLog {
  /**
   * 记录使用日志
   * @param {object} logData 日志数据
   * @returns {boolean} 是否记录成功
   */
  static async log(logData) {
    const connection = await getConnection();
    
    try {
      await connection.execute(
        `INSERT INTO usage_logs (
          user_id, openid, action_type, is_free, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          logData.userId || null,
          logData.openid || null,
          logData.actionType,
          logData.isFree !== false, // 默认为true
          logData.ipAddress || null,
          logData.userAgent || null
        ]
      );
      
      return true;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取用户使用统计
   * @param {number} userId 用户ID
   * @param {number} days 统计天数（默认30天）
   * @returns {object} 使用统计
   */
  static async getUserStats(userId, days = 30) {
    const connection = await getConnection();
    
    try {
      const [stats] = await connection.execute(
        `SELECT 
          COUNT(*) as total_usage,
          COUNT(CASE WHEN action_type = 'share' THEN 1 END) as share_count,
          COUNT(CASE WHEN action_type = 'fullscreen' THEN 1 END) as fullscreen_count,
          COUNT(CASE WHEN is_free = TRUE THEN 1 END) as free_usage,
          COUNT(CASE WHEN is_free = FALSE THEN 1 END) as paid_usage,
          DATE(MIN(created_at)) as first_usage,
          DATE(MAX(created_at)) as last_usage
         FROM usage_logs 
         WHERE user_id = ? 
         AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)`,
        [userId, days]
      );
      
      return stats[0];
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取系统使用统计
   * @param {number} days 统计天数（默认7天）
   * @returns {object} 系统统计
   */
  static async getSystemStats(days = 7) {
    const connection = await getConnection();
    
    try {
      const [stats] = await connection.execute(
        `SELECT 
          COUNT(*) as total_usage,
          COUNT(DISTINCT user_id) as active_users,
          COUNT(CASE WHEN action_type = 'share' THEN 1 END) as total_shares,
          COUNT(CASE WHEN action_type = 'fullscreen' THEN 1 END) as total_fullscreen,
          COUNT(CASE WHEN is_free = TRUE THEN 1 END) as free_usage,
          COUNT(CASE WHEN is_free = FALSE THEN 1 END) as paid_usage,
          ROUND(COUNT(CASE WHEN is_free = FALSE THEN 1 END) * 100.0 / COUNT(*), 2) as paid_usage_rate
         FROM usage_logs 
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)`,
        [days]
      );
      
      return stats[0];
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取每日使用趋势
   * @param {number} days 统计天数（默认30天）
   * @returns {array} 每日使用数据
   */
  static async getDailyTrend(days = 30) {
    const connection = await getConnection();
    
    try {
      const [trend] = await connection.execute(
        `SELECT 
          DATE(created_at) as date,
          COUNT(*) as total_usage,
          COUNT(DISTINCT user_id) as active_users,
          COUNT(CASE WHEN action_type = 'share' THEN 1 END) as shares,
          COUNT(CASE WHEN action_type = 'fullscreen' THEN 1 END) as fullscreen,
          COUNT(CASE WHEN is_free = TRUE THEN 1 END) as free_usage,
          COUNT(CASE WHEN is_free = FALSE THEN 1 END) as paid_usage
         FROM usage_logs 
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
         GROUP BY DATE(created_at)
         ORDER BY date DESC`,
        [days]
      );
      
      return trend;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取热门时段统计
   * @param {number} days 统计天数（默认7天）
   * @returns {array} 时段使用数据
   */
  static async getHourlyStats(days = 7) {
    const connection = await getConnection();
    
    try {
      const [stats] = await connection.execute(
        `SELECT 
          HOUR(created_at) as hour,
          COUNT(*) as usage_count,
          COUNT(DISTINCT user_id) as active_users,
          COUNT(CASE WHEN action_type = 'share' THEN 1 END) as shares,
          COUNT(CASE WHEN action_type = 'fullscreen' THEN 1 END) as fullscreen
         FROM usage_logs 
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
         GROUP BY HOUR(created_at)
         ORDER BY hour`,
        [days]
      );
      
      return stats;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 清理旧的使用日志
   * @param {number} days 保留天数（默认90天）
   * @returns {number} 清理的记录数
   */
  static async cleanOldLogs(days = 90) {
    const connection = await getConnection();
    
    try {
      const [result] = await connection.execute(
        'DELETE FROM usage_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
        [days]
      );
      
      return result.affectedRows;
      
    } finally {
      await connection.release();
    }
  }
  
  /**
   * 获取用户行为分析
   * @param {number} userId 用户ID
   * @returns {object} 用户行为分析
   */
  static async getUserBehavior(userId) {
    const connection = await getConnection();
    
    try {
      // 获取用户使用习惯
      const [behavior] = await connection.execute(
        `SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN action_type = 'share' THEN 1 END) as share_sessions,
          COUNT(CASE WHEN action_type = 'fullscreen' THEN 1 END) as fullscreen_sessions,
          ROUND(AVG(HOUR(created_at)), 1) as avg_usage_hour,
          COUNT(CASE WHEN HOUR(created_at) BETWEEN 9 AND 17 THEN 1 END) as work_hours_usage,
          COUNT(CASE WHEN HOUR(created_at) BETWEEN 18 AND 23 THEN 1 END) as evening_usage,
          COUNT(CASE WHEN WEEKDAY(created_at) IN (5, 6) THEN 1 END) as weekend_usage,
          DATEDIFF(MAX(created_at), MIN(created_at)) + 1 as active_days
         FROM usage_logs 
         WHERE user_id = ?`,
        [userId]
      );
      
      // 获取最近使用记录
      const [recentUsage] = await connection.execute(
        `SELECT action_type, created_at, is_free
         FROM usage_logs 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 10`,
        [userId]
      );
      
      return {
        behavior: behavior[0],
        recentUsage: recentUsage
      };
      
    } finally {
      await connection.release();
    }
  }
}

module.exports = UsageLog;
