const express = require('express');
const router = express.Router();
const MerchantMatcher = require('../services/merchantMatcher');
const WechatPayService = require('../services/wechatPayService');

/**
 * 获取商户映射统计信息
 */
router.get('/stats', (req, res) => {
  try {
    const stats = MerchantMatcher.getStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取商户统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 根据AppID获取对应的商户信息
 */
router.get('/match/:appId', (req, res) => {
  try {
    const { appId } = req.params;
    
    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'AppID不能为空'
      });
    }

    const merchantId = MerchantMatcher.getMerchantIdByAppId(appId);
    const isSupported = MerchantMatcher.isAppIdSupported(appId);
    const config = MerchantMatcher.getMerchantConfigByAppId(appId);

    res.json({
      success: true,
      data: {
        appId,
        merchantId,
        isSupported,
        businessName: config.businessName,
        description: config.description,
        isActive: config.isActive,
        verifyMethod: config.verifyMethod,
        hasWechatConfig: !!config.wechat_config,
        wechatAppId: config.wechat_config?.app_id || null
      }
    });
  } catch (error) {
    console.error('匹配商户失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取所有支持的AppID列表
 */
router.get('/appids', (req, res) => {
  try {
    const appIds = MerchantMatcher.getSupportedAppIds();
    res.json({
      success: true,
      data: {
        appIds,
        count: appIds.length
      }
    });
  } catch (error) {
    console.error('获取AppID列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 根据商户ID获取支持的AppID列表
 */
router.get('/:merchantId/appids', (req, res) => {
  try {
    const { merchantId } = req.params;
    const appIds = MerchantMatcher.getAppIdsByMerchant(merchantId);
    
    res.json({
      success: true,
      data: {
        merchantId,
        appIds,
        count: appIds.length
      }
    });
  } catch (error) {
    console.error('获取商户AppID列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 测试微信配置（根据AppID）
 */
router.get('/test/wechat-config/:appId', (req, res) => {
  try {
    const { appId } = req.params;

    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'AppID不能为空'
      });
    }

    const merchantId = MerchantMatcher.getMerchantIdByAppId(appId);
    const config = MerchantMatcher.getMerchantConfigByAppId(appId);

    res.json({
      success: true,
      data: {
        appId,
        merchantId,
        businessName: config.businessName,
        hasWechatConfig: !!config.wechat_config,
        wechatConfig: config.wechat_config ? {
          app_id: config.wechat_config.app_id,
          app_secret: config.wechat_config.app_secret ? '***已配置***' : '未配置'
        } : null
      }
    });
  } catch (error) {
    console.error('测试微信配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 测试支付服务创建（根据AppID）
 */
router.post('/test/payment-service', (req, res) => {
  try {
    const { appId, merchantId } = req.body;
    
    let paymentService;
    let method;
    
    if (appId) {
      paymentService = WechatPayService.createByAppId(appId);
      method = 'AppID匹配';
    } else if (merchantId) {
      paymentService = WechatPayService.createByMerchantId(merchantId);
      method = '商户ID指定';
    } else {
      paymentService = new WechatPayService();
      method = '默认商户';
    }

    res.json({
      success: true,
      data: {
        method,
        merchantId: paymentService.merchantId,
        businessName: paymentService.config.businessName,
        description: paymentService.config.description,
        isActive: paymentService.config.isActive,
        appId: paymentService.config.appid
      }
    });
  } catch (error) {
    console.error('测试支付服务创建失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 重新加载商户映射配置
 */
router.post('/reload', (req, res) => {
  try {
    MerchantMatcher.reload();
    const stats = MerchantMatcher.getStats();
    
    res.json({
      success: true,
      message: '商户映射配置已重新加载',
      data: stats
    });
  } catch (error) {
    console.error('重新加载配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 添加AppID映射
 */
router.post('/mapping', (req, res) => {
  try {
    const { appId, merchantId } = req.body;
    
    if (!appId || !merchantId) {
      return res.status(400).json({
        success: false,
        error: 'AppID和商户ID都不能为空'
      });
    }

    const success = MerchantMatcher.addAppIdMapping(appId, merchantId);
    
    if (success) {
      res.json({
        success: true,
        message: `映射添加成功: ${appId} -> ${merchantId}`
      });
    } else {
      res.status(400).json({
        success: false,
        error: '映射添加失败'
      });
    }
  } catch (error) {
    console.error('添加映射失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 移除AppID映射
 */
router.delete('/mapping/:appId', (req, res) => {
  try {
    const { appId } = req.params;
    const success = MerchantMatcher.removeAppIdMapping(appId);
    
    if (success) {
      res.json({
        success: true,
        message: `映射移除成功: ${appId}`
      });
    } else {
      res.status(404).json({
        success: false,
        error: '映射不存在'
      });
    }
  } catch (error) {
    console.error('移除映射失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取商户映射帮助信息
 */
router.get('/help', (req, res) => {
  res.json({
    success: true,
    data: {
      title: '多商户智能匹配系统',
      description: '根据前端小程序AppID自动匹配对应的商户配置',
      endpoints: {
        'GET /api/merchant/stats': '获取商户映射统计信息',
        'GET /api/merchant/match/:appId': '根据AppID获取对应的商户信息',
        'GET /api/merchant/appids': '获取所有支持的AppID列表',
        'GET /api/merchant/:merchantId/appids': '根据商户ID获取支持的AppID列表',
        'POST /api/merchant/test/payment-service': '测试支付服务创建',
        'POST /api/merchant/reload': '重新加载商户映射配置',
        'POST /api/merchant/mapping': '添加AppID映射',
        'DELETE /api/merchant/mapping/:appId': '移除AppID映射',
        'GET /api/merchant/help': '获取帮助信息'
      },
      usage: {
        '创建支付服务': {
          '根据AppID': 'WechatPayService.createByAppId(appId)',
          '根据商户ID': 'WechatPayService.createByMerchantId(merchantId)',
          '使用默认': 'new WechatPayService()'
        },
        '配置文件格式': {
          'supportedAppIds': ['wx123456789', 'wx987654321'],
          'businessName': '业务名称',
          'description': '商户描述'
        }
      }
    }
  });
});

module.exports = router;
