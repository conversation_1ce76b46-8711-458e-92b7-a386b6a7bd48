const express = require('express');
const router = express.Router();
const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
const AppManager = require('../services/MerchantManager');

/**
 * 创建订单
 */
router.post('/create', async (req, res) => {
  try {
    const { openid, appId, productCode } = req.body;

    if (!openid || !appId || !productCode) {
      return res.status(400).json({
        success: false,
        error: 'openid、appId和productCode不能为空'
      });
    }

    const result = await transaction(async (connection) => {
      // 获取用户信息
      const [users] = await connection.execute(
        'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
        [openid, appId]
      );

      if (users.length === 0) {
        throw new Error('用户不存在');
      }

      const userId = users[0].user_id;

      // 使用小程序管理器获取小程序配置
      const appConfig = AppManager.getAppByAppId(appId);
      if (!appConfig) {
        throw new Error('未找到对应的小程序配置');
      }

      // 获取商品信息（从数据库）
      const product = await AppManager.getProductByCode(appId, productCode);
      if (!product) {
        throw new Error('商品不存在或已下架');
      }

      // 生成订单ID
      const orderId = `IC${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

      // 创建订单
      await connection.execute(
        `INSERT INTO orders (
          order_id, user_id, openid, app_id,
          product_code, product_name, amount, expire_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderId,
          userId,
          openid,
          appId,
          productCode,
          product.productName,
          product.price,
          moment().add(30, 'minutes').toDate() // 30分钟后过期
        ]
      );

      return {
        orderId,
        productName: product.productName,
        amount: parseFloat(product.price),
        appid: appId,
        businessName: appConfig.businessName,
        expireAt: moment().add(30, 'minutes').toISOString()
      };
    });

    res.json({
      success: true,
      data: result,
      message: '订单创建成功'
    });

  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 查询订单列表
 */
router.get('/list', async (req, res) => {
  try {
    const { openid, appId, page = 1, limit = 10, status } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户ID
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userId = user[0].user_id;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE user_id = ?';
    let queryParams = [userId];

    if (status) {
      whereClause += ' AND payment_status = ?';
      queryParams.push(status);
    }

    // 查询订单列表
    const orders = await query(
      `SELECT * FROM orders 
       ${whereClause}
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    // 获取总数
    const totalResult = await query(
      `SELECT COUNT(*) as total FROM orders ${whereClause}`,
      queryParams
    );

    res.json({
      success: true,
      data: {
        orders: orders.map(order => ({
          orderId: order.order_id,
          productCode: order.product_code,
          productName: order.product_name,
          amount: parseFloat(order.amount),
          paymentStatus: order.payment_status,
          paymentMethod: order.payment_method,
          transactionId: order.transaction_id,
          createdAt: order.created_at,
          paidAt: order.paid_at,
          expireAt: order.expire_at
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          totalPages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('查询订单列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 查询单个订单
 */
router.get('/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    const { openid, appId } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 查询订单
    const order = await query(
      'SELECT * FROM orders WHERE order_id = ? AND openid = ? AND app_id = ?',
      [orderId, openid, appId]
    );

    if (order.length === 0) {
      return res.status(404).json({
        success: false,
        error: '订单不存在'
      });
    }

    const orderData = order[0];

    res.json({
      success: true,
      data: {
        orderId: orderData.order_id,
        productCode: orderData.product_code,
        productName: orderData.product_name,
        amount: parseFloat(orderData.amount),
        paymentStatus: orderData.payment_status,
        paymentMethod: orderData.payment_method,
        transactionId: orderData.transaction_id,
        merchantId: orderData.merchant_id,
        createdAt: orderData.created_at,
        paidAt: orderData.paid_at,
        expireAt: orderData.expire_at,
        isExpired: orderData.expire_at && new Date() > new Date(orderData.expire_at)
      }
    });

  } catch (error) {
    console.error('查询订单失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 支付成功回调
 */
router.post('/pay-success', async (req, res) => {
  try {
    const { orderId, transactionId, paymentMethod = 'wechat' } = req.body;

    if (!orderId || !transactionId) {
      return res.status(400).json({
        success: false,
        error: 'orderId和transactionId不能为空'
      });
    }

    const result = await transaction(async (connection) => {
      // 获取订单信息
      const [orders] = await connection.execute(
        'SELECT * FROM orders WHERE order_id = ? AND payment_status = "pending"',
        [orderId]
      );

      if (orders.length === 0) {
        throw new Error('订单不存在或已支付');
      }

      const order = orders[0];

      // 检查订单是否过期
      if (order.expire_at && new Date() > new Date(order.expire_at)) {
        throw new Error('订单已过期');
      }

      // 更新订单状态
      await connection.execute(
        `UPDATE orders SET 
         payment_status = 'paid', 
         transaction_id = ?, 
         payment_method = ?, 
         paid_at = NOW() 
         WHERE order_id = ?`,
        [transactionId, paymentMethod, orderId]
      );

      // 创建会员记录
      let expireTime = null;
      if (order.product_code === 'day_card') {
        expireTime = moment().add(24, 'hours').toDate();
      }

      await connection.execute(
        `INSERT INTO memberships (
          user_id, membership_type, start_time, expire_time, order_id
        ) VALUES (?, ?, NOW(), ?, ?)`,
        [order.user_id, order.product_code, expireTime, orderId]
      );

      return {
        orderId,
        membershipType: order.product_code,
        expireTime: expireTime ? moment(expireTime).toISOString() : null
      };
    });

    res.json({
      success: true,
      data: result,
      message: '支付成功，会员已激活'
    });

  } catch (error) {
    console.error('支付成功处理失败:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
