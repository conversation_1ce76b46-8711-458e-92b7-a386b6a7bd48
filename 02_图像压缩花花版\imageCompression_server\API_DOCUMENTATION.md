# 图像压缩小程序 API 文档

## 基础信息

- **服务器端口**: 8851
- **API 基础路径**: `/api`
- **数据格式**: JSON
- **认证方式**: 微信 openid + AppID

## 业务模式说明

- **图像压缩**: 完全在前端本地处理，后端不参与
- **免费次数**: 前端本地管理（每日仅1次免费）
- **付费模式**: 免费次数用完后，必须购买会员才能继续使用
- **后端职责**: 用户管理 + 会员系统 + 订单处理

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息"
}
```

## 用户相关接口 (`/api/user`)

### 1. 用户登录/注册
```http
POST /api/user/login
```

**请求参数:**
```json
{
  "openid": "string",
  "appId": "string",
  "userInfo": {
    "nickname": "string",
    "avatar_url": "string"
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user": {
      "userId": 1,
      "openid": "ox1234567890",
      "appId": "wx123456789",
      "nickname": "用户昵称",
      "avatarUrl": "https://...",
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    // 前端自己管理免费次数，后端不提供
    "membership": {
      "isActive": false,
      "type": null,
      "startTime": null,
      "expireTime": null
    }
  },
  "message": "登录成功"
}
```

### 2. 获取用户状态（主要是会员状态）
```http
GET /api/user/status?openid={openid}&appId={appId}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "userId": 1,
    "membership": {
      "isActive": true,
      "type": "day_card",
      "expireTime": "2024-01-02T00:00:00.000Z"
    }
  }
}
```

### 3. 检查会员权限（替代广告功能）
```http
POST /api/user/check-membership
```

**请求参数:**
```json
{
  "openid": "string",
  "appId": "string"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "hasActiveMembership": true,
    "membershipType": "day_card",
    "expireTime": "2024-01-02T00:00:00.000Z",
    "canReplaceAd": true
  }
}
```

## 会员相关接口 (`/api/membership`)

### 1. 获取商品列表
```http
GET /api/membership/products?appId={appId}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "merchantId": "merchant_001",
    "products": [
      {
        "productId": 1,
        "productCode": "day_card",
        "productName": "日卡会员",
        "description": "24小时内无限制压缩图片",
        "price": 4.99,
        "durationHours": 24,
        "isPermanent": false
      },
      {
        "productId": 2,
        "productCode": "permanent",
        "productName": "永久会员",
        "description": "永久无限制压缩图片",
        "price": 19.99,
        "durationHours": null,
        "isPermanent": true
      }
    ]
  }
}
```

### 2. 获取会员状态
```http
GET /api/membership/status?openid={openid}&appId={appId}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "isActive": true,
    "type": "day_card",
    "startTime": "2024-01-01T00:00:00.000Z",
    "expireTime": "2024-01-02T00:00:00.000Z",
    "remainingTime": 3600,
    "isPermanent": false
  }
}
```

### 3. 检查会员权限（用于替代广告逻辑）
```http
POST /api/membership/check-permission
```

**请求参数:**
```json
{
  "openid": "string",
  "appId": "string"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "hasActiveMembership": true,
    "membershipType": "day_card",
    "expireTime": "2024-01-02T00:00:00.000Z",
    "canReplaceAd": true,
    "shouldShowMembershipOffer": false
  }
}
```

## 订单相关接口 (`/api/order`)

### 1. 创建订单
```http
POST /api/order/create
```

**请求参数:**
```json
{
  "openid": "string",
  "appId": "string",
  "productCode": "day_card"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "orderId": "IC1704067200ABC123",
    "productName": "日卡会员",
    "amount": 4.99,
    "merchantId": "merchant_001",
    "expireAt": "2024-01-01T01:00:00.000Z"
  },
  "message": "订单创建成功"
}
```

### 2. 查询订单列表
```http
GET /api/order/list?openid={openid}&appId={appId}&page=1&limit=10&status=paid
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "orderId": "IC1704067200ABC123",
        "productCode": "day_card",
        "productName": "日卡会员",
        "amount": 4.99,
        "paymentStatus": "paid",
        "paymentMethod": "wechat",
        "transactionId": "wx123456789",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "paidAt": "2024-01-01T00:05:00.000Z",
        "expireAt": "2024-01-01T01:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

### 3. 支付成功回调
```http
POST /api/order/pay-success
```

**请求参数:**
```json
{
  "orderId": "string",
  "transactionId": "string",
  "paymentMethod": "wechat"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "orderId": "IC1704067200ABC123",
    "membershipType": "day_card",
    "expireTime": "2024-01-02T00:00:00.000Z"
  },
  "message": "支付成功，会员已激活"
}
```

## 商户相关接口 (`/api/merchant`)

### 1. 获取商户信息
```http
GET /api/merchant/info/{appId}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "merchantId": "merchant_001",
    "appId": "wx123456789",
    "businessName": "商户A",
    "isActive": true,
    "hasWechatConfig": true,
    "wechatAppId": "wx123456789",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. 检查AppID支持
```http
GET /api/merchant/check/{appId}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "appId": "wx123456789",
    "isSupported": true,
    "isActive": true,
    "merchantId": "merchant_001",
    "businessName": "商户A"
  }
}
```

### 3. 系统概览统计
```http
GET /api/merchant/system/overview
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total": {
      "active_merchants": 5,
      "total_users": 1000,
      "total_paid_orders": 500,
      "total_revenue": 2495.00,
      "active_memberships": 200
    },
    "today": {
      "new_users_today": 10,
      "orders_today": 5,
      "paid_orders_today": 3,
      "revenue_today": 14.97
    }
  }
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 常见错误信息

- `openid和appId不能为空` - 缺少必要参数
- `用户不存在` - 用户未注册
- `未找到对应的商户配置` - AppID未配置
- `商品不存在或已下架` - 商品配置错误
- `订单不存在或已支付` - 订单状态异常
- `今日免费次数已用完，请开通会员` - 免费额度不足

## 使用示例

### 小程序端调用示例
```javascript
// 用户登录
const loginResult = await wx.request({
  url: 'https://your-domain.com/api/user/login',
  method: 'POST',
  data: {
    openid: 'ox1234567890',
    appId: 'wx123456789',
    userInfo: {
      nickname: '用户昵称',
      avatar_url: 'https://...'
    }
  }
});

// 检查权限
const permissionResult = await wx.request({
  url: 'https://your-domain.com/api/membership/check-permission',
  method: 'POST',
  data: {
    openid: 'ox1234567890',
    appId: 'wx123456789'
  }
});

// 使用免费次数
if (permissionResult.data.hasFreeQuota) {
  await wx.request({
    url: 'https://your-domain.com/api/user/use-free',
    method: 'POST',
    data: {
      openid: 'ox1234567890',
      appId: 'wx123456789'
    }
  });
}
```
