# 微信支付证书管理

## 📁 文件夹结构

```
certs/
├── README.md                       # 说明文档
├── guofuli1700692997/              # 主商户证书目录
│   ├── apiclient_cert.pem         # API证书
│   ├── apiclient_key.pem          # API私钥
│   ├── apiclient_cert.p12         # PKCS12格式证书（可选）
│   └── config.json                # 商户配置信息
├── lixiang1717867742/              # 备用商户2证书目录
│   ├── apiclient_cert.pem
│   ├── apiclient_key.pem
│   ├── apiclient_cert.p12
│   └── config.json
└── haocaihua1717453427/            # 备用商户3证书目录
    ├── apiclient_cert.pem
    ├── apiclient_key.pem
    ├── apiclient_cert.p12
    └── config.json
```

## 🔧 配置说明

### 证书文件
- `apiclient_cert.pem`: 微信支付API证书（公钥）
- `apiclient_key.pem`: 微信支付API私钥
- `apiclient_cert.p12`: PKCS12格式证书（包含公钥和私钥）

### 验签方式文件
- `wechatpay_public_key.pem`: 微信支付公钥（新方式，从商户平台下载）

### 验签方式说明
- **平台证书验签**：使用平台证书公钥，序列号格式如 `4DF076AC...`
- **微信支付公钥验签**：使用微信支付公钥，ID格式如 `PUB_KEY_ID_...`

### 配置文件 (config.json)
```json
{
  "mchid": "1234567890",
  "appid": "wxbd7109f36cd77b2b",
  "api_v3_key": "your_api_v3_key_32_characters",
  "serial_no": "certificate_serial_number_or_public_key_id",
  "private_key_path": "apiclient_key.pem",
  "cert_path": "apiclient_cert.pem",
  "description": "商户描述",
  "isActive": true,
  "verifyMethod": "platform_cert"
}
```

#### 参数说明
- **mchid**: 商户号（必需）
- **appid**: 小程序AppID（必需）
- **api_v3_key**: API v3密钥，32位字符（必需）
- **serial_no**: 商户API证书序列号或微信支付公钥ID（必需）
- **private_key_path**: 商户API私钥文件路径（必需）
- **cert_path**: 商户API证书文件路径（必需）

⚠️ **注意**: API v3不需要APIv2密钥，两者完全隔离

#### verifyMethod 配置说明
- `"platform_cert"`: 使用平台证书验签（传统方式）
- `"public_key"`: 使用微信支付公钥验签（新方式）

## 📋 证书获取步骤

1. **登录微信商户平台**
   - 访问：https://pay.weixin.qq.com/
   - 使用商户账号登录

2. **下载商户API证书**
   - 进入：账户中心 → API安全 → API证书
   - 下载证书文件（apiclient_cert.pem 和 apiclient_key.pem）

3. **获取证书序列号**
   - 在API证书页面查看证书序列号
   - 或使用命令：`openssl x509 -in apiclient_cert.pem -noout -serial`

4. **设置API v3密钥**
   - 进入：账户中心 → API安全 → API v3密钥
   - 设置32位字符的API v3密钥
   - ⚠️ **注意**：API v3不需要设置API v2密钥

5. **获取微信支付公钥（如使用公钥验签）**
   - 进入：账户中心 → API安全 → 微信支付公钥
   - 下载公钥文件并获取公钥ID

## 🔒 安全注意事项

1. **文件权限**
   ```bash
   chmod 600 *.pem *.p12
   chmod 644 config.json
   ```

2. **版本控制**
   - 证书文件不应提交到Git仓库
   - 已在 .gitignore 中排除

3. **备份**
   - 定期备份证书文件
   - 证书过期前及时更新

## 🚀 使用方式

```javascript
const PaymentService = require('../services/paymentService');

// 使用默认商户（guofuli1700692997）
const payment = new PaymentService();

// 使用指定商户
const payment = new PaymentService('lixiang1717867742');
const payment = new PaymentService('haocaihua1717453427');
```

## 📞 技术支持

如有证书配置问题，请联系：
- 微信支付技术支持
- 项目技术负责人
