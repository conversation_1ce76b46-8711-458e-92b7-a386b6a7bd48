@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义主题色变量 - 微信风格 */
/* 辅助色 */
/* 背景和文字颜色 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 60rpx 40rpx;
}
.modal-overlay .modal-content {
  background: #F7F7F7;
  box-shadow: 12px 12px 24px rgba(0, 0, 0, 0.1), -8px -8px 20px rgba(255, 255, 255, 0.9), inset 2px 2px 4px rgba(255, 255, 255, 0.5), inset -2px -2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}
.modal-overlay .modal-content .modal-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.modal-overlay .modal-content .modal-header .modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}
.modal-overlay .modal-content .modal-header .close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}
.modal-overlay .modal-content .modal-header .close-btn::after {
  display: none;
}
.modal-overlay .modal-content .modal-header .close-btn:active {
  background: rgba(0, 0, 0, 0.05);
}
.modal-overlay .modal-content .modal-header .close-btn .close-icon {
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
  font-weight: 300;
}
.modal-overlay .modal-content .packages-list {
  padding: 20rpx 40rpx;
}
.modal-overlay .modal-content .packages-list .package-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}
.modal-overlay .modal-content .packages-list .package-item:last-child {
  margin-bottom: 0;
}
.modal-overlay .modal-content .packages-list .package-item.selected {
  border-color: #07C160;
  background: rgba(7, 193, 96, 0.05);
}
.modal-overlay .modal-content .packages-list .package-item:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.modal-overlay .modal-content .packages-list .package-item .package-info {
  flex: 1;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-header .package-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-header .package-badge {
  background: linear-gradient(135deg, #07C160, #06AD56);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-header .package-badge.permanent {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-header .package-badge .badge-text {
  font-weight: 500;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-price {
  display: flex;
  align-items: center;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-price .current-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #07C160;
  margin-right: 16rpx;
}
.modal-overlay .modal-content .packages-list .package-item .package-info .package-price .original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.modal-overlay .modal-content .packages-list .package-item .package-radio .radio-circle {
  width: 40rpx;
  height: 40rpx;
  border: 2px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.modal-overlay .modal-content .packages-list .package-item .package-radio .radio-circle.checked {
  border-color: #07C160;
  background: #07C160;
}
.modal-overlay .modal-content .packages-list .package-item .package-radio .radio-circle .radio-dot {
  width: 20rpx;
  height: 20rpx;
  background: white;
  border-radius: 50%;
}
.modal-overlay .modal-content .modal-footer {
  padding: 20rpx 40rpx 40rpx;
}
.modal-overlay .modal-content .modal-footer .pay-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #07C160, #06AD56);
  border: none;
  border-radius: 40rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);
}
.modal-overlay .modal-content .modal-footer .pay-btn::after {
  display: none;
}
.modal-overlay .modal-content .modal-footer .pay-btn:active {
  background: linear-gradient(135deg, #06AD56, #059C4F);
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.modal-overlay .modal-content .modal-footer .pay-btn:disabled {
  background: #ccc;
  box-shadow: none;
  -webkit-transform: none;
          transform: none;
}
