const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const User = require('../models/User');
const { formatSuccess, formatError } = require('../utils/helpers');

/**
 * 获取产品配置
 * GET /api/order/products
 */
router.get('/products', async (req, res) => {
  try {
    const products = await Order.getProductConfig();

    res.json(formatSuccess(products, '获取产品配置成功'));

  } catch (error) {
    console.error('获取产品配置失败:', error);
    res.status(500).json(formatError(
      '获取产品配置失败',
      'GET_PRODUCTS_FAILED',
      error.message
    ));
  }
});

/**
 * 创建订单
 * POST /api/order/create
 */
router.post('/create', async (req, res) => {
  try {
    const { userId, openid, productType } = req.body;
    
    if (!userId || !openid || !productType) {
      return res.status(400).json(formatError(
        '缺少必要参数',
        'MISSING_PARAMS'
      ));
    }
    
    if (!['temp_vip', 'lifetime_vip'].includes(productType)) {
      return res.status(400).json(formatError(
        '无效的产品类型',
        'INVALID_PRODUCT_TYPE'
      ));
    }
    
    const product = await Order.getProductByType(productType);

    if (!product) {
      return res.status(400).json(formatError(
        '产品不存在或已下架',
        'PRODUCT_NOT_FOUND'
      ));
    }
    
    const orderData = {
      userId: parseInt(userId),
      openid: openid,
      productType: productType,
      productName: product.name,
      amount: product.price
    };
    
    const order = await Order.createOrder(orderData);
    
    res.json(formatSuccess({
      orderId: order.order_id,
      productName: order.product_name,
      amount: order.amount,
      expireAt: order.expire_at,
      paymentStatus: order.payment_status
    }, '订单创建成功'));
    
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json(formatError(
      '创建订单失败',
      'CREATE_ORDER_FAILED',
      error.message
    ));
  }
});

/**
 * 获取订单详情
 * GET /api/order/:orderId
 */
router.get('/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const order = await Order.getOrder(orderId);
    
    if (!order) {
      return res.status(404).json(formatError(
        '订单不存在',
        'ORDER_NOT_FOUND'
      ));
    }
    
    res.json(formatSuccess({
      orderId: order.order_id,
      productType: order.product_type,
      productName: order.product_name,
      amount: order.amount,
      paymentStatus: order.payment_status,
      createdAt: order.created_at,
      paidAt: order.paid_at,
      expireAt: order.expire_at
    }, '获取订单详情成功'));
    
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json(formatError(
      '获取订单详情失败',
      'GET_ORDER_FAILED',
      error.message
    ));
  }
});

/**
 * 获取用户订单列表
 * GET /api/order/user/:userId
 */
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const orders = await Order.getUserOrders(parseInt(userId), parseInt(limit), offset);
    
    res.json(formatSuccess({
      orders: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: orders.length
      }
    }, '获取用户订单成功'));
    
  } catch (error) {
    console.error('获取用户订单失败:', error);
    res.status(500).json(formatError(
      '获取用户订单失败',
      'GET_USER_ORDERS_FAILED',
      error.message
    ));
  }
});

/**
 * 测试VIP激活功能（开发测试用）
 * POST /api/order/test-vip-activation
 */
router.post('/test-vip-activation', async (req, res) => {
  try {
    const { userId, vipType } = req.body;

    if (!userId || !vipType) {
      return res.status(400).json(formatError(
        '缺少必要参数：userId 和 vipType',
        'MISSING_PARAMS'
      ));
    }

    if (!['temp', 'lifetime'].includes(vipType)) {
      return res.status(400).json(formatError(
        'vipType 必须是 temp 或 lifetime',
        'INVALID_VIP_TYPE'
      ));
    }

    console.log(`🧪 测试VIP激活: userId=${userId}, vipType=${vipType}`);

    // 测试VIP激活
    const User = require('../models/User');
    const result = await User.activateVip(userId, vipType);

    // 获取更新后的用户信息
    const updatedQuota = await User.getUserQuota(userId);

    res.json(formatSuccess({
      activationResult: result,
      quota: {
        hasVipAccess: updatedQuota.has_vip_access,
        isVip: updatedQuota.is_vip,
        vipExpireTime: updatedQuota.vip_expire_time,
        tempVipExpireTime: updatedQuota.temp_vip_expire_time
      }
    }, 'VIP激活测试完成'));

  } catch (error) {
    console.error('VIP激活测试失败:', error);
    res.status(500).json(formatError(
      'VIP激活测试失败',
      'VIP_ACTIVATION_TEST_FAILED',
      error.message
    ));
  }
});

/**
 * 模拟支付成功（开发测试用）
 * POST /api/order/pay-success
 */
router.post('/pay-success', async (req, res) => {
  try {
    const { orderId, transactionId } = req.body;
    
    if (!orderId) {
      return res.status(400).json(formatError(
        '缺少订单ID',
        'MISSING_ORDER_ID'
      ));
    }
    
    // 检查订单是否存在
    const order = await Order.getOrder(orderId);
    if (!order) {
      return res.status(404).json(formatError(
        '订单不存在',
        'ORDER_NOT_FOUND'
      ));
    }
    
    if (order.payment_status === 'paid') {
      return res.status(400).json(formatError(
        '订单已支付',
        'ORDER_ALREADY_PAID'
      ));
    }
    
    // 处理支付成功
    await Order.processPaymentSuccess(orderId, {
      transactionId: transactionId || `test_${Date.now()}`,
      paymentMethod: 'wechat_pay'
    });
    
    // 获取更新后的用户信息
    const updatedQuota = await User.getUserQuota(order.user_id);
    
    res.json(formatSuccess({
      orderId: orderId,
      paymentStatus: 'paid',
      quota: {
        hasVipAccess: updatedQuota.has_vip_access,
        isVip: updatedQuota.is_vip,
        vipExpireTime: updatedQuota.vip_expire_time,
        tempVipExpireTime: updatedQuota.temp_vip_expire_time
      }
    }, '支付成功'));
    
  } catch (error) {
    console.error('处理支付成功失败:', error);
    res.status(500).json(formatError(
      '处理支付失败',
      'PROCESS_PAYMENT_FAILED',
      error.message
    ));
  }
});

/**
 * 微信支付回调（实际生产环境使用）
 * POST /api/order/wechat-callback
 */
router.post('/wechat-callback', async (req, res) => {
  try {
    // 这里应该验证微信支付回调的签名
    // 实际实现时需要根据微信支付文档进行验证
    
    const { out_trade_no, transaction_id, trade_state } = req.body;
    
    if (trade_state === 'SUCCESS') {
      await Order.processPaymentSuccess(out_trade_no, {
        transactionId: transaction_id,
        paymentMethod: 'wechat_pay'
      });
    } else {
      await Order.updatePaymentStatus(out_trade_no, 'failed');
    }
    
    // 返回微信要求的格式
    res.json({
      code: 'SUCCESS',
      message: '成功'
    });
    
  } catch (error) {
    console.error('微信支付回调处理失败:', error);
    res.json({
      code: 'FAIL',
      message: '处理失败'
    });
  }
});

/**
 * 获取订单统计
 * GET /api/order/stats
 */
router.get('/stats/summary', async (req, res) => {
  try {
    const { userId } = req.query;
    
    const stats = await Order.getOrderStats(userId ? parseInt(userId) : null);
    
    res.json(formatSuccess(stats, '获取订单统计成功'));
    
  } catch (error) {
    console.error('获取订单统计失败:', error);
    res.status(500).json(formatError(
      '获取订单统计失败',
      'GET_ORDER_STATS_FAILED',
      error.message
    ));
  }
});

/**
 * 清理过期订单
 * POST /api/order/cleanup
 */
router.post('/cleanup', async (req, res) => {
  try {
    const cleanedCount = await Order.cleanExpiredOrders();

    res.json(formatSuccess({
      cleanedCount: cleanedCount
    }, `清理了 ${cleanedCount} 个过期订单`));

  } catch (error) {
    console.error('清理过期订单失败:', error);
    res.status(500).json(formatError(
      '清理过期订单失败',
      'CLEANUP_ORDERS_FAILED',
      error.message
    ));
  }
});

/**
 * 更新产品价格
 * PUT /api/order/price
 */
router.put('/price', async (req, res) => {
  try {
    const { productType, newPrice } = req.body;

    if (!productType || !newPrice) {
      return res.status(400).json(formatError(
        '缺少必要参数',
        'MISSING_PARAMS'
      ));
    }

    if (!['temp_vip', 'lifetime_vip'].includes(productType)) {
      return res.status(400).json(formatError(
        '无效的产品类型',
        'INVALID_PRODUCT_TYPE'
      ));
    }

    if (isNaN(newPrice) || newPrice <= 0) {
      return res.status(400).json(formatError(
        '价格必须是大于0的数字',
        'INVALID_PRICE'
      ));
    }

    const success = await Order.updateProductPrice(productType, parseFloat(newPrice));

    if (!success) {
      return res.status(404).json(formatError(
        '产品不存在',
        'PRODUCT_NOT_FOUND'
      ));
    }

    res.json(formatSuccess({
      productType: productType,
      newPrice: parseFloat(newPrice)
    }, '价格更新成功'));

  } catch (error) {
    console.error('更新产品价格失败:', error);
    res.status(500).json(formatError(
      '更新产品价格失败',
      'UPDATE_PRICE_FAILED',
      error.message
    ));
  }
});

module.exports = router;
