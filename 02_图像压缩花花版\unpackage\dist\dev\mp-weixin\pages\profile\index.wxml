<view class="profile-container"><view class="custom-nav glassmorphism"><view class="status-bar" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="nav-content"><view class="back-button-container"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-btn" bindtap="__e"><text class="back-icon">‹</text></button></view><text class="nav-title">我的</text><view class="nav-right-buttons"></view></view></view><view class="main-content" style="{{'padding-top:'+(navHeight+'px')+';'}}"><view class="user-info-card neumorphism"><view class="user-header"><view class="user-avatar"><image class="avatar-image" src="/static/user-avatar.svg" mode="aspectFit"></image></view><view class="user-details"><view class="user-name">微信用户</view><view class="member-badge">{{$root.m0}}</view></view><view class="member-icon"><image class="crown-image" src="/static/crown.svg" mode="aspectFit"></image></view></view><view class="member-section"><view class="member-header"><text class="member-title">{{$root.m1}}</text><text class="member-subtitle">尊享会员特权</text></view><view class="divider-line"></view><view class="privileges-grid"><view class="privilege-item"><view class="check-icon"><text class="check-mark">✓</text></view><text class="privilege-text">无限次图片压缩</text></view><view class="privilege-item"><view class="check-icon"><text class="check-mark">✓</text></view><text class="privilege-text">批量压缩处理</text></view><view class="privilege-item"><view class="check-icon"><text class="check-mark">✓</text></view><text class="privilege-text">高级压缩算法</text></view><view class="privilege-item"><view class="check-icon"><text class="check-mark">✓</text></view><text class="privilege-text">专属会员服务</text></view></view><view class="divider-line"></view><view class="member-validity"><text>{{"会员有效期: "+$root.m2}}</text></view><button data-event-opts="{{[['tap',[['handleRenewMember',['$event']]]]]}}" class="{{['renew-btn',(memberInfo.type==='normal')?'heartbeat-animation':'']}}" bindtap="__e">{{''+$root.m3+''}}</button></view></view><view class="menu-section"><view data-event-opts="{{[['tap',[['handleMenuClick',['order']]]]]}}" class="menu-item neumorphism" bindtap="__e"><view class="menu-icon"><image class="icon-image" src="/static/order.svg" mode="aspectFit"></image></view><text class="menu-text">订单查询</text></view><view data-event-opts="{{[['tap',[['handleMenuClick',['feedback']]]]]}}" class="menu-item neumorphism" bindtap="__e"><view class="menu-icon"><image class="icon-image" src="/static/feedback.svg" mode="aspectFit"></image></view><text class="menu-text">联系我们</text></view><view data-event-opts="{{[['tap',[['handleMenuClick',['about']]]]]}}" class="menu-item neumorphism" bindtap="__e"><view class="menu-icon"><image class="icon-image" src="/static/about.svg" mode="aspectFit"></image></view><text class="menu-text">关于我们</text></view><view data-event-opts="{{[['tap',[['handleMenuClick',['test']]]]]}}" class="menu-item neumorphism" bindtap="__e"><view class="menu-icon"><text style="font-size:48rpx;">🔧</text></view><text class="menu-text">测试会员功能</text></view></view><member-package-modal vue-id="fa7063ba-1" visible="{{showMemberModal}}" data-event-opts="{{[['^close',[['e0']]],['^purchaseSuccess',[['handlePurchaseSuccess']]]]}}" bind:close="__e" bind:purchaseSuccess="__e" bind:__l="__l"></member-package-modal></view></view>