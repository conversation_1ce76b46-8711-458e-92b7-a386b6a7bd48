<template>
	<view class="profile-container">
		<!-- 自定义导航栏 -->
		<view class="custom-nav glassmorphism">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-content">
				<view class="back-button-container">
					<button class="back-btn" @tap="goBack">
						<text class="back-icon">‹</text>
					</button>
				</view>
				<text class="nav-title">我的</text>
				<view class="nav-right-buttons">
					<!-- 移除自定义按钮，使用微信官方按钮 -->
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content" :style="{ paddingTop: navHeight + 'px' }">
			<!-- 用户信息和会员卡片 -->
			<view class="user-info-card neumorphism">
				<!-- 用户基本信息 -->
				<view class="user-header">
					<view class="user-avatar">
						<image src="/static/user-avatar.svg" mode="aspectFit" class="avatar-image"></image>
					</view>
					<view class="user-details">
						<view class="user-name">微信用户</view>
						<view class="member-badge">终身会员</view>
					</view>
					<view class="member-icon">
						<image src="/static/crown.svg" mode="aspectFit" class="crown-image"></image>
					</view>
				</view>

				<!-- 会员特权信息 -->
				<view class="member-section">
					<view class="member-header">
						<text class="member-title">终身会员</text>
						<text class="member-subtitle">尊享会员特权</text>
					</view>
					<!-- 分隔线 -->
					<view class="divider-line"></view>
					<view class="privileges-grid">
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">无限次图片压缩</text>
						</view>
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">批量压缩处理</text>
						</view>
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">高级压缩算法</text>
						</view>
						<view class="privilege-item">
							<view class="check-icon">
								<text class="check-mark">✓</text>
							</view>
							<text class="privilege-text">专属会员服务</text>
						</view>
					</view>
					<!-- 分隔线 -->
					<view class="divider-line"></view>
					<view class="member-validity">
						<text>会员有效期: 9999-12-31</text>
					</view>
					<button class="renew-btn">续费会员</button>
				</view>
			</view>

			<!-- 功能菜单 -->
			<view class="menu-section">
				<view class="menu-item neumorphism" @tap="handleMenuClick('order')">
					<view class="menu-icon">
						<image src="/static/order.svg" mode="aspectFit" class="icon-image"></image>
					</view>
					<text class="menu-text">订单查询</text>
				</view>
				<view class="menu-item neumorphism" @tap="handleMenuClick('feedback')">
					<view class="menu-icon">
						<image src="/static/feedback.svg" mode="aspectFit" class="icon-image"></image>
					</view>
					<text class="menu-text">联系我们</text>
				</view>

				<view class="menu-item neumorphism" @tap="handleMenuClick('about')">
					<view class="menu-icon">
						<image src="/static/about.svg" mode="aspectFit" class="icon-image"></image>
					</view>
					<text class="menu-text">关于我们</text>
				</view>
			</view>


		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			navHeight: 0
		}
	},
	async onReady() {
		// 获取状态栏高度
		const windowInfo = uni.getWindowInfo()
		this.statusBarHeight = windowInfo.statusBarHeight
		// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）
		this.navHeight = this.statusBarHeight + 44
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		// 处理菜单点击
		handleMenuClick(type) {
			if (type === 'order') {
				// 跳转到订单查询页面
				uni.navigateTo({
					url: '/pages/profile/orders'
				})
				return
			}

			if (type === 'feedback') {
				// 联系我们 - 询问是否复制客服微信号
				uni.showModal({
					title: '客服微信',
					content: '客服微信号：gbw6646\n是否复制到剪贴板？',
					confirmText: '复制',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 用户点击复制
							uni.setClipboardData({
								data: 'gbw6646',
								success: () => {
									uni.showToast({
										title: '已复制到剪贴板',
										icon: 'success',
										duration: 2000
									})
								}
							})
						}
					}
				})
				return
			}

			if (type === 'about') {
				// 关于我们 - 显示应用信息
				uni.showModal({
					title: '关于我们',
					content: `图像压缩花花版 v1.0

🌸 让图片压缩变得简单高效

📱 支持多种压缩方式
💾 智能优化图片大小
✨ 为您节省珍贵的存储空间

谢谢使用，祝您生活愉快！`,
					showCancel: false,
					confirmText: '知道了'
				})
				return
			}
		}
	}
}
</script>

<style lang="scss">
// 使用uni.scss中定义的主题色变量
$primary-color: $uni-color-primary;
$primary-gradient: $theme-color-primary-gradient;
$bg-color: $uni-bg-color-grey;
$text-primary: $uni-text-color;
$text-secondary: $theme-text-secondary;
$text-tertiary: $uni-text-color-grey;
$border-color: $uni-border-color;
$shadow-dark: $theme-shadow-dark;
$shadow-light: $theme-shadow-light;

page {
	background-color: $bg-color;
}

// 新拟物风格的混入
@mixin neumorphism {
	background: $bg-color;
	box-shadow: 12px 12px 24px $shadow-dark,
				-8px -8px 20px $shadow-light,
				inset 2px 2px 4px rgba(255, 255, 255, 0.5),
				inset -2px -2px 4px rgba(0, 0, 0, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

// 磨砂玻璃风格的混入
@mixin glassmorphism {
	background: rgba($bg-color, 0.98);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.neumorphism {
	@include neumorphism;
}

.glassmorphism {
	@include glassmorphism;
}

.profile-container {
	padding: 30rpx;

	.custom-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		padding: 0 30rpx;

		.nav-content {
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.back-button-container {
				min-width: 44px;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.back-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 44px;
					height: 44px;
					background: transparent !important;
					background-color: transparent !important;
					border: none !important;
					border-radius: 0 !important;
					box-shadow: none !important;
					padding: 0;
					margin: 0;
					outline: none;
					transition: opacity 0.2s ease;

					&::after {
						display: none !important;
					}

					&:active {
						opacity: 0.6;
						background: transparent !important;
						background-color: transparent !important;
					}

					.back-icon {
						font-size: 36px;
						color: #000000;
						font-weight: normal;
						line-height: 1;
						margin-left: -2px; // 微调位置，使箭头更居中
					}
				}
			}

			.nav-title {
				font-size: 32rpx;
				color: $text-primary;
				font-weight: 500;
			}

			.nav-right-buttons {
				/* 保留容器用于布局平衡 */
				min-width: 44px;
			}
		}
	}

	.main-content {
		position: relative;
		width: 100%;
		padding-bottom: 40rpx;
		margin-top: -40rpx;

		.user-info-card {
			border-radius: 30rpx;
			padding: 30rpx;
			margin: 20rpx 0;
			color: #333333;

			.user-header {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;

				.user-avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 60rpx;
					overflow: hidden;
					margin-right: 30rpx;

					.avatar-image {
						width: 100%;
						height: 100%;
					}
				}

				.user-details {
					flex: 1;

					.user-name {
						font-size: 36rpx;
						color: $text-primary;
						font-weight: 500;
						margin-bottom: 10rpx;
					}

					.member-badge {
						background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
						color: white;
						font-size: 24rpx;
						padding: 8rpx 16rpx;
						border-radius: 20rpx;
						display: inline-block;
						font-weight: 500;
					}
				}

				.member-icon {
					.crown-image {
						width: 90rpx;
						height: 90rpx;
					}
				}
			}

			.member-section {
				border-top: 1px solid #f0f0f0;
				padding-top: 30rpx;

				.member-header {
					text-align: center;
					margin-bottom: 20rpx;

					.member-title {
						font-size: 40rpx;
						font-weight: bold;
						display: block;
						margin-bottom: 10rpx;
					}

					.member-subtitle {
						font-size: 28rpx;
						color: #666666;
					}
				}

				.divider-line {
					width: 100%;
					height: 1px;
					border-top: 1px solid #f0f0f0;
					margin: 20rpx 0;
				}

				.privileges-grid {
					display: grid;
					grid-template-columns: 1fr 1fr;
					gap: 20rpx;
					margin-bottom: 30rpx;
					align-items: center;
					justify-items: center;
					padding: 0 20rpx;

					.privilege-item {
						display: flex;
						align-items: center;
						justify-content: flex-start;
						width: 100%;
						padding-left: 60rpx;

						.check-icon {
							width: 32rpx;
							height: 32rpx;
							margin-right: 12rpx;
							background: linear-gradient(135deg, #07C160, #06AD56);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							flex-shrink: 0;

							.check-mark {
								color: white;
								font-size: 20rpx;
								font-weight: bold;
							}
						}

						.privilege-text {
							font-size: 26rpx;
							text-align: left;
							line-height: 1.4;
							white-space: nowrap;
							flex: 1;
						}
					}
				}

				.member-validity {
					text-align: center;
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 30rpx;
				}

				.renew-btn {
					width: 100%;
					height: 80rpx;
					background: linear-gradient(135deg, #07C160, #06AD56);
					border: none;
					border-radius: 40rpx;
					color: white;
					font-size: 32rpx;
					font-weight: 500;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.3s ease;
					box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);

					&:active {
						background: linear-gradient(135deg, #06AD56, #059C4F);
						transform: scale(0.98);
					}
				}
			}
		}

		.menu-section {
			margin: 30rpx 0;

			.menu-item {
				display: flex;
				align-items: center;
				padding: 30rpx;
				margin-bottom: 20rpx;
				border-radius: 20rpx;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				&:last-child {
					margin-bottom: 0;
				}

				.menu-icon {
					width: 60rpx;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 30rpx;

					.icon-image {
						width: 48rpx;
						height: 48rpx;
					}
				}

				.menu-text {
					font-size: 32rpx;
					color: $text-primary;
					font-weight: 400;
				}
			}
		}


	}
}
</style>
