-- 弹幕分享数据库初始化脚本
-- 适用于 MySQL 8.0.36+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS danmu_share
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE danmu_share;

-- 创建弹幕分享表
CREATE TABLE IF NOT EXISTS danmu_shares (
    share_id VARCHAR(8) PRIMARY KEY COMMENT '分享ID（8位随机字符）',

    -- 弹幕配置数据
    danmu_text VARCHAR(500) NOT NULL COMMENT '弹幕文字内容',
    style_type VARCHAR(40) NOT NULL COMMENT '样式类型',
    speed_class VARCHAR(40) NOT NULL COMMENT '速度等级',
    size_class VARCHAR(40) NOT NULL COMMENT '字体大小',
    font_class VARCHAR(40) NOT NULL COMMENT '字体类型',
    running_mode VARCHAR(40) NOT NULL COMMENT '运行模式',

    -- 自定义样式（当style_type为custom时使用）
    custom_text_color VARCHAR(7) COMMENT '自定义文字颜色',
    custom_bg_color VARCHAR(7) COMMENT '自定义背景颜色',
    custom_shake_level VARCHAR(40) COMMENT '自定义抖动级别',
    custom_shadow_type VARCHAR(40) COMMENT '自定义阴影类型',

    -- 分享信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分享创建时间',
    expires_at TIMESTAMP NULL COMMENT '分享过期时间（NULL表示永不过期）',
    access_count INT DEFAULT 0 COMMENT '被访问次数',

    -- 创建者信息
    creator_ip VARCHAR(45) COMMENT '创建者IP地址（支持IPv6）',
    creator_openid VARCHAR(64) COMMENT '创建者微信openid（可选）',
    creator_user_agent TEXT COMMENT '创建者用户代理字符串',

    -- 索引
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at),
    INDEX idx_access_count (access_count),
    INDEX idx_creator_ip (creator_ip),
    INDEX idx_creator_openid (creator_openid)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='弹幕分享表';

-- 创建用户（可选，用于安全访问）
-- CREATE USER 'danmu_user'@'localhost' IDENTIFIED BY 'your_secure_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON danmu_share.* TO 'danmu_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 插入示例数据（可选）
INSERT INTO danmu_shares (
    share_id, danmu_text, style_type, speed_class, size_class, font_class, running_mode,
    expires_at, creator_ip, creator_user_agent
) VALUES
(
    'demo1234',
    '示例弹幕文字',
    'black-white',
    'speed-normal',
    'size-normal',
    'font-default',
    'normal',
    DATE_ADD(NOW(), INTERVAL 30 DAY),
    '127.0.0.1',
    'Mozilla/5.0 (示例浏览器)'
),
(
    'demo5678',
    '炫彩弹幕效果',
    'shake-effect',
    'speed-fast',
    'size-large',
    'font-fashion',
    'mirror',
    DATE_ADD(NOW(), INTERVAL 30 DAY),
    '127.0.0.1',
    'Mozilla/5.0 (示例浏览器)'
),
(
    'custom99',
    '自定义样式弹幕',
    'custom',
    'speed-normal',
    'size-normal',
    'font-default',
    'normal',
    DATE_ADD(NOW(), INTERVAL 30 DAY),
    '127.0.0.1',
    'Mozilla/5.0 (示例浏览器)'
);

-- 查看表结构
DESCRIBE danmu_shares;

-- 查看示例数据
SELECT
    share_id,
    danmu_text,
    style_type,
    speed_class,
    size_class,
    font_class,
    running_mode,
    created_at,
    expires_at,
    access_count
FROM danmu_shares;

-- 清理过期分享的存储过程（可选）
DELIMITER //
CREATE PROCEDURE CleanExpiredShares()
BEGIN
    DELETE FROM danmu_shares
    WHERE expires_at IS NOT NULL
    AND expires_at < NOW();

    SELECT ROW_COUNT() as deleted_count;
END //
DELIMITER ;

-- 获取分享统计信息的视图（可选）
CREATE VIEW danmu_share_stats AS
SELECT
    COUNT(*) as total_shares,
    COUNT(CASE WHEN expires_at IS NULL OR expires_at > NOW() THEN 1 END) as active_shares,
    COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at <= NOW() THEN 1 END) as expired_shares,
    COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as recent_24h,
    COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_7d,
    AVG(access_count) as avg_access_count,
    MAX(access_count) as max_access_count,
    COUNT(CASE WHEN style_type = 'custom' THEN 1 END) as custom_style_count,
    COUNT(CASE WHEN style_type = 'shake-effect' THEN 1 END) as shake_effect_count
FROM danmu_shares;

-- 查看统计信息
SELECT * FROM danmu_share_stats;

-- 创建定时清理事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
--
-- CREATE EVENT IF NOT EXISTS cleanup_expired_shares
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanExpiredShares();

-- ========================================
-- 用户额度管理系统表结构
-- ========================================

-- 创建用户表（简化版）
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    openid VARCHAR(64) UNIQUE NOT NULL COMMENT '微信openid',

    -- 免费额度
    free_share_count INT DEFAULT 3 COMMENT '免费分享剩余次数',
    daily_fullscreen_count INT DEFAULT 3 COMMENT '每日免费全屏剩余次数',
    last_fullscreen_reset DATE DEFAULT (CURDATE()) COMMENT '上次全屏次数重置日期',

    -- 会员状态通过 user_purchases 表管理，这里不再存储VIP信息
    -- is_vip BOOLEAN DEFAULT FALSE COMMENT '是否为VIP会员',
    -- vip_expire_time TIMESTAMP NULL COMMENT 'VIP过期时间（NULL表示永久）',

    -- 统计信息
    total_share_count INT DEFAULT 0 COMMENT '总分享次数',
    total_fullscreen_count INT DEFAULT 0 COMMENT '总全屏使用次数',

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',

    -- 索引
    INDEX idx_openid (openid),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户信息表';

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    order_id VARCHAR(32) PRIMARY KEY COMMENT '订单ID',
    user_id INT NOT NULL COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信openid',

    -- 订单信息
    product_type ENUM('temp_vip', 'lifetime_vip') NOT NULL COMMENT '产品类型：temp_vip=24小时卡，lifetime_vip=终身会员',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额（元）',

    -- 支付信息
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending' COMMENT '支付状态',
    payment_method VARCHAR(50) COMMENT '支付方式',
    transaction_id VARCHAR(100) COMMENT '微信支付交易号',

    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
    paid_at TIMESTAMP NULL COMMENT '支付完成时间',
    expire_at TIMESTAMP NULL COMMENT '订单过期时间',

    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,

    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at),
    INDEX idx_paid_at (paid_at)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='订单表';

-- 创建使用记录表（用于统计和分析）
CREATE TABLE IF NOT EXISTS usage_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id INT COMMENT '用户ID（可为空，匿名用户）',
    openid VARCHAR(64) COMMENT '微信openid（可为空）',

    -- 使用信息
    action_type ENUM('share', 'fullscreen') NOT NULL COMMENT '操作类型',
    is_free BOOLEAN DEFAULT TRUE COMMENT '是否使用免费额度',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',

    -- 外键约束（可选）
    -- FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,

    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    INDEX idx_is_free (is_free)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='使用记录表';

-- 注释：不添加user_id字段，只通过openid进行权限控制
-- 这样设计更简单，只关注权限而不追踪具体分享归属
-- ALTER TABLE danmu_shares
-- ADD COLUMN user_id INT COMMENT '创建者用户ID',
-- ADD FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL;

-- 创建每日重置全屏次数的存储过程
DELIMITER //
CREATE PROCEDURE ResetDailyFullscreenCount()
BEGIN
    UPDATE users
    SET
        daily_fullscreen_count = 3,
        last_fullscreen_reset = CURDATE()
    WHERE last_fullscreen_reset < CURDATE();

    SELECT ROW_COUNT() as reset_count;
END //
DELIMITER ;

-- 创建检查和更新VIP状态的存储过程
DELIMITER //
CREATE PROCEDURE UpdateVipStatus()
BEGIN
    -- 清理过期的会员记录
    UPDATE user_memberships
    SET status = 'expired'
    WHERE status = 'active'
    AND expire_date IS NOT NULL
    AND expire_date < NOW();

    SELECT ROW_COUNT() as expired_memberships;
END //
DELIMITER ;

-- ========================================
-- 商品和会员管理系统
-- ========================================

-- 创建商品表（简化版，包含价格）
CREATE TABLE IF NOT EXISTS products (
    product_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    product_code VARCHAR(50) UNIQUE NOT NULL COMMENT '商品代码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '价格（元）',
    product_type ENUM('temp_vip', 'lifetime_vip') NOT NULL COMMENT '商品类型',
    duration_days INT COMMENT '有效期天数（NULL=永久）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_product_code (product_code),
    INDEX idx_product_type (product_type),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='商品表';

-- 创建用户会员表
CREATE TABLE IF NOT EXISTS user_memberships (
    membership_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会员记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    membership_type ENUM('temp_vip', 'lifetime_vip') NOT NULL COMMENT '会员类型',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    expire_date TIMESTAMP NULL COMMENT '过期时间（NULL=永久）',
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active' COMMENT '状态',
    order_id VARCHAR(32) COMMENT '关联订单ID',

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,

    -- 索引
    INDEX idx_user_status (user_id, status),
    INDEX idx_expire_date (expire_date),
    INDEX idx_membership_type (membership_type),
    INDEX idx_order_id (order_id)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户会员表';

-- 删除用户购买记录表（功能合并到user_memberships表）
-- CREATE TABLE IF NOT EXISTS user_purchases (...)

-- 插入默认商品（包含价格）
INSERT INTO products (product_code, product_name, description, price, product_type, duration_days, sort_order) VALUES
('temp_vip_24h', '24小时无限使用', '24小时内无限制使用分享和全屏功能', 2.99, 'temp_vip', 1, 1),
('lifetime_vip', '终身会员', '永久无限制使用所有功能', 19.90, 'lifetime_vip', NULL, 2);

-- 查看创建的表和结构
SHOW TABLES;
SHOW CREATE TABLE danmu_shares;
SHOW CREATE TABLE users;
SHOW CREATE TABLE orders;
SHOW CREATE TABLE usage_logs;
SHOW CREATE TABLE product_configs;
