const crypto = require('crypto');
const https = require('https');
const fs = require('fs');
const path = require('path');
const CertManager = require('./certManager');

/**
 * 微信支付平台证书管理服务
 */
class WechatCertService {
  constructor(merchantId = 'guofuli1700692997') {
    this.merchantId = merchantId;
    this.config = CertManager.getMerchantConfig(merchantId);
    this.platformCerts = new Map(); // 缓存平台证书
    this.certCachePath = path.join(__dirname, '../cache/platform_certs');
    
    // 确保缓存目录存在
    this.ensureCacheDir();
  }

  /**
   * 确保缓存目录存在
   */
  ensureCacheDir() {
    try {
      if (!fs.existsSync(this.certCachePath)) {
        fs.mkdirSync(this.certCachePath, { recursive: true });
      }
    } catch (error) {
      console.error('创建证书缓存目录失败:', error);
    }
  }

  /**
   * 生成请求签名（用于调用微信支付API）
   * @param {string} method HTTP方法
   * @param {string} url 请求URL路径
   * @param {string} body 请求体
   * @returns {string} 签名
   */
  generateRequestSignature(method, url, body = '') {
    try {
      const timestamp = Math.floor(Date.now() / 1000);
      const nonce = crypto.randomBytes(16).toString('hex');
      
      // 构造签名字符串
      const message = `${method}\n${url}\n${timestamp}\n${nonce}\n${body}\n`;
      
      // 读取商户私钥
      const privateKeyPath = CertManager.getCertPath(this.merchantId, 'key');
      const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
      
      // 使用RSA-SHA256签名
      const sign = crypto.createSign('RSA-SHA256');
      sign.update(message, 'utf8');
      const signature = sign.sign(privateKey, 'base64');
      
      return {
        timestamp,
        nonce,
        signature,
        serialNo: this.config.serialNo
      };
      
    } catch (error) {
      console.error('生成请求签名失败:', error);
      throw error;
    }
  }

  /**
   * 调用微信支付API获取平台证书
   * @returns {Array} 平台证书列表
   */
  async fetchPlatformCertificates() {
    try {
      const url = '/v3/certificates';
      const method = 'GET';
      
      // 生成请求签名
      const { timestamp, nonce, signature, serialNo } = this.generateRequestSignature(method, url);
      
      // 构造Authorization头
      const authorization = `WECHATPAY2-SHA256-RSA2048 mchid="${this.config.merchantId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${serialNo}"`;
      
      return new Promise((resolve, reject) => {
        const options = {
          hostname: 'api.mch.weixin.qq.com',
          port: 443,
          path: url,
          method: method,
          headers: {
            'Authorization': authorization,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'danmu-server/1.0.0'
          }
        };
        
        const req = https.request(options, (res) => {
          let data = '';
          
          res.on('data', (chunk) => {
            data += chunk;
          });
          
          res.on('end', () => {
            try {
              if (res.statusCode === 200) {
                const result = JSON.parse(data);
                console.log('✅ 获取微信支付平台证书成功');
                resolve(result.data || []);
              } else {
                console.error('❌ 获取平台证书失败:', res.statusCode, data);
                reject(new Error(`HTTP ${res.statusCode}: ${data}`));
              }
            } catch (error) {
              reject(error);
            }
          });
        });
        
        req.on('error', (error) => {
          console.error('❌ 请求微信支付API失败:', error);
          reject(error);
        });
        
        req.end();
      });
      
    } catch (error) {
      console.error('❌ 获取平台证书失败:', error);
      throw error;
    }
  }

  /**
   * 解密平台证书
   * @param {object} encryptedCert 加密的证书数据
   * @returns {string} 解密后的证书内容
   */
  decryptPlatformCert(encryptedCert) {
    try {
      const { ciphertext, nonce, associated_data } = encryptedCert;
      
      // 使用AES-256-GCM解密
      const decipher = crypto.createDecipheriv('aes-256-gcm', this.config.apiV3Key, nonce);
      
      // 设置认证标签（最后16字节）
      const authTag = Buffer.from(ciphertext.slice(-32), 'base64');
      decipher.setAuthTag(authTag);
      
      // 设置附加认证数据
      decipher.setAAD(Buffer.from(associated_data, 'utf8'));
      
      // 解密证书内容（去掉最后32字节的认证标签）
      const encryptedContent = ciphertext.slice(0, -32);
      let decrypted = decipher.update(encryptedContent, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
      
    } catch (error) {
      console.error('❌ 解密平台证书失败:', error);
      throw error;
    }
  }

  /**
   * 从证书内容中提取公钥
   * @param {string} certContent 证书内容
   * @returns {string} 公钥
   */
  extractPublicKeyFromCert(certContent) {
    try {
      // 使用Node.js的crypto模块从证书中提取公钥
      const cert = crypto.createPublicKey(certContent);
      return cert.export({ type: 'spki', format: 'pem' });
    } catch (error) {
      console.error('❌ 从证书中提取公钥失败:', error);
      throw error;
    }
  }

  /**
   * 缓存平台证书到本地
   * @param {string} serialNo 证书序列号
   * @param {string} certContent 证书内容
   * @param {string} publicKey 公钥
   */
  cachePlatformCert(serialNo, certContent, publicKey) {
    try {
      const certFile = path.join(this.certCachePath, `${serialNo}.pem`);
      const keyFile = path.join(this.certCachePath, `${serialNo}_public.pem`);
      
      fs.writeFileSync(certFile, certContent);
      fs.writeFileSync(keyFile, publicKey);
      
      // 内存缓存
      this.platformCerts.set(serialNo, {
        certContent,
        publicKey,
        cachedAt: Date.now()
      });
      
      console.log(`✅ 平台证书已缓存: ${serialNo}`);
      
    } catch (error) {
      console.error('❌ 缓存平台证书失败:', error);
    }
  }

  /**
   * 从缓存中加载平台证书
   * @param {string} serialNo 证书序列号
   * @returns {object|null} 证书信息
   */
  loadCachedPlatformCert(serialNo) {
    try {
      // 先检查内存缓存
      if (this.platformCerts.has(serialNo)) {
        const cached = this.platformCerts.get(serialNo);
        // 检查缓存是否过期（24小时）
        if (Date.now() - cached.cachedAt < 24 * 60 * 60 * 1000) {
          return cached;
        }
      }
      
      // 检查文件缓存
      const keyFile = path.join(this.certCachePath, `${serialNo}_public.pem`);
      if (fs.existsSync(keyFile)) {
        const publicKey = fs.readFileSync(keyFile, 'utf8');
        
        // 更新内存缓存
        const certInfo = {
          publicKey,
          cachedAt: Date.now()
        };
        this.platformCerts.set(serialNo, certInfo);
        
        return certInfo;
      }
      
      return null;
      
    } catch (error) {
      console.error('❌ 加载缓存的平台证书失败:', error);
      return null;
    }
  }

  /**
   * 获取平台证书公钥
   * @param {string} serialNo 证书序列号
   * @returns {string|null} 公钥
   */
  async getPlatformPublicKey(serialNo) {
    try {
      // 先尝试从缓存加载
      let certInfo = this.loadCachedPlatformCert(serialNo);
      
      if (certInfo && certInfo.publicKey) {
        console.log(`✅ 从缓存获取平台证书公钥: ${serialNo}`);
        return certInfo.publicKey;
      }
      
      // 缓存中没有，从微信支付API获取
      console.log(`🔄 从微信支付API获取平台证书: ${serialNo}`);
      const certificates = await this.fetchPlatformCertificates();
      
      for (const cert of certificates) {
        if (cert.serial_no === serialNo) {
          // 解密证书
          const certContent = this.decryptPlatformCert(cert.encrypt_certificate);
          
          // 提取公钥
          const publicKey = this.extractPublicKeyFromCert(certContent);
          
          // 缓存证书
          this.cachePlatformCert(serialNo, certContent, publicKey);
          
          return publicKey;
        }
      }
      
      console.error(`❌ 未找到序列号为 ${serialNo} 的平台证书`);
      return null;
      
    } catch (error) {
      console.error('❌ 获取平台证书公钥失败:', error);
      return null;
    }
  }

  /**
   * 清理过期的证书缓存
   */
  cleanExpiredCache() {
    try {
      const now = Date.now();
      const expireTime = 7 * 24 * 60 * 60 * 1000; // 7天
      
      // 清理内存缓存
      for (const [serialNo, certInfo] of this.platformCerts.entries()) {
        if (now - certInfo.cachedAt > expireTime) {
          this.platformCerts.delete(serialNo);
          console.log(`🗑️ 清理过期的内存缓存: ${serialNo}`);
        }
      }
      
      // 清理文件缓存
      if (fs.existsSync(this.certCachePath)) {
        const files = fs.readdirSync(this.certCachePath);
        for (const file of files) {
          const filePath = path.join(this.certCachePath, file);
          const stats = fs.statSync(filePath);
          if (now - stats.mtime.getTime() > expireTime) {
            fs.unlinkSync(filePath);
            console.log(`🗑️ 清理过期的文件缓存: ${file}`);
          }
        }
      }
      
    } catch (error) {
      console.error('❌ 清理证书缓存失败:', error);
    }
  }
}

module.exports = WechatCertService;
