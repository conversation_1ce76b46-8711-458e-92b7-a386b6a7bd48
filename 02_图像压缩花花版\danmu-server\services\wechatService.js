const https = require('https');
const MerchantMatcher = require('./merchantMatcher');

/**
 * 微信小程序服务
 */
class WechatService {
  constructor() {
    // 保留默认配置作为兜底
    this.defaultAppId = 'wxbd7109f36cd77b2b';
    this.defaultAppSecret = 'f97e7c1a1cc722fa5168e848bd21e593';

    // access_token缓存
    this.accessTokenCache = new Map();
  }

  /**
   * 根据AppID获取微信配置
   * @param {string} appId 小程序AppID
   * @returns {object} 微信配置
   */
  getWechatConfigByAppId(appId) {
    try {
      if (!appId) {
        console.warn('⚠️ AppID为空，使用默认微信配置');
        return {
          app_id: this.defaultAppId,
          app_secret: this.defaultAppSecret
        };
      }

      // 通过MerchantMatcher获取商户配置
      const merchantConfig = MerchantMatcher.getMerchantConfigByAppId(appId);

      if (merchantConfig && merchantConfig.wechat_config) {
        console.log(`🎯 AppID ${appId} 匹配到微信配置:`, {
          app_id: merchantConfig.wechat_config.app_id,
          app_secret: merchantConfig.wechat_config.app_secret ? '***' : undefined
        });
        return merchantConfig.wechat_config;
      }

      console.warn(`⚠️ 未找到AppID ${appId} 对应的微信配置，使用默认配置`);
      return {
        app_id: this.defaultAppId,
        app_secret: this.defaultAppSecret
      };
    } catch (error) {
      console.error('❌ 获取微信配置失败:', error);
      return {
        app_id: this.defaultAppId,
        app_secret: this.defaultAppSecret
      };
    }
  }

  /**
   * 通过code获取openid和session_key
   * @param {string} code 微信登录code
   * @param {string} appId 小程序AppID（可选）
   * @returns {Promise<object>} 包含openid和session_key的对象
   */
  async getOpenidByCode(code, appId = null) {
    // 获取对应的微信配置
    const wechatConfig = this.getWechatConfigByAppId(appId);

    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${wechatConfig.app_id}&secret=${wechatConfig.app_secret}&js_code=${code}&grant_type=authorization_code`;
    
    return new Promise((resolve, reject) => {
      https.get(url, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            
            if (result.errcode) {
              console.error('微信API错误:', result);
              reject(new Error(`微信API错误: ${result.errcode} - ${result.errmsg}`));
            } else {
              console.log('✅ 微信登录成功:', {
                appId: wechatConfig.app_id,
                openid: result.openid,
                session_key: result.session_key ? '***' : undefined
              });
              resolve(result);
            }
          } catch (error) {
            console.error('解析微信API响应失败:', error);
            reject(new Error('解析微信API响应失败'));
          }
        });
      }).on('error', (error) => {
        console.error('请求微信API失败:', error);
        reject(new Error('请求微信API失败'));
      });
    });
  }

  /**
   * 验证用户信息签名
   * @param {string} rawData 原始数据
   * @param {string} signature 签名
   * @param {string} sessionKey session_key
   * @returns {boolean} 验证结果
   */
  verifyUserInfo(rawData, signature, sessionKey) {
    const crypto = require('crypto');
    const hash = crypto.createHmac('sha1', sessionKey).update(rawData).digest('hex');
    return hash === signature;
  }

  /**
   * 获取access_token
   * @param {string} appId 小程序AppID（可选）
   * @returns {Promise<string>} access_token
   */
  async getAccessToken(appId = null) {
    try {
      // 获取对应的微信配置
      const wechatConfig = this.getWechatConfigByAppId(appId);
      const cacheKey = wechatConfig.app_id;

      // 检查缓存
      const cached = this.accessTokenCache.get(cacheKey);
      if (cached && cached.expires_at > Date.now()) {
        console.log('✅ 使用缓存的access_token');
        return cached.access_token;
      }

      console.log('🔄 获取新的access_token');

      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${wechatConfig.app_id}&secret=${wechatConfig.app_secret}`;

      return new Promise((resolve, reject) => {
        https.get(url, (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const result = JSON.parse(data);

              if (result.errcode) {
                console.error('❌ 获取access_token失败:', result);
                reject(new Error(`微信API错误: ${result.errcode} - ${result.errmsg}`));
              } else {
                // 缓存access_token（提前5分钟过期）
                const expiresAt = Date.now() + (result.expires_in - 300) * 1000;
                this.accessTokenCache.set(cacheKey, {
                  access_token: result.access_token,
                  expires_at: expiresAt
                });

                console.log('✅ 获取access_token成功');
                resolve(result.access_token);
              }
            } catch (error) {
              console.error('❌ 解析access_token响应失败:', error);
              reject(new Error('解析API响应失败'));
            }
          });
        }).on('error', (error) => {
          console.error('❌ 请求access_token失败:', error);
          reject(new Error('请求微信API失败'));
        });
      });
    } catch (error) {
      console.error('❌ 获取access_token异常:', error);
      throw error;
    }
  }

  /**
   * 解密用户敏感数据
   * @param {string} encryptedData 加密数据
   * @param {string} iv 初始向量
   * @param {string} sessionKey session_key
   * @returns {object} 解密后的数据
   */
  decryptData(encryptedData, iv, sessionKey) {
    const crypto = require('crypto');

    try {
      const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');

      const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true);

      let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
      decrypted += decipher.final('utf8');

      return JSON.parse(decrypted);
    } catch (error) {
      console.error('解密用户数据失败:', error);
      throw new Error('解密用户数据失败');
    }
  }
}

module.exports = new WechatService();
