const { pool } = require('../config/database');

class UserMembership {
  /**
   * 创建用户会员记录
   * @param {number} userId 用户ID
   * @param {string} membershipType 会员类型
   * @param {string} orderId 订单ID
   * @param {number} durationDays 有效期天数（null=永久）
   * @returns {object} 创建结果
   */
  static async create(userId, membershipType, orderId, durationDays = null) {
    const expireDate = durationDays 
      ? new Date(Date.now() + durationDays * 24 * 60 * 60 * 1000)
      : null;

    const [result] = await pool.execute(
      `INSERT INTO user_memberships (user_id, membership_type, expire_date, order_id)
       VALUES (?, ?, ?, ?)`,
      [userId, membershipType, expireDate, orderId]
    );

    return {
      membershipId: result.insertId,
      userId,
      membershipType,
      expireDate,
      orderId
    };
  }

  /**
   * 获取用户当前有效的会员记录
   * @param {number} userId 用户ID
   * @returns {object|null} 会员记录
   */
  static async getActiveMembership(userId) {
    const [rows] = await pool.execute(
      `SELECT 
        membership_id, user_id, membership_type, start_date, expire_date, 
        status, order_id, created_at
       FROM user_memberships 
       WHERE user_id = ? 
         AND status = 'active'
         AND (expire_date IS NULL OR expire_date > NOW())
       ORDER BY 
         CASE WHEN expire_date IS NULL THEN 1 ELSE 0 END DESC,
         expire_date DESC
       LIMIT 1`,
      [userId]
    );

    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 获取用户所有会员记录
   * @param {number} userId 用户ID
   * @param {number} limit 限制数量
   * @returns {Array} 会员记录列表
   */
  static async getUserMemberships(userId, limit = 10) {
    const [rows] = await pool.execute(
      `SELECT 
        membership_id, user_id, membership_type, start_date, expire_date, 
        status, order_id, created_at
       FROM user_memberships 
       WHERE user_id = ?
       ORDER BY created_at DESC
       LIMIT ?`,
      [userId, limit]
    );

    return rows;
  }

  /**
   * 检查用户是否为VIP
   * @param {number} userId 用户ID
   * @returns {boolean} 是否为VIP
   */
  static async isVip(userId) {
    const membership = await this.getActiveMembership(userId);
    return membership !== null;
  }

  /**
   * 过期会员记录
   * @param {number} membershipId 会员记录ID
   * @returns {boolean} 是否成功
   */
  static async expire(membershipId) {
    const [result] = await pool.execute(
      'UPDATE user_memberships SET status = "expired", updated_at = NOW() WHERE membership_id = ?',
      [membershipId]
    );

    return result.affectedRows > 0;
  }

  /**
   * 取消会员记录
   * @param {number} membershipId 会员记录ID
   * @returns {boolean} 是否成功
   */
  static async cancel(membershipId) {
    const [result] = await pool.execute(
      'UPDATE user_memberships SET status = "cancelled", updated_at = NOW() WHERE membership_id = ?',
      [membershipId]
    );

    return result.affectedRows > 0;
  }

  /**
   * 清理过期的会员记录
   * @returns {number} 清理的记录数
   */
  static async cleanExpired() {
    const [result] = await pool.execute(
      `UPDATE user_memberships 
       SET status = 'expired', updated_at = NOW() 
       WHERE status = 'active' 
         AND expire_date IS NOT NULL 
         AND expire_date <= NOW()`
    );

    return result.affectedRows;
  }

  /**
   * 获取会员统计信息
   * @returns {object} 统计信息
   */
  static async getStats() {
    const connection = await pool.getConnection();

    try {
      const [totalMemberships] = await connection.execute(
        'SELECT COUNT(*) as count FROM user_memberships'
      );

      const [activeMemberships] = await connection.execute(
        `SELECT COUNT(*) as count FROM user_memberships 
         WHERE status = 'active' AND (expire_date IS NULL OR expire_date > NOW())`
      );

      const [membershipTypes] = await connection.execute(
        `SELECT 
          membership_type, 
          COUNT(*) as count,
          COUNT(CASE WHEN status = 'active' AND (expire_date IS NULL OR expire_date > NOW()) THEN 1 END) as active_count
         FROM user_memberships 
         GROUP BY membership_type`
      );

      const [recentMemberships] = await connection.execute(
        `SELECT COUNT(*) as count FROM user_memberships 
         WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)`
      );

      return {
        total: totalMemberships[0].count,
        active: activeMemberships[0].count,
        recent24h: recentMemberships[0].count,
        types: membershipTypes
      };
    } finally {
      connection.release();
    }
  }

  /**
   * 根据订单ID获取会员记录
   * @param {string} orderId 订单ID
   * @returns {object|null} 会员记录
   */
  static async getByOrderId(orderId) {
    const [rows] = await pool.execute(
      `SELECT 
        membership_id, user_id, membership_type, start_date, expire_date, 
        status, order_id, created_at
       FROM user_memberships 
       WHERE order_id = ?`,
      [orderId]
    );

    return rows.length > 0 ? rows[0] : null;
  }
}

module.exports = UserMembership;
