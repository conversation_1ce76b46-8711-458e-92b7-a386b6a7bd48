const express = require('express');
const router = express.Router();
const User = require('../models/User');
const UsageLog = require('../models/UsageLog');
const { formatSuccess, formatError, getClientIP } = require('../utils/helpers');

/**
 * 检查全屏使用权限
 * POST /api/fullscreen/check
 */
router.post('/check', async (req, res) => {
  try {
    const { userId, openid } = req.body;
    
    if (!userId && !openid) {
      return res.status(400).json(formatError(
        '缺少用户标识',
        'MISSING_USER_IDENTIFIER'
      ));
    }
    
    let user = null;
    let quota = null;
    
    if (userId) {
      try {
        quota = await User.getUserQuota(parseInt(userId));
        user = { userId: parseInt(userId) };
      } catch (error) {
        console.warn('获取用户额度失败:', error.message);
      }
    }
    
    // 如果没有用户ID或获取失败，尝试通过openid获取或创建用户
    if (!user && openid) {
      try {
        const userInfo = await User.getOrCreateUser(openid);
        quota = await User.getUserQuota(userInfo.user_id);
        user = { userId: userInfo.user_id };
      } catch (error) {
        console.warn('通过openid获取用户失败:', error.message);
      }
    }
    
    if (!quota) {
      // 匿名用户，允许使用但不记录额度
      return res.json(formatSuccess({
        canUseFullscreen: true,
        isAnonymous: true,
        reason: '匿名用户'
      }, '匿名用户可以使用全屏'));
    }
    
    const canUseFullscreen = quota.has_vip_access || quota.daily_fullscreen_count > 0;
    
    res.json(formatSuccess({
      canUseFullscreen: canUseFullscreen,
      isAnonymous: false,
      dailyFullscreenCount: quota.daily_fullscreen_count,
      hasVipAccess: quota.has_vip_access,
      isVip: quota.is_vip,
      tempVipExpireTime: quota.temp_vip_expire_time,
      reason: canUseFullscreen ? null : '今日全屏次数已用完，请开通会员'
    }, '检查全屏权限成功'));
    
  } catch (error) {
    console.error('检查全屏权限失败:', error);
    res.status(500).json(formatError(
      '检查全屏权限失败',
      'CHECK_FULLSCREEN_FAILED',
      error.message
    ));
  }
});

/**
 * 使用全屏功能
 * POST /api/fullscreen/use
 */
router.post('/use', async (req, res) => {
  try {
    const { userId, openid } = req.body;
    
    if (!userId && !openid) {
      return res.status(400).json(formatError(
        '缺少用户标识',
        'MISSING_USER_IDENTIFIER'
      ));
    }
    
    let user = null;
    let quota = null;
    
    if (userId) {
      try {
        quota = await User.getUserQuota(parseInt(userId));
        user = { userId: parseInt(userId) };
      } catch (error) {
        console.warn('获取用户额度失败:', error.message);
      }
    }
    
    // 如果没有用户ID或获取失败，尝试通过openid获取或创建用户
    if (!user && openid) {
      try {
        const userInfo = await User.getOrCreateUser(openid);
        quota = await User.getUserQuota(userInfo.user_id);
        user = { userId: userInfo.user_id };
      } catch (error) {
        console.warn('通过openid获取用户失败:', error.message);
      }
    }
    
    if (!user || !quota) {
      // 匿名用户不允许使用全屏功能，需要先登录
      return res.status(401).json(formatError(
        '请先登录后使用全屏功能',
        'LOGIN_REQUIRED'
      ));
    }
    
    // 检查是否可以使用全屏
    const canUseFullscreen = quota.has_vip_access || quota.daily_fullscreen_count > 0;
    
    if (!canUseFullscreen) {
      return res.status(403).json(formatError(
        '今日全屏次数不足，请开通会员',
        'INSUFFICIENT_FULLSCREEN_COUNT'
      ));
    }
    
    // 使用全屏次数（VIP用户不扣除次数）
    if (!quota.has_vip_access) {
      const useSuccess = await User.useFullscreenCount(user.userId);
      if (!useSuccess) {
        return res.status(403).json(formatError(
          '今日全屏次数不足',
          'INSUFFICIENT_FULLSCREEN_COUNT'
        ));
      }
    } else {
      // VIP用户只更新总使用次数
      await User.useFullscreenCount(user.userId);
    }
    
    // 记录使用日志
    await UsageLog.log({
      userId: user.userId,
      openid: openid,
      actionType: 'fullscreen',
      isFree: !quota.has_vip_access,
      ipAddress: getClientIP(req),
      userAgent: req.get('User-Agent')
    });
    
    // 返回更新后的额度信息
    const updatedQuota = await User.getUserQuota(user.userId);
    
    res.json(formatSuccess({
      success: true,
      isAnonymous: false,
      quota: {
        dailyFullscreenCount: updatedQuota.daily_fullscreen_count,
        hasVipAccess: updatedQuota.has_vip_access,
        isVip: updatedQuota.is_vip,
        tempVipExpireTime: updatedQuota.temp_vip_expire_time
      }
    }, '使用全屏功能成功'));
    
  } catch (error) {
    console.error('使用全屏功能失败:', error);
    res.status(500).json(formatError(
      '使用全屏功能失败',
      'USE_FULLSCREEN_FAILED',
      error.message
    ));
  }
});

/**
 * 获取全屏使用统计
 * GET /api/fullscreen/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    
    const stats = await UsageLog.getSystemStats(parseInt(days));
    const hourlyStats = await UsageLog.getHourlyStats(parseInt(days));
    const dailyTrend = await UsageLog.getDailyTrend(parseInt(days));
    
    res.json(formatSuccess({
      systemStats: stats,
      hourlyStats: hourlyStats,
      dailyTrend: dailyTrend
    }, '获取全屏统计成功'));
    
  } catch (error) {
    console.error('获取全屏统计失败:', error);
    res.status(500).json(formatError(
      '获取全屏统计失败',
      'GET_FULLSCREEN_STATS_FAILED',
      error.message
    ));
  }
});

module.exports = router;
