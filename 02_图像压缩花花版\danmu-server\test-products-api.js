/**
 * 测试产品价格API
 * 用于验证 /api/order/products 接口是否正常工作
 */

const express = require('express');
const cors = require('cors');
const Order = require('./models/Order');
const { formatSuccess, formatError } = require('./utils/helpers');

const app = express();
app.use(cors());
app.use(express.json());

// 测试获取产品配置的路由
app.get('/api/order/products', async (req, res) => {
  try {
    console.log('🔄 开始获取产品配置...');
    
    const products = await Order.getProductConfig();
    console.log('✅ 产品配置获取成功:', products);

    res.json(formatSuccess(products, '获取产品配置成功'));

  } catch (error) {
    console.error('❌ 获取产品配置失败:', error);
    res.status(500).json(formatError(
      '获取产品配置失败',
      'GET_PRODUCTS_FAILED',
      error.message
    ));
  }
});

// 启动测试服务器
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🚀 测试服务器启动在端口 ${PORT}`);
  console.log(`📡 测试URL: http://localhost:${PORT}/api/order/products`);
  
  // 自动测试API
  setTimeout(async () => {
    try {
      console.log('\n🧪 开始自动测试...');
      const products = await Order.getProductConfig();
      console.log('✅ 直接调用模型方法成功:', JSON.stringify(products, null, 2));
    } catch (error) {
      console.error('❌ 直接调用模型方法失败:', error);
    }
  }, 1000);
});
