-- 插入测试数据用于订单查询功能测试
USE danmu_share;

-- 插入测试用户
INSERT INTO users (openid, free_share_count, daily_fullscreen_count, total_share_count, total_fullscreen_count) VALUES
('test_openid_001', 3, 3, 0, 0),
('test_openid_002', 1, 2, 5, 8),
('test_openid_003', 0, 0, 10, 15);

-- 获取插入的用户ID
SET @user1_id = (SELECT user_id FROM users WHERE openid = 'test_openid_001');
SET @user2_id = (SELECT user_id FROM users WHERE openid = 'test_openid_002');
SET @user3_id = (SELECT user_id FROM users WHERE openid = 'test_openid_003');

-- 插入测试订单
INSERT INTO orders (order_id, user_id, openid, product_type, product_name, amount, payment_status, created_at, paid_at, expire_at) VALUES
-- 用户1的订单
('DM20240115001', @user1_id, 'test_openid_001', 'temp_vip', '24小时无限使用', 2.99, 'paid', '2024-01-15 14:30:25', '2024-01-15 14:31:10', DATE_ADD('2024-01-15 14:30:25', INTERVAL 30 MINUTE)),
('DM20240120002', @user1_id, 'test_openid_001', 'lifetime_vip', '终身会员', 19.90, 'pending', '2024-01-20 09:15:30', NULL, DATE_ADD('2024-01-20 09:15:30', INTERVAL 30 MINUTE)),

-- 用户2的订单
('DM20240118003', @user2_id, 'test_openid_002', 'temp_vip', '24小时无限使用', 2.99, 'paid', '2024-01-18 16:45:12', '2024-01-18 16:46:05', DATE_ADD('2024-01-18 16:45:12', INTERVAL 30 MINUTE)),
('DM20240119004', @user2_id, 'test_openid_002', 'temp_vip', '24小时无限使用', 2.99, 'failed', '2024-01-19 10:20:18', NULL, DATE_ADD('2024-01-19 10:20:18', INTERVAL 30 MINUTE)),
('DM20240121005', @user2_id, 'test_openid_002', 'lifetime_vip', '终身会员', 19.90, 'paid', '2024-01-21 13:55:40', '2024-01-21 13:56:22', DATE_ADD('2024-01-21 13:55:40', INTERVAL 30 MINUTE)),

-- 用户3的订单
('DM20240116006', @user3_id, 'test_openid_003', 'temp_vip', '24小时无限使用', 2.99, 'paid', '2024-01-16 11:30:15', '2024-01-16 11:31:08', DATE_ADD('2024-01-16 11:30:15', INTERVAL 30 MINUTE)),
('DM20240117007', @user3_id, 'test_openid_003', 'temp_vip', '24小时无限使用', 2.99, 'paid', '2024-01-17 15:22:33', '2024-01-17 15:23:11', DATE_ADD('2024-01-17 15:22:33', INTERVAL 30 MINUTE)),
('DM20240122008', @user3_id, 'test_openid_003', 'lifetime_vip', '终身会员', 19.90, 'pending', '2024-01-22 08:45:50', NULL, DATE_ADD('2024-01-22 08:45:50', INTERVAL 30 MINUTE));

-- 插入对应的会员记录（对于已支付的订单）
INSERT INTO user_memberships (user_id, membership_type, start_date, expire_date, status, order_id) VALUES
-- 用户1的会员记录
(@user1_id, 'temp_vip', '2024-01-15 14:31:10', DATE_ADD('2024-01-15 14:31:10', INTERVAL 1 DAY), 'expired', 'DM20240115001'),

-- 用户2的会员记录
(@user2_id, 'temp_vip', '2024-01-18 16:46:05', DATE_ADD('2024-01-18 16:46:05', INTERVAL 1 DAY), 'expired', 'DM20240118003'),
(@user2_id, 'lifetime_vip', '2024-01-21 13:56:22', NULL, 'active', 'DM20240121005'),

-- 用户3的会员记录
(@user3_id, 'temp_vip', '2024-01-16 11:31:08', DATE_ADD('2024-01-16 11:31:08', INTERVAL 1 DAY), 'expired', 'DM20240116006'),
(@user3_id, 'temp_vip', '2024-01-17 15:23:11', DATE_ADD('2024-01-17 15:23:11', INTERVAL 1 DAY), 'expired', 'DM20240117007');

-- 插入使用记录
INSERT INTO usage_logs (user_id, openid, action_type, is_free, ip_address, created_at) VALUES
-- 用户1的使用记录
(@user1_id, 'test_openid_001', 'share', TRUE, '*************', '2024-01-15 10:30:00'),
(@user1_id, 'test_openid_001', 'fullscreen', TRUE, '*************', '2024-01-15 11:15:00'),
(@user1_id, 'test_openid_001', 'fullscreen', FALSE, '*************', '2024-01-15 15:30:00'),

-- 用户2的使用记录
(@user2_id, 'test_openid_002', 'share', TRUE, '*************', '2024-01-18 09:20:00'),
(@user2_id, 'test_openid_002', 'share', TRUE, '*************', '2024-01-18 10:45:00'),
(@user2_id, 'test_openid_002', 'fullscreen', FALSE, '*************', '2024-01-21 14:30:00'),
(@user2_id, 'test_openid_002', 'fullscreen', FALSE, '*************', '2024-01-21 16:20:00'),

-- 用户3的使用记录
(@user3_id, 'test_openid_003', 'share', TRUE, '*************', '2024-01-16 08:30:00'),
(@user3_id, 'test_openid_003', 'share', FALSE, '*************', '2024-01-16 12:15:00'),
(@user3_id, 'test_openid_003', 'fullscreen', FALSE, '*************', '2024-01-17 16:45:00');

-- 查看插入的测试数据
SELECT '=== 用户信息 ===' as info;
SELECT user_id, openid, free_share_count, daily_fullscreen_count, total_share_count, total_fullscreen_count, created_at FROM users;

SELECT '=== 订单信息 ===' as info;
SELECT order_id, user_id, openid, product_type, product_name, amount, payment_status, created_at, paid_at FROM orders ORDER BY created_at DESC;

SELECT '=== 会员信息 ===' as info;
SELECT membership_id, user_id, membership_type, start_date, expire_date, status, order_id FROM user_memberships ORDER BY created_at DESC;

SELECT '=== 使用记录 ===' as info;
SELECT log_id, user_id, openid, action_type, is_free, created_at FROM usage_logs ORDER BY created_at DESC LIMIT 10;
