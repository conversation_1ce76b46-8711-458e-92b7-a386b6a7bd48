# 弹幕分享服务器系统设计文档

## 🎯 系统概述

弹幕分享服务器为手持弹幕小程序提供完整的后端支持，包括用户额度管理、分享功能、付费服务和数据统计。

## 💰 商业模式

### 免费额度
- **分享功能**: 每个新用户总共3次免费分享机会（终身）
- **全屏功能**: 每天3次免费使用，每日0点自动重置

### 付费方案
- **24小时卡**: ¥2.99 - 24小时内无限制使用所有功能
- **终身会员**: ¥19.9 - 永久无限制使用所有功能

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   Express.js    │    │   MySQL 8.0     │
│                │    │   API 服务器     │    │   数据库        │
│ - 用户界面      │◄──►│                │◄──►│                │
│ - 弹幕显示      │    │ - 用户管理      │    │ - 用户数据      │
│ - 分享功能      │    │ - 额度控制      │    │ - 订单记录      │
│ - 支付集成      │    │ - 订单处理      │    │ - 使用日志      │
└─────────────────┘    │ - 定时任务      │    │ - 分享配置      │
                       └─────────────────┘    └─────────────────┘
```

## 📊 数据库设计

### 核心表结构

#### 1. users 表 - 用户信息
```sql
- user_id (主键)
- openid (微信唯一标识)
- nickname, avatar_url (用户信息)
- free_share_count (免费分享剩余次数)
- daily_fullscreen_count (每日免费全屏剩余次数)
- is_vip, vip_expire_time (VIP状态)
- temp_vip_expire_time (临时VIP过期时间)
```

#### 2. orders 表 - 订单管理
```sql
- order_id (订单号)
- user_id, openid (用户关联)
- product_type (产品类型: temp_vip/lifetime_vip)
- amount (金额)
- payment_status (支付状态)
- transaction_id (微信支付交易号)
```

#### 3. usage_logs 表 - 使用记录
```sql
- log_id (主键)
- user_id, openid (用户关联)
- action_type (操作类型: share/fullscreen)
- is_free (是否使用免费额度)
- ip_address, user_agent (客户端信息)
```

#### 4. danmu_shares 表 - 分享配置
```sql
- share_id (8位分享ID)
- user_id (创建者)
- danmu_text, style_type, speed_class... (弹幕配置)
- access_count (访问次数)
- expires_at (过期时间)
```

## 🔄 业务流程

### 用户注册/登录流程
1. 小程序获取微信 openid
2. 调用 `/api/user/login` 接口
3. 系统检查用户是否存在，不存在则创建新用户
4. 返回用户信息和当前额度状态

### 分享功能流程
1. 用户在小程序中配置弹幕样式
2. 点击分享按钮
3. 系统检查用户分享权限（VIP或免费次数）
4. 验证通过后生成8位分享ID
5. 保存分享配置到数据库
6. 返回分享链接给用户

### 全屏功能流程
1. 用户点击"开启全屏"按钮
2. 系统检查用户全屏权限（VIP或每日免费次数）
3. 验证通过后扣除相应次数
4. 记录使用日志
5. 跳转到全屏页面

### 付费流程
1. 用户选择付费产品（24小时卡或终身会员）
2. 调用 `/api/order/create` 创建订单
3. 集成微信支付（小程序端处理）
4. 支付成功后调用 `/api/order/pay-success`
5. 系统激活用户VIP权限

## ⚙️ 核心功能模块

### 1. 用户额度管理 (User.js)
- 用户注册和登录
- 免费额度检查和扣除
- VIP状态管理
- 每日次数重置

### 2. 订单管理 (Order.js)
- 订单创建和查询
- 支付状态更新
- VIP权限激活
- 订单统计分析

### 3. 使用记录 (UsageLog.js)
- 用户行为记录
- 使用统计分析
- 数据趋势分析
- 日志清理

### 4. 分享管理 (DanmuShare.js)
- 分享配置保存
- 分享链接生成
- 访问统计
- 过期清理

## 🕐 定时任务系统

### 自动化任务
1. **每日0点**: 重置所有用户的每日免费全屏次数
2. **每小时**: 清理过期订单（30分钟未支付）
3. **每天2点**: 清理过期的分享配置
4. **每周日3点**: 清理90天前的使用日志
5. **每小时30分**: 更新过期的VIP会员状态

### 手动维护
- 支持手动执行所有清理任务
- 提供系统状态查询接口
- 支持数据统计和分析

## 🔒 安全设计

### 数据验证
- 严格的输入参数验证
- SQL注入防护
- XSS攻击防护

### 权限控制
- 基于用户ID的权限验证
- VIP状态实时检查
- 额度使用原子性操作

### 数据保护
- 敏感信息加密存储
- 定期数据备份
- 访问日志记录

## 📈 性能优化

### 数据库优化
- 合理的索引设计
- 查询语句优化
- 连接池管理

### 缓存策略
- 用户状态缓存
- 热门分享缓存
- 统计数据缓存

### 监控告警
- 系统性能监控
- 错误日志收集
- 异常情况告警

## 🚀 扩展性设计

### 水平扩展
- 无状态API设计
- 数据库读写分离支持
- 负载均衡友好

### 功能扩展
- 模块化架构设计
- 插件式功能开发
- 版本兼容性保证

## 📊 数据分析

### 用户行为分析
- 使用频率统计
- 功能偏好分析
- 付费转化率

### 业务指标监控
- 日活跃用户数
- 付费用户比例
- 收入趋势分析

## 🔧 运维支持

### 部署方案
- Docker容器化部署
- PM2进程管理
- Nginx反向代理

### 监控运维
- 健康检查接口
- 系统状态监控
- 自动故障恢复

## 📝 开发规范

### 代码规范
- ESLint代码检查
- 统一的错误处理
- 完整的API文档

### 测试规范
- 单元测试覆盖
- 集成测试验证
- 性能测试评估

这个系统设计充分考虑了商业需求、技术实现和运维管理，为手持弹幕小程序提供了完整、稳定、可扩展的后端支持。
