const express = require('express');
const router = express.Router();
const User = require('../models/User');
const UsageLog = require('../models/UsageLog');
const { formatSuccess, formatError, getClientIP } = require('../utils/helpers');

/**
 * 用户登录/注册
 * POST /api/user/login
 */
router.post('/login', async (req, res) => {
  try {
    const { openid, userInfo } = req.body;
    
    if (!openid) {
      return res.status(400).json(formatError(
        '缺少openid参数',
        'MISSING_OPENID'
      ));
    }
    
    // 获取或创建用户
    const user = await User.getOrCreateUser(openid, userInfo);
    
    // 获取用户额度信息
    const quota = await User.getUserQuota(user.user_id);
    
    res.json(formatSuccess({
      user: {
        userId: user.user_id,
        openid: user.openid,
        nickname: user.nickname,
        avatarUrl: user.avatar_url,
        createdAt: user.created_at
      },
      quota: {
        freeShareCount: quota.free_share_count,
        dailyFullscreenCount: quota.daily_fullscreen_count,
        totalShareCount: quota.total_share_count,
        totalFullscreenCount: quota.total_fullscreen_count,
        hasVipAccess: quota.has_vip_access,
        isVip: quota.is_vip,
        vipExpireTime: quota.vip_expire_time,
        tempVipExpireTime: quota.temp_vip_expire_time
      }
    }, '登录成功'));
    
  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json(formatError(
      '登录失败',
      'LOGIN_FAILED',
      error.message
    ));
  }
});

/**
 * 获取用户额度信息
 * GET /api/user/quota/:userId
 */
router.get('/quota/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const quota = await User.getUserQuota(parseInt(userId));
    
    res.json(formatSuccess({
      freeShareCount: quota.free_share_count,
      dailyFullscreenCount: quota.daily_fullscreen_count,
      totalShareCount: quota.total_share_count,
      totalFullscreenCount: quota.total_fullscreen_count,
      hasVipAccess: quota.has_vip_access,
      isVip: quota.is_vip,
      vipExpireTime: quota.vip_expire_time,
      tempVipExpireTime: quota.temp_vip_expire_time
    }, '获取额度信息成功'));
    
  } catch (error) {
    console.error('获取用户额度失败:', error);
    res.status(500).json(formatError(
      '获取额度信息失败',
      'GET_QUOTA_FAILED',
      error.message
    ));
  }
});

/**
 * 检查分享权限
 * POST /api/user/check-share
 */
router.post('/check-share', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const quota = await User.getUserQuota(parseInt(userId));
    const canShare = quota.has_vip_access || quota.free_share_count > 0;
    
    res.json(formatSuccess({
      canShare: canShare,
      freeShareCount: quota.free_share_count,
      hasVipAccess: quota.has_vip_access,
      reason: canShare ? null : '分享次数已用完，请开通会员'
    }, '检查分享权限成功'));
    
  } catch (error) {
    console.error('检查分享权限失败:', error);
    res.status(500).json(formatError(
      '检查分享权限失败',
      'CHECK_SHARE_FAILED',
      error.message
    ));
  }
});

/**
 * 检查全屏权限
 * POST /api/user/check-fullscreen
 */
router.post('/check-fullscreen', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const quota = await User.getUserQuota(parseInt(userId));
    const canFullscreen = quota.has_vip_access || quota.daily_fullscreen_count > 0;
    
    res.json(formatSuccess({
      canFullscreen: canFullscreen,
      dailyFullscreenCount: quota.daily_fullscreen_count,
      hasVipAccess: quota.has_vip_access,
      reason: canFullscreen ? null : '今日全屏次数已用完，请开通会员'
    }, '检查全屏权限成功'));
    
  } catch (error) {
    console.error('检查全屏权限失败:', error);
    res.status(500).json(formatError(
      '检查全屏权限失败',
      'CHECK_FULLSCREEN_FAILED',
      error.message
    ));
  }
});

/**
 * 使用分享次数
 * POST /api/user/use-share
 */
router.post('/use-share', async (req, res) => {
  try {
    const { userId, openid } = req.body;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const success = await User.useShareCount(parseInt(userId));
    
    if (!success) {
      return res.status(403).json(formatError(
        '分享次数不足',
        'INSUFFICIENT_SHARE_COUNT'
      ));
    }
    
    // 记录使用日志
    const quota = await User.getUserQuota(parseInt(userId));
    await UsageLog.log({
      userId: parseInt(userId),
      openid: openid,
      actionType: 'share',
      isFree: !quota.has_vip_access,
      ipAddress: getClientIP(req),
      userAgent: req.get('User-Agent')
    });
    
    // 返回更新后的额度信息
    const updatedQuota = await User.getUserQuota(parseInt(userId));
    
    res.json(formatSuccess({
      success: true,
      quota: {
        freeShareCount: updatedQuota.free_share_count,
        hasVipAccess: updatedQuota.has_vip_access
      }
    }, '使用分享次数成功'));
    
  } catch (error) {
    console.error('使用分享次数失败:', error);
    res.status(500).json(formatError(
      '使用分享次数失败',
      'USE_SHARE_FAILED',
      error.message
    ));
  }
});

/**
 * 使用全屏次数
 * POST /api/user/use-fullscreen
 */
router.post('/use-fullscreen', async (req, res) => {
  try {
    const { userId, openid } = req.body;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const success = await User.useFullscreenCount(parseInt(userId));
    
    if (!success) {
      return res.status(403).json(formatError(
        '今日全屏次数不足',
        'INSUFFICIENT_FULLSCREEN_COUNT'
      ));
    }
    
    // 记录使用日志
    const quota = await User.getUserQuota(parseInt(userId));
    await UsageLog.log({
      userId: parseInt(userId),
      openid: openid,
      actionType: 'fullscreen',
      isFree: !quota.has_vip_access,
      ipAddress: getClientIP(req),
      userAgent: req.get('User-Agent')
    });
    
    // 返回更新后的额度信息
    const updatedQuota = await User.getUserQuota(parseInt(userId));
    
    res.json(formatSuccess({
      success: true,
      quota: {
        dailyFullscreenCount: updatedQuota.daily_fullscreen_count,
        hasVipAccess: updatedQuota.has_vip_access
      }
    }, '使用全屏次数成功'));
    
  } catch (error) {
    console.error('使用全屏次数失败:', error);
    res.status(500).json(formatError(
      '使用全屏次数失败',
      'USE_FULLSCREEN_FAILED',
      error.message
    ));
  }
});

/**
 * 获取用户统计信息
 * GET /api/user/stats/:userId
 */
router.get('/stats/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { days = 30 } = req.query;
    
    if (!userId || isNaN(userId)) {
      return res.status(400).json(formatError(
        '无效的用户ID',
        'INVALID_USER_ID'
      ));
    }
    
    const stats = await UsageLog.getUserStats(parseInt(userId), parseInt(days));
    const behavior = await UsageLog.getUserBehavior(parseInt(userId));
    
    res.json(formatSuccess({
      stats: stats,
      behavior: behavior
    }, '获取用户统计成功'));
    
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json(formatError(
      '获取用户统计失败',
      'GET_USER_STATS_FAILED',
      error.message
    ));
  }
});

module.exports = router;
