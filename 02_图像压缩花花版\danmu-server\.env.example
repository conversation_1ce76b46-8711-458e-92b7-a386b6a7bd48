# ==========================================
# 弹幕分享服务器环境配置文件
# ==========================================
# 复制此文件为 .env 并根据你的环境修改配置

# ==========================================
# 服务器配置
# ==========================================
# 服务器运行端口（修改为8850）
PORT=8850

# 运行环境 (development/production)
NODE_ENV=production

# 日志级别 (error/warn/info/debug)
LOG_LEVEL=info

# ==========================================
# 数据库配置
# ==========================================
# 数据库主机地址
DB_HOST=localhost

# 数据库端口
DB_PORT=3306

# 数据库用户名（建议创建专用用户）
DB_USER=danmu_user

# 数据库密码（请修改为你的实际密码）
DB_PASSWORD=your_password_here

# 数据库名称
DB_NAME=danmu_share

# 数据库字符集
DB_CHARSET=utf8mb4

# 数据库时区
DB_TIMEZONE=+08:00

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# ==========================================
# 业务配置
# ==========================================
# 分享配置过期天数（默认30天）
CONFIG_EXPIRE_DAYS=30

# 单个配置数据最大大小（字节，默认10KB）
MAX_CONFIG_SIZE=10240

# 单IP每小时最大创建分享数量（防滥用）
MAX_SHARES_PER_HOUR=100

# 分享ID长度（默认8位）
SHARE_ID_LENGTH=8

# ==========================================
# 安全配置
# ==========================================
# JWT密钥（如果需要用户认证）
JWT_SECRET=your_jwt_secret_here

# CORS允许的域名（多个域名用逗号分隔，* 表示允许所有）
CORS_ORIGIN=*

# 请求体大小限制
BODY_LIMIT=1mb

# 是否启用请求日志
ENABLE_REQUEST_LOG=true

# 是否启用IP记录
ENABLE_IP_LOGGING=true

# ==========================================
# 缓存配置（可选）
# ==========================================
# Redis配置（如果需要缓存）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0

# 热门分享缓存时间（秒）
POPULAR_CACHE_TTL=300

# ==========================================
# 监控配置（可选）
# ==========================================
# 是否启用性能监控
ENABLE_MONITORING=false

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# ==========================================
# 微信小程序配置（预留）
# ==========================================
# WECHAT_APP_ID=your-app-id
# WECHAT_APP_SECRET=your-app-secret

# ==========================================
# 开发调试配置
# ==========================================
# 是否启用调试模式
DEBUG=false

# 是否启用SQL查询日志
ENABLE_SQL_LOG=false

# 是否启用详细错误信息（生产环境建议关闭）
ENABLE_DETAILED_ERRORS=false
