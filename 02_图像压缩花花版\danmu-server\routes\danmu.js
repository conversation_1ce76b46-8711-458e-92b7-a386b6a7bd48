const express = require('express');
const DanmuShare = require('../models/DanmuShare');
const User = require('../models/User');
const UsageLog = require('../models/UsageLog');
const {
  generateId,
  getClientIP,
  validateShareData,
  sanitizeShareData,
  formatError,
  formatSuccess,
  checkDataSize
} = require('../utils/helpers');

const router = express.Router();

// 保存弹幕分享配置
router.post('/save', async (req, res) => {
  try {
    const shareData = req.body;
    const { userId, openid } = req.body;

    // 验证分享数据
    if (!validateShareData(shareData)) {
      return res.status(400).json(formatError(
        '无效的分享数据',
        'INVALID_SHARE_DATA'
      ));
    }

    // 检查数据大小
    if (!checkDataSize(shareData)) {
      return res.status(400).json(formatError(
        '分享数据过大',
        'SHARE_TOO_LARGE'
      ));
    }

    // 用户额度验证（如果提供了userId）
    let user = null;
    if (userId) {
      try {
        // 检查用户分享权限
        const quota = await User.getUserQuota(parseInt(userId));
        const canShare = quota.has_vip_access || quota.free_share_count > 0;

        if (!canShare) {
          return res.status(403).json(formatError(
            '分享次数不足，请开通会员',
            'INSUFFICIENT_SHARE_COUNT'
          ));
        }

        // 使用分享次数
        const useSuccess = await User.useShareCount(parseInt(userId));
        if (!useSuccess) {
          return res.status(403).json(formatError(
            '分享次数不足',
            'INSUFFICIENT_SHARE_COUNT'
          ));
        }

        user = { userId: parseInt(userId), hasVipAccess: quota.has_vip_access };

      } catch (userError) {
        console.warn('用户验证失败，继续匿名分享:', userError.message);
      }
    }

    // 清理和标准化分享数据
    const sanitizedData = sanitizeShareData(shareData);

    // 生成唯一分享ID
    let shareId;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      shareId = generateId();
      attempts++;

      if (attempts > maxAttempts) {
        throw new Error('无法生成唯一分享ID');
      }
    } while (await DanmuShare.exists(shareId));

    // 获取客户端信息
    const creatorIP = getClientIP(req);
    const creatorUserAgent = req.get('User-Agent') || '';
    const creatorOpenid = openid || null;

    // 保存到数据库
    await DanmuShare.save(shareId, sanitizedData, creatorIP, creatorOpenid, creatorUserAgent);

    // 记录使用日志
    if (user) {
      await UsageLog.log({
        userId: user.userId,
        openid: creatorOpenid,
        actionType: 'share',
        isFree: !user.hasVipAccess,
        ipAddress: creatorIP,
        userAgent: creatorUserAgent
      });
    }

    // 计算过期时间
    const expireDays = parseInt(process.env.CONFIG_EXPIRE_DAYS) || 30;
    const expiresAt = new Date(Date.now() + expireDays * 24 * 60 * 60 * 1000);

    res.json(formatSuccess({
      shareId: shareId,
      shareUrl: `/pages/fullscreen/fullscreen?share=${shareId}`,
      expires_at: expiresAt.toISOString(),
      expires_in_days: expireDays,
      quota: user ? await User.getUserQuota(user.userId) : null
    }, '分享创建成功'));

  } catch (error) {
    console.error('保存分享失败:', error);
    res.status(500).json(formatError(
      '保存失败',
      'SAVE_FAILED',
      error.message
    ));
  }
});

// 获取弹幕分享配置
router.get('/get/:shareId', async (req, res) => {
  try {
    const { shareId } = req.params;

    // 验证分享ID格式
    if (!shareId || shareId.length !== 8 || !/^[a-zA-Z0-9]+$/.test(shareId)) {
      return res.status(400).json(formatError(
        '无效的分享ID格式',
        'INVALID_SHARE_ID_FORMAT'
      ));
    }

    // 从数据库获取分享配置
    const result = await DanmuShare.getByShareId(shareId);

    if (result) {
      // 更新访问次数
      await DanmuShare.incrementAccessCount(shareId);

      res.json(formatSuccess({
        shareId: result.shareId,
        config: {
          danmuText: result.danmuText,
          styleType: result.styleType,
          speedClass: result.speedClass,
          sizeClass: result.sizeClass,
          fontClass: result.fontClass,
          runningMode: result.runningMode,
          customTextColor: result.customTextColor,
          customBgColor: result.customBgColor,
          customShakeLevel: result.customShakeLevel,
          customShadowType: result.customShadowType
        },
        created_at: result.createdAt,
        access_count: result.accessCount + 1 // 包含本次访问
      }, '分享配置获取成功'));
    } else {
      res.status(404).json(formatError(
        '分享不存在或已过期',
        'SHARE_NOT_FOUND'
      ));
    }

  } catch (error) {
    console.error('获取分享配置失败:', error);
    res.status(500).json(formatError(
      '获取失败',
      'GET_FAILED',
      error.message
    ));
  }
});

// 清理过期分享（管理接口）
router.delete('/cleanup', async (req, res) => {
  try {
    const deletedCount = await DanmuShare.cleanExpired();

    res.json(formatSuccess({
      deleted_count: deletedCount
    }, `已清理 ${deletedCount} 个过期分享`));
  } catch (error) {
    console.error('清理失败:', error);
    res.status(500).json(formatError(
      '清理失败',
      'CLEANUP_FAILED',
      error.message
    ));
  }
});

// 获取分享统计信息
router.get('/stats', async (req, res) => {
  try {
    const stats = await DanmuShare.getStats();

    res.json(formatSuccess({
      statistics: stats
    }, '分享统计信息获取成功'));
  } catch (error) {
    console.error('获取统计失败:', error);
    res.status(500).json(formatError(
      '获取统计失败',
      'STATS_FAILED',
      error.message
    ));
  }
});

// 获取热门分享
router.get('/popular', async (req, res) => {
  try {
    const limit = Math.min(parseInt(req.query.limit) || 10, 50); // 最多50个
    const popular = await DanmuShare.getPopular(limit);

    res.json(formatSuccess({
      popular_shares: popular,
      count: popular.length
    }, '热门分享获取成功'));
  } catch (error) {
    console.error('获取热门分享失败:', error);
    res.status(500).json(formatError(
      '获取热门分享失败',
      'POPULAR_FAILED',
      error.message
    ));
  }
});

// 根据IP获取用户分享历史
router.get('/history', async (req, res) => {
  try {
    const ipAddress = getClientIP(req);
    const limit = Math.min(parseInt(req.query.limit) || 20, 100); // 最多100个

    const history = await DanmuShare.getByCreatorIP(ipAddress, limit);

    res.json(formatSuccess({
      history: history,
      count: history.length,
      creator_ip: ipAddress
    }, '分享历史获取成功'));
  } catch (error) {
    console.error('获取分享历史失败:', error);
    res.status(500).json(formatError(
      '获取分享历史失败',
      'HISTORY_FAILED',
      error.message
    ));
  }
});

module.exports = router;
