-- ========================================
-- VIP时间累加功能数据库更新脚本
-- ========================================
-- 修复日卡重复购买时间覆盖的问题

-- 1. 修改user_memberships表的status字段，添加'upgraded'状态
ALTER TABLE user_memberships 
MODIFY COLUMN status ENUM('active', 'expired', 'cancelled', 'upgraded') DEFAULT 'active' COMMENT '状态：active=活跃，expired=过期，cancelled=取消，upgraded=已升级';

-- 2. 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_user_memberships_user_status 
ON user_memberships(user_id, status);

CREATE INDEX IF NOT EXISTS idx_user_memberships_expire 
ON user_memberships(expire_date);

-- 3. 创建查看用户会员状态的视图
CREATE OR REPLACE VIEW user_vip_status AS
SELECT 
    u.user_id,
    u.openid,
    CASE 
        WHEN um.membership_type = 'lifetime_vip' THEN '终身会员'
        WHEN um.membership_type = 'temp_vip' AND um.expire_date > NOW() THEN '日卡会员'
        ELSE '普通用户'
    END as vip_status,
    um.membership_type,
    um.expire_date,
    um.created_at as vip_start_time,
    CASE 
        WHEN um.expire_date IS NULL THEN '永久'
        WHEN um.expire_date > NOW() THEN CONCAT(TIMESTAMPDIFF(HOUR, NOW(), um.expire_date), '小时')
        ELSE '已过期'
    END as remaining_time
FROM users u
LEFT JOIN user_memberships um ON u.user_id = um.user_id 
    AND um.status = 'active' 
    AND (um.expire_date IS NULL OR um.expire_date > NOW())
ORDER BY u.user_id;

-- 4. 创建存储过程：安全激活VIP（支持时间累加）
DELIMITER //
CREATE PROCEDURE SafeActivateVip(
    IN p_user_id INT,
    IN p_membership_type ENUM('temp_vip', 'lifetime_vip'),
    IN p_order_id VARCHAR(32),
    OUT p_result_code INT,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_existing_type VARCHAR(20) DEFAULT NULL;
    DECLARE v_existing_expire TIMESTAMP DEFAULT NULL;
    DECLARE v_new_expire TIMESTAMP DEFAULT NULL;
    DECLARE v_action VARCHAR(20) DEFAULT 'created';
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result_code = -1;
        SET p_message = '数据库操作失败';
    END;
    
    START TRANSACTION;
    
    -- 检查现有会员状态
    SELECT membership_type, expire_date 
    INTO v_existing_type, v_existing_expire
    FROM user_memberships 
    WHERE user_id = p_user_id 
        AND status = 'active' 
        AND (expire_date IS NULL OR expire_date > NOW())
    ORDER BY CASE WHEN expire_date IS NULL THEN 1 ELSE 0 END DESC,
             expire_date DESC
    LIMIT 1;
    
    -- 处理不同情况
    IF p_membership_type = 'temp_vip' THEN
        IF v_existing_type = 'lifetime_vip' THEN
            -- 已是终身会员，拒绝日卡购买
            SET p_result_code = 1;
            SET p_message = '您已是终身会员，无需购买日卡';
            ROLLBACK;
        ELSEIF v_existing_type = 'temp_vip' THEN
            -- 累加24小时到现有日卡
            SET v_new_expire = DATE_ADD(v_existing_expire, INTERVAL 24 HOUR);
            UPDATE user_memberships 
            SET expire_date = v_new_expire, 
                updated_at = NOW()
            WHERE user_id = p_user_id 
                AND status = 'active' 
                AND membership_type = 'temp_vip';
            SET v_action = 'extended';
            SET p_result_code = 0;
            SET p_message = CONCAT('日卡时间已累加至：', DATE_FORMAT(v_new_expire, '%Y-%m-%d %H:%i:%s'));
        ELSE
            -- 新建日卡
            SET v_new_expire = DATE_ADD(NOW(), INTERVAL 24 HOUR);
            INSERT INTO user_memberships (user_id, membership_type, expire_date, order_id, status)
            VALUES (p_user_id, 'temp_vip', v_new_expire, p_order_id, 'active');
            SET p_result_code = 0;
            SET p_message = '24小时会员激活成功';
        END IF;
        
    ELSEIF p_membership_type = 'lifetime_vip' THEN
        IF v_existing_type = 'lifetime_vip' THEN
            -- 已是终身会员，拒绝重复购买
            SET p_result_code = 1;
            SET p_message = '您已是终身会员，无需重复购买';
            ROLLBACK;
        ELSE
            -- 升级为终身会员
            IF v_existing_type = 'temp_vip' THEN
                UPDATE user_memberships 
                SET status = 'upgraded', updated_at = NOW()
                WHERE user_id = p_user_id 
                    AND status = 'active' 
                    AND membership_type = 'temp_vip';
                SET v_action = 'upgraded';
            END IF;
            
            INSERT INTO user_memberships (user_id, membership_type, expire_date, order_id, status)
            VALUES (p_user_id, 'lifetime_vip', NULL, p_order_id, 'active');
            SET p_result_code = 0;
            SET p_message = CASE WHEN v_action = 'upgraded' THEN '已升级为终身会员' ELSE '终身会员激活成功' END;
        END IF;
    END IF;
    
    COMMIT;
END //
DELIMITER ;

-- 5. 创建查询用户VIP详情的存储过程
DELIMITER //
CREATE PROCEDURE GetUserVipDetails(IN p_user_id INT)
BEGIN
    SELECT 
        u.user_id,
        u.openid,
        um.membership_type,
        um.expire_date,
        um.status,
        um.created_at as vip_start_time,
        CASE 
            WHEN um.membership_type = 'lifetime_vip' THEN '终身会员'
            WHEN um.membership_type = 'temp_vip' AND um.expire_date > NOW() THEN '日卡会员'
            WHEN um.membership_type = 'temp_vip' AND um.expire_date <= NOW() THEN '日卡已过期'
            ELSE '普通用户'
        END as vip_status_text,
        CASE 
            WHEN um.expire_date IS NULL THEN '永久有效'
            WHEN um.expire_date > NOW() THEN CONCAT(
                TIMESTAMPDIFF(DAY, NOW(), um.expire_date), '天',
                MOD(TIMESTAMPDIFF(HOUR, NOW(), um.expire_date), 24), '小时',
                MOD(TIMESTAMPDIFF(MINUTE, NOW(), um.expire_date), 60), '分钟'
            )
            ELSE '已过期'
        END as remaining_time_text
    FROM users u
    LEFT JOIN user_memberships um ON u.user_id = um.user_id 
        AND um.status = 'active' 
        AND (um.expire_date IS NULL OR um.expire_date > NOW())
    WHERE u.user_id = p_user_id
    ORDER BY um.created_at DESC
    LIMIT 1;
END //
DELIMITER ;

-- 6. 插入测试数据验证功能（可选）
-- INSERT INTO users (openid) VALUES ('test_user_vip_fix') ON DUPLICATE KEY UPDATE openid=openid;

-- 执行完成提示
SELECT '✅ VIP时间累加功能数据库更新完成！' as message;
SELECT '📋 新增功能：' as info, '1. 日卡时间累加 2. 防止重复购买 3. VIP状态查询视图 4. 安全激活存储过程' as details;
