const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const danmuRoutes = require('./routes/danmu');
const userRoutes = require('./routes/user');
const orderRoutes = require('./routes/order');
const fullscreenRoutes = require('./routes/fullscreen');
const authRoutes = require('./routes/auth');
const certsRoutes = require('./routes/certs');
const paymentRoutes = require('./routes/payment');
const merchantRoutes = require('./routes/merchant');
const usageRoutes = require('./routes/usage');
const securityRoutes = require('./routes/security');
const { testConnection } = require('./config/database');
const Scheduler = require('./utils/scheduler');

const app = express();
const PORT = process.env.PORT || 8850;

// 中间件
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/certs', certsRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/merchant', merchantRoutes);
app.use('/api/danmu', danmuRoutes);
app.use('/api/user', userRoutes);
app.use('/api/order', orderRoutes);
app.use('/api/fullscreen', fullscreenRoutes);
app.use('/api/usage', usageRoutes);
app.use('/api/security', securityRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: '弹幕分享API服务',
    version: '1.0.0'
  });
});

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '弹幕分享API服务正在运行',
    version: '1.0.0',
    endpoints: {
      // 弹幕相关
      'POST /api/danmu/save': '保存弹幕分享配置',
      'GET /api/danmu/get/:shareId': '获取弹幕分享配置',
      'GET /api/danmu/stats': '获取分享统计信息',
      'GET /api/danmu/popular': '获取热门分享',
      'GET /api/danmu/history': '获取分享历史',
      'DELETE /api/danmu/cleanup': '清理过期分享',

      // 认证相关
      'POST /api/auth/login': '微信小程序登录',
      'POST /api/auth/refresh': '刷新用户session',

      // 证书管理
      'GET /api/certs/status': '获取所有商户状态',
      'GET /api/certs/merchants': '获取活跃商户列表',
      'GET /api/certs/validate/:merchantId': '验证商户配置',
      'GET /api/certs/config/:merchantId': '获取商户配置（脱敏）',
      'GET /api/certs/files/:merchantId': '检查证书文件状态',
      'POST /api/certs/reload': '重新加载配置',
      'GET /api/certs/help': '获取证书管理帮助',

      // 商户管理
      'GET /api/merchant/match/:appId': '根据AppID匹配商户',
      'GET /api/merchant/test/wechat-config/:appId': '测试微信配置',

      // 订单和VIP管理
      'POST /api/order/test-vip-activation': '测试VIP激活功能（支持时间累加）',

      // 微信支付
      'POST /api/payment/notify': '微信支付回调',
      'POST /api/payment/create': '创建支付订单',
      'GET /api/payment/query/:orderId': '查询支付状态',
      'POST /api/payment/close': '关闭支付订单',
      'GET /api/payment/config': '获取支付配置',

      // 用户相关
      'POST /api/user/login': '用户登录/注册（已废弃，请使用/api/auth/login）',
      'GET /api/user/quota/:userId': '获取用户额度信息',
      'POST /api/user/check-share': '检查分享权限',
      'POST /api/user/check-fullscreen': '检查全屏权限',
      'POST /api/user/use-share': '使用分享次数',
      'POST /api/user/use-fullscreen': '使用全屏次数',
      'GET /api/user/stats/:userId': '获取用户统计信息',

      // 订单相关
      'GET /api/order/products': '获取产品配置',
      'POST /api/order/create': '创建订单',
      'GET /api/order/:orderId': '获取订单详情',
      'GET /api/order/user/:userId': '获取用户订单列表',
      'POST /api/order/pay-success': '模拟支付成功',
      'POST /api/order/wechat-callback': '微信支付回调',
      'GET /api/order/stats/summary': '获取订单统计',
      'POST /api/order/cleanup': '清理过期订单',

      // 全屏相关
      'POST /api/fullscreen/check': '检查全屏使用权限',
      'POST /api/fullscreen/use': '使用全屏功能',
      'GET /api/fullscreen/stats': '获取全屏使用统计',

      // 系统相关
      'GET /health': '健康检查'
    },
    documentation: 'https://github.com/your-repo/danmu-server'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err.stack);
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在',
    path: req.path
  });
});

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    await testConnection();
    console.log('✅ 数据库连接成功');
    
    app.listen(PORT, () => {
      console.log(`
======================================
   弹幕分享服务器 (danmu-server)
======================================

服务器已启动:
- 端口: ${PORT}
- 环境: ${process.env.NODE_ENV || 'development'}
- 数据库: ${process.env.DB_NAME || 'danmu_share'}

API地址: http://localhost:${PORT}
健康检查: http://localhost:${PORT}/health

按 Ctrl+C 停止服务器
      `);

      // 启动定时任务
      if (process.env.NODE_ENV !== 'test') {
        Scheduler.start();
      }
    });
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    console.error('请检查数据库连接配置');
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  process.exit(0);
});

startServer();
