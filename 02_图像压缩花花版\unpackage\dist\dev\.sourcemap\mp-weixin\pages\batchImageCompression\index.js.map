{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/batchImageCompression/index.vue?8541", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/batchImageCompression/index.vue?39fe", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/batchImageCompression/index.vue?0be0", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/batchImageCompression/index.vue?013f", "uni-app:///pages/batchImageCompression/index.vue", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/batchImageCompression/index.vue?47a3", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/batchImageCompression/index.vue?e290"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "ShareButton", "data", "imagesList", "quality", "size", "compressing", "statusBarHeight", "navHeight", "canvasWidth", "canvasHeight", "compressTimer", "currentIndex", "startTime", "endTime", "showSizeInput", "temp<PERSON>idth", "tempHeight", "customWidth", "customHeight", "compressMode", "selectionStart", "selectionEnd", "lastCompressParams", "showSharePopup", "videoAd", "onLoad", "console", "methods", "initRewardedVideoAd", "adUnitId", "uni", "title", "icon", "duration", "showRewardedVideoAd", "then", "catch", "showAdPrompt", "content", "confirmText", "cancelText", "success", "goBack", "delta", "fail", "url", "handleShare", "withShareTicket", "menus", "mode", "getApp", "path", "imageUrl", "processImages", "i", "Promise", "checkResult", "fs", "filePath", "src", "fileInfo", "imageInfo", "width", "height", "originalSize", "compressedSize", "selectImages", "itemList", "sourceType", "count", "type", "deleteImage", "clearImages", "onQualityChange", "onQualityChanging", "onSizeChange", "onSizeChanging", "updateCompressionStatus", "previewCompression", "item", "canvas", "ctx", "targetWidth", "targetHeight", "widthRatio", "heightRatio", "scaleRatio", "scaledWidth", "scaledHeight", "cropX", "cropY", "query", "fields", "node", "exec", "resolve", "reject", "image", "fileType", "result", "compressedInfo", "compressedImageInfo", "Math", "curItem", "compressionRatio", "estimatedSize", "updateCustomSize", "showSizePopup", "hideSizePopup", "confirmSize", "handleInputFocus", "setTimeout", "startCompression", "freeCount", "mask", "imagePaths", "checkResults", "checkingImages", "showCancel", "riskyImages", "currentParams", "paramsChanged", "JSON", "currentFreeCount", "timeCost", "saveCompressedImages", "savedImages", "onShareAppMessage", "onShareTimeline", "hideSharePopup"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,iMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAAouB,CAAgB,mtBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACgLxvB;AACA;AACA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;QACA;QACA;UACA;QACA;MACA;QACAC;MACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;UACAC;QACA;;QAEA;QACA;UACAH;QACA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACA;UACA;YACA;YACA;;YAEAI;cACAC;cACAC;cACAC;YACA;;YAEA;YACA;UACA;YACA;YACAH;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACA;QACA;UACA;UACA,qBACAC;YAAA;UAAA,GACAC;YACAV;YACAI;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACAP;QACAC;QACAO;QACAC;QACAC;QACAC;UACA;YACA;YACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAZ;QACAa;QACAC;UACA;UACAd;YACAe;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;UAAA;QAAA;QAEA;UACA;UACA;YAAA;UAAA;UACA;UACAhB;YACAiB;YACAC;UACA;;UAEA;UACA;YACA7C;YACAC;YACA6C;YACAhC;YACAC;UACA;;UAEA;UACAgC;UACAA;YACAnB;YACAoB;YACAC;UACA;UAEAtB;YACAC;YACAC;YACAC;UACA;QACA;UACA;UACAH;YACAiB;YACAC;UACA;;UAEA;UACAE;UACAA;YACAnB;YACAoB;YACAC;UACA;UAEAtB;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACA;QACAH;UACAiB;UACAC;QACA;;QAEA;QACAE;QACAA;UACAnB;UACAoB;UACAC;QACA;QAEAtB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAoB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,8DACAC;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACAH;0BAAA;0BAEA;0BACA;0BACAI;4BAAA;4BAAA;8BAAA;gCAAA;kCAAA;oCAAA;oCAAA;oCAAA,OAGA;kCAAA;oCAAAC;oCAEA;oCACA;oCACA;oCAAA;oCAAA;kCAAA;oCAAA;oCAAA;oCAEA;oCACA9B;kCAAA;kCAAA;oCAAA;gCAAA;8BAAA;4BAAA;0BAAA,CAEA;0BAAA;0BAAA,OAEA6B,aACA;4BACA;4BACAE;8BACAC;8BACAjB;8BACAG;4BACA;0BACA,IACA;4BACAd;8BACA6B;8BACAlB;8BACAG;4BACA;0BACA,GACA;wBAAA;0BAAA;0BAAA;0BAhBAgB;0BAAAC;0BAkBA;4BACAV;4BACAW;4BACAC;4BACAC;4BACAC;0BACA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAEAvC;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBA7CA4B;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;gBAiDA;gBACA;;gBAEA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAY;MAAA;MACApC;QACAqC;QACA1B;UACA;UACA;YACA;cACA2B;cACA;YACA;cACAA;cACA;YACA;cACA;cACAtC;gBACAuC;gBACAC;gBACA7B;kBACA;oBAAA;kBAAA;kBACA;gBACA;gBACAG;kBACAd;oBACAC;oBACAC;kBACA;gBACA;cACA;cACA;UAAA;UAGAF;YACAuC;YACAD;YACA3B;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA8B;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;;gBAEA;gBACAC;gBAGAC;gBACAC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBACAC;;gBAEA;gBACAC;gBACAC,0CAEA;gBACAC,gDAEA;gBACAC;gBACAC,qDAEA;gBACAC;gBACAC,uDAEA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBAAA;gBAAA;gBAAA,OAEA;kBACAA,gCACAC;oBAAAC;oBAAAzF;kBAAA,GACA0F;oBACA;sBACAC;oBACA;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAVAhB;gBAYAC;;gBAEA;gBACAD;gBACAA;;gBAEA;gBACAC;;gBAEA;gBACAgB;gBAAA;gBAAA,OACA;kBACAA;kBACAA;kBACAA;gBACA;cAAA;gBAEA;gBACAhB,cACAgB,OACA;gBAAA;gBACA;gBAAA,CACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvE;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACAwD;gBACAC;;gBAEA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAEA;gBACAQ;gBAAA;gBAAA;gBAAA,OAEA;kBACAA,iCACAC;oBAAAC;oBAAAzF;kBAAA,GACA0F;oBACA;sBACAC;oBACA;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAVAhB;gBAYAC;;gBAEA;gBACAD;gBACAA;;gBAEA;gBACAC;;gBAEA;gBACAgB;gBAAA;gBAAA,OACA;kBACAA;kBACAA;kBACAA;gBACA;cAAA;gBAEA;gBACAhB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvD;gBAAA,MACA;cAAA;gBAAA,IAKAsD;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAIA;kBACA;oBACAlD;sBACAkD;sBACAkB;sBACA/F;sBACAsC;sBACAG;oBACA;kBACA;oBACAoD;kBACA;gBACA;cAAA;gBAZAG;gBAAA;gBAAA,OAeA;kBACA;kBACA1C;oBACAC;oBACAjB;oBACAG;kBACA;gBACA;cAAA;gBAPAwD;gBAAA;gBAAA,OAUA;kBACAtE;oBACA6B;oBACAlB;oBACAG;kBACA;gBACA;cAAA;gBANAyD;gBAQA;gBACA,oDACAC;gBACA;gBACA;gBACA;;gBAEA;gBACA;kBACAC,gCACA;kBACAC;kBACAC;kBACA,qEACAA;kBAEA;oBACA;oBACA;kBACA;oBACA,qDACAH;oBACA,sDACAA;kBACA;gBACA;;gBAEA;gBACA;kBACAnG;kBACAC;kBACAa;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;gBACAI;kBACAC;kBACAC;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAyE;MACA;MACA;;MAEA;MACA;MACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;QACA/E;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;MAEA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;UACA;QACA;MACA;QACAF;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACA8E;MAAA;MACA;MACAC;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAnF;kBACAC;kBACAmF;gBACA;gBAAA;gBAGA;gBACA;gBACAC;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAEA;gBACAC;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAvF;gBACAA;kBACAC;kBACAO;kBACAgF;gBACA;gBAAA;cAAA;gBAIA;gBACAC;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAzF;gBACAA;kBACAC;kBACAO;kBACAgF;gBACA;gBAAA;cAAA;gBAIA;gBACAxF;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACAI;gBACA;cAAA;gBAGA;gBACA;gBAEAA;kBACAC;kBACAmF;gBACA;gBAAA;gBAGA;gBACAM;kBACArH;kBACAC;kBACAa;kBACAC;kBACAC;gBACA;gBAEAsG,+CACAC,8EAEA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,gEAIAnE;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACA;0BACAyB;0BAEAG;0BACAF;0BACAC;0BAAA,MAEA;4BAAA;4BAAA;0BAAA;0BACA;0BACAC;0BACAC;;0BAEA;0BACAC;0BACAC,0CAEA;0BACAC,gDAEA;0BACAC;0BACAC,qDAEA;0BACAC;0BACAC,uDAEA;0BACA;0BACA;;0BAEA;0BAAA;0BAAA,OACA;4BAAA;0BAAA;wBAAA;0BAEA;0BACAC;0BAAA;0BAAA;0BAAA,OAEA;4BACAA,gCACAC;8BAAAC;8BAAAzF;4BAAA,GACA0F;8BACA;gCACAC;8BACA;gCACAC;8BACA;4BACA;0BACA;wBAAA;0BAVAhB;0BAYAC;;0BAEA;0BACAD;0BACAA;;0BAEA;0BACAC;;0BAEA;0BACAgB;0BAAA;0BAAA,OACA;4BACAA;4BACAA;4BACAA;0BACA;wBAAA;0BAEA;0BACAhB,cACAgB,OACA;0BAAA;0BACA;0BAAA,CACA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAEAvE;0BAAA,MACA;wBAAA;0BAAA;0BAAA;wBAAA;0BAGA;0BACAwD;0BACAC;;0BAEA;0BACA;0BACA;;0BAEA;0BAAA;0BAAA,OACA;4BAAA;0BAAA;wBAAA;0BAEA;0BACAQ;0BAAA;0BAAA;0BAAA,OAEA;4BACAA,kCACAC;8BAAAC;8BAAAzF;4BAAA,GACA0F;8BACA;gCACAC;8BACA;gCACAC;8BACA;4BACA;0BACA;wBAAA;0BAVAhB;0BAYAC;;0BAEA;0BACAD;0BACAA;;0BAEA;0BACAC;;0BAEA;0BACAgB;0BAAA;0BAAA,OACA;4BACAA;4BACAA;4BACAA;0BACA;wBAAA;0BAEA;0BACAhB;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAEAvD;0BAAA,MACA;wBAAA;0BAAA,IAKAsD;4BAAA;4BAAA;0BAAA;0BAAA,MACA;wBAAA;0BAAA;0BAAA,OAIA;4BACA;8BACAlD;gCACAkD;gCACAkB;gCACA/F;gCACAsC;gCACAG;8BACA;4BACA;8BACAoD;4BACA;0BACA;wBAAA;0BAZAG;0BAAA;0BAAA,OAeA;4BACA;4BACA1C;8BACAC;8BACAjB;8BACAG;4BACA;0BACA;wBAAA;0BAPAwD;0BAAA;0BAAA,OAUA;4BACAtE;8BACA6B;8BACAlB;8BACAG;4BACA;0BACA;wBAAA;0BANAyD;0BAQA;0BACA,sDACAC;0BACA;0BACA;0BACA;;0BAEA;0BACAxE;4BACAC;4BACAmF;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAnLA5D;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;gBAsLA;gBACA;;gBAEA;gBACAqE;gBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA7F;;gBAEA;gBACA8F,sEAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAlG;gBACAI;gBACAA;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4F;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,uCAGA;gBAAA;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA/C;0BAAA,KACAA;4BAAA;4BAAA;0BAAA;0BAAA;0BAAA,OACA;4BACAjD;8BACA4B;8BACAjB;gCACAqF;gCACA/B;8BACA;8BACAnD;gCACA;kCACA;kCACAoD;gCACA;kCACA;kCACAD;gCACA;8BACA;4BACA;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAIA;kBAAA;gBAAA;kBACA;kBACAjE;oBACAC;oBACAO;oBACAG;sBACA;wBACAX;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAA;kBACAC;kBACAO;kBACAG;oBACA;sBACAX;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAiG;MACA;MACA;QACA;MACA;MACA;MACA;QACAhG;QACAoB;QACAC;MACA;IACA;IACA4E;MACA;MACA;QACA;UACAjG;UACAoB;UACAC;QACA;MACA;MACA;MACA;QACArB;QACAoB;QACAC;MACA;IACA;IACA;IACA6E;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtuCA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/batchImageCompression/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/batchImageCompression/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=370cc4bf&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/batchImageCompression/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=370cc4bf&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.imagesList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 =\n      !item.compressedSize && !(_vm.compressMode === \"custom\")\n        ? Math.floor((item.width * _vm.size) / 100)\n        : null\n    var g1 =\n      !item.compressedSize && !(_vm.compressMode === \"custom\")\n        ? Math.floor((item.height * _vm.size) / 100)\n        : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.imagesList.length\n  var g3 = _vm.imagesList.length\n  var g4 = _vm.imagesList.length\n  var g5 = _vm.imagesList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"image-compression\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-nav glassmorphism\">\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<view class=\"nav-content\">\n\t\t\t\t<view class=\"back-btn\" @tap=\"goBack\">\n\t\t\t\t\t<view class=\"wx-nav-back\"></view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"nav-title\">批量压缩</text>\n\t\t\t\t<view class=\"share-button-container\">\n\t\t\t\t\t<share-button @share=\"handleShare\" :showText=\"false\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"main-content\" :style=\"{ paddingTop: navHeight + 'px' }\">\n\t\t\t<!-- 图片列表区域 -->\n\t\t\t<view class=\"image-list neumorphism\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text>批量压缩</text>\n\t\t\t\t\t<view class=\"clear-btn\" @click=\"clearImages\">\n\t\t\t\t\t\t<text>清空图片</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 图片项 -->\n\t\t\t\t<view class=\"image-item\" v-for=\"(item, index) in imagesList\" :key=\"index\">\n\t\t\t\t\t<image class=\"preview-image\" :src=\"item.path\" mode=\"aspectFit\"></image>\n\t\t\t\t\t<view class=\"image-info\">\n\t\t\t\t\t\t<view class=\"info-line\">\n\t\t\t\t\t\t\t<text>压缩前：</text>\n\t\t\t\t\t\t\t<text>尺寸: {{item.width}} × {{item.height}} 大小: {{item.originalSize}} KB</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-line\">\n\t\t\t\t\t\t\t<text>压缩后：</text>\n\t\t\t\t\t\t\t<text v-if=\"item.compressedSize\">尺寸: {{item.compressedWidth || item.width}} × {{item.compressedHeight || item.height}} 大小: {{item.compressedSize}} KB</text>\n\t\t\t\t\t\t\t<text v-else>尺寸: {{compressMode === 'custom' ? customWidth + ' × ' + customHeight : Math.floor(item.width * size / 100) + ' × ' + Math.floor(item.height * size / 100)}} 大小: -- KB</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"progress-bar\" v-if=\"compressing && currentIndex === index\">\n\t\t\t\t\t\t\t<view class=\"progress-inner\" :style=\"{width: '100%'}\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"delete-btn\" @click=\"deleteImage(index)\">×</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 提示信息 -->\n\t\t\t\t<view class=\"tip-text\" v-if=\"imagesList.length > 0\">\n\t\t\t\t\t压缩在微信本地完成，您的照片不会被泄露\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<view class=\"empty-state\" v-if=\"imagesList.length === 0\" @tap=\"selectImages\">\n\t\t\t\t\t<image src=\"/static/upload.svg\" mode=\"aspectFit\" class=\"upload-icon\"></image>\n\t\t\t\t\t<text>请选择需要压缩的图片</text>\n\t\t\t\t\t<text class=\"click-hint\">点击此处选择图片</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 添加隐藏的canvas -->\n\t\t\t<canvas\n\t\t\t\ttype=\"2d\"\n\t\t\t\tid=\"compressCanvas\"\n\t\t\t\t:style=\"{\n\t\t\t\t\twidth: `${canvasWidth}px`,\n\t\t\t\t\theight: `${canvasHeight}px`,\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: '-9999px'\n\t\t\t\t}\"\n\t\t\t></canvas>\n\n\t\t\t<!-- 添加用于图片预处理的隐藏canvas -->\n\t\t\t<canvas\n\t\t\t\ttype=\"2d\"\n\t\t\t\tid=\"uploadPreprocessCanvas\"\n\t\t\t\tstyle=\"position: fixed; left: -9999px; width: 300px; height: 300px;\"\n\t\t\t></canvas>\n\t\t</view>\n\n\t\t<!-- 尺寸输入弹出层 -->\n\t\t<view class=\"size-popup\" v-if=\"showSizeInput\">\n\t\t\t<view class=\"popup-mask\" @tap=\"hideSizePopup\"></view>\n\t\t\t<view class=\"popup-content neumorphism\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">设置批量压缩尺寸</text>\n\t\t\t\t\t<text class=\"popup-close\" @tap=\"hideSizePopup\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text>宽度</text>\n\t\t\t\t\t\t<input type=\"number\" v-model=\"tempWidth\" placeholder=\"输入宽度\" :selection-start=\"0\" :selection-end=\"-1\" @focus=\"handleInputFocus\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text>高度</text>\n\t\t\t\t\t\t<input type=\"number\" v-model=\"tempHeight\" placeholder=\"输入高度\" :selection-start=\"0\" :selection-end=\"-1\" @focus=\"handleInputFocus\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup-buttons\">\n\t\t\t\t\t\t<button class=\"cancel-btn\" @tap=\"hideSizePopup\">取消</button>\n\t\t\t\t\t\t<button class=\"confirm-btn\" @tap=\"confirmSize\">确定</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 分享弹窗 -->\n\t\t<view class=\"share-popup\" v-if=\"showSharePopup\">\n\t\t\t<view class=\"popup-mask\" @tap=\"hideSharePopup\"></view>\n\t\t\t<view class=\"popup-content neumorphism\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">压缩成功</text>\n\t\t\t\t\t<text class=\"popup-close\" @tap=\"hideSharePopup\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"share-content\">\n\t\t\t\t\t\t<image src=\"/static/share-icon.png\" mode=\"aspectFit\" class=\"share-icon\"></image>\n\t\t\t\t\t\t<text class=\"share-title\">图片已保存到相册</text>\n\t\t\t\t\t\t<text class=\"share-desc\">分享给好友一起使用吧</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"share-buttons\">\n\t\t\t\t\t\t<button class=\"share-btn\" open-type=\"share\">\n\t\t\t\t\t\t\t<text class=\"iconfont icon-wechat\"></text>\n\t\t\t\t\t\t\t<text>分享给好友</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<button class=\"cancel-btn\" @tap=\"hideSharePopup\">关闭</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部固定区域 -->\n\t\t<view class=\"footer-area\">\n\t\t\t<!-- 压缩设置 -->\n\t\t\t<view class=\"compress-settings glassmorphism\">\n\t\t\t\t<!-- 压缩质量滑块 -->\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-label\">压缩质量</text>\n\t\t\t\t\t\t<text class=\"setting-value glassmorphism\">{{quality}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"slider-container neumorphism-inset\">\n\t\t\t\t\t\t<slider :value=\"quality\" @change=\"onQualityChange\" @changing=\"onQualityChanging\" min=\"0\" max=\"100\"\n\t\t\t\t\t\t\tactiveColor=\"#07C160\" backgroundColor=\"rgba(7, 193, 96, 0.1)\"\n\t\t\t\t\t\t\tblock-color=\"#ffffff\" block-size=\"28\" step=\"1\"></slider>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 尺寸大小滑块 -->\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-label\">尺寸大小</text>\n\t\t\t\t\t\t<view class=\"size-controls\">\n\t\t\t\t\t\t\t<view class=\"size-display glassmorphism\" @tap=\"showSizePopup\" v-if=\"imagesList.length > 0\">\n\t\t\t\t\t\t\t\t<text>{{customWidth}} × {{customHeight}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"setting-value glassmorphism\">{{size}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"slider-container neumorphism-inset\">\n\t\t\t\t\t\t<slider :value=\"size\" @change=\"onSizeChange\" @changing=\"onSizeChanging\" min=\"5\" max=\"100\"\n\t\t\t\t\t\t\tactiveColor=\"#07C160\" backgroundColor=\"rgba(7, 193, 96, 0.1)\"\n\t\t\t\t\t\t\tblock-color=\"#ffffff\" block-size=\"28\" step=\"1\"></slider>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 底部按钮区域 -->\n\t\t\t<view class=\"button-group\">\n\t\t\t\t<button class=\"btn select-btn\" @tap=\"selectImages\">选择图片</button>\n\t\t\t\t<button class=\"btn compress-btn\" @tap=\"startCompression\" :disabled=\"imagesList.length === 0\">开始压缩</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport ShareButton from '@/components/shareButton.vue';\nimport { checkImageSecurity, handleCheckResult, batchCheckImages } from '@/utils/security.js';\nimport { isImageRisky } from '@/utils/securityStore.js';\nimport { getFreeCount, decreaseFreeCount, increaseFreeCount } from '@/utils/adCounter.js';\n\nexport default {\n\tcomponents: {\n\t\tShareButton\n\t},\n\tdata() {\n\t\treturn {\n\t\t\timagesList: [],\n\t\t\tquality: 80,\n\t\t\tsize: 100,\n\t\t\tcompressing: false,\n\t\t\tstatusBarHeight: 0,\n\t\t\tnavHeight: 0,\n\t\t\tcanvasWidth: 0,\n\t\t\tcanvasHeight: 0,\n\t\t\tcompressTimer: null,\n\t\t\tcurrentIndex: -1,\n\t\t\tstartTime: 0,\n\t\t\tendTime: 0,\n\t\t\tshowSizeInput: false,\n\t\t\ttempWidth: '',\n\t\t\ttempHeight: '',\n\t\t\tcustomWidth: '',\n\t\t\tcustomHeight: '',\n\t\t\tcompressMode: 'slider',\n\t\t\tselectionStart: 0,\n\t\t\tselectionEnd: 0,\n\t\t\tlastCompressParams: null,\n\t\t\tshowSharePopup: false,\n\t\t\t// 广告相关\n\t\t\tvideoAd: null\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取传入的图片列表\n\t\tif (options.images) {\n\t\t\ttry {\n\t\t\t\tconst tempFilePaths = JSON.parse(decodeURIComponent(options.images));\n\t\t\t\tif (Array.isArray(tempFilePaths) && tempFilePaths.length > 0) {\n\t\t\t\t\tthis.processImages(tempFilePaths);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('解析传入图片数据失败:', e);\n\t\t\t}\n\t\t}\n\n\t\t// 获取状态栏高度\n\t\tconst windowInfo = uni.getWindowInfo();\n\t\tthis.statusBarHeight = windowInfo.statusBarHeight;\n\t\t// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）\n\t\tthis.navHeight = this.statusBarHeight + 44;\n\n\t\t// 初始化激励视频广告\n\t\tthis.initRewardedVideoAd();\n\t},\n\tmethods: {\n\t\t// 初始化激励视频广告\n\t\tinitRewardedVideoAd() {\n\t\t\t// 仅在支持的环境下初始化广告\n\t\t\tif (wx.createRewardedVideoAd) {\n\t\t\t\tthis.videoAd = wx.createRewardedVideoAd({\n\t\t\t\t\tadUnitId: 'adunit-b7bbea52a631e115'\n\t\t\t\t});\n\n\t\t\t\t// 监听加载事件\n\t\t\t\tthis.videoAd.onLoad(() => {\n\t\t\t\t\tconsole.log('激励视频广告加载成功');\n\t\t\t\t});\n\n\t\t\t\t// 监听错误事件\n\t\t\t\tthis.videoAd.onError((err) => {\n\t\t\t\t\tconsole.error('激励视频广告加载失败', err);\n\t\t\t\t});\n\n\t\t\t\t// 监听关闭事件\n\t\t\t\tthis.videoAd.onClose((res) => {\n\t\t\t\t\t// 用户完整观看广告\n\t\t\t\t\tif (res && res.isEnded) {\n\t\t\t\t\t\t// 标记用户当天已观看广告，可以无限使用\n\t\t\t\t\t\tincreaseFreeCount(3); // 这个函数内部会调用 markAdWatchedToday()\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '今日可无限压缩，请点击开始压缩',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 不再自动触发压缩，让用户手动点击按钮\n\t\t\t\t\t\t// 移除了自动调用 continueCompression() 的代码\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 用户提前关闭广告，不给予奖励\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '需完整观看广告才能获得奖励',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 显示激励视频广告\n\t\tshowRewardedVideoAd() {\n\t\t\tif (this.videoAd) {\n\t\t\t\tthis.videoAd.show().catch(() => {\n\t\t\t\t\t// 失败重试\n\t\t\t\t\tthis.videoAd.load()\n\t\t\t\t\t\t.then(() => this.videoAd.show())\n\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\tconsole.error('激励视频广告显示失败', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '广告加载失败，请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 显示广告提示弹窗\n\t\tshowAdPrompt() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '免费次数已用完',\n\t\t\t\tcontent: '观看一个短视频，今日可无限压缩',\n\t\t\t\tconfirmText: '观看视频',\n\t\t\t\tcancelText: '稍后再说',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 用户点击\"观看视频\"\n\t\t\t\t\t\tthis.showRewardedVideoAd();\n\t\t\t\t\t}\n\t\t\t\t\t// 用户点击\"稍后再说\"，不做任何操作\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack({\n\t\t\t\tdelta: 1,\n\t\t\t\tfail: () => {\n\t\t\t\t\t// 如果无法返回上一页（例如直接打开此页面），则跳转到首页\n\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 处理分享事件\n\t\thandleShare() {\n\t\t\tif (this.imagesList.length > 0) {\n\t\t\t\t// 如果有压缩后的图片，优先分享第一张压缩后的图片\n\t\t\t\tconst hasCompressed = this.imagesList.some(item => item.compressedPath);\n\n\t\t\t\tif (hasCompressed) {\n\t\t\t\t\t// 查找第一个压缩后的图片\n\t\t\t\t\tconst firstCompressedImage = this.imagesList.find(item => item.compressedPath);\n\t\t\t\t\t// 分享压缩后的图片\n\t\t\t\t\tuni.showShareMenu({\n\t\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t\t\t\t\t});\n\n\t\t\t\t\t// 更新当前页面的分享信息\n\t\t\t\t\tconst currentParams = encodeURIComponent(JSON.stringify({\n\t\t\t\t\t\tquality: this.quality,\n\t\t\t\t\t\tsize: this.size,\n\t\t\t\t\t\tmode: this.compressMode,\n\t\t\t\t\t\tcustomWidth: this.customWidth,\n\t\t\t\t\t\tcustomHeight: this.customHeight\n\t\t\t\t\t}));\n\n\t\t\t\t\t// 保存当前分享为全局状态\n\t\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\t\tgetApp().globalData.shareInfo = {\n\t\t\t\t\t\ttitle: '我用图片压缩工具压缩了图片，效果不错！',\n\t\t\t\t\t\tpath: `/pages/index/index?from=share&params=${currentParams}`,\n\t\t\t\t\t\timageUrl: firstCompressedImage.compressedPath\n\t\t\t\t\t};\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请点击右上角分享',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 分享小程序\n\t\t\t\t\tuni.showShareMenu({\n\t\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t\t\t\t\t});\n\n\t\t\t\t\t// 更新当前页面的分享信息\n\t\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\t\tgetApp().globalData.shareInfo = {\n\t\t\t\t\t\ttitle: '推荐这个好用的图片压缩工具！',\n\t\t\t\t\t\tpath: '/pages/index/index?from=share',\n\t\t\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t\t\t};\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请点击右上角分享',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 分享小程序\n\t\t\t\tuni.showShareMenu({\n\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t\t\t\t});\n\n\t\t\t\t// 更新当前页面的分享信息\n\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\tgetApp().globalData.shareInfo = {\n\t\t\t\t\ttitle: '推荐这个好用的图片压缩工具！',\n\t\t\t\t\tpath: '/pages/index/index?from=share',\n\t\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t\t};\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请点击右上角分享',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 处理图片列表\n\t\tasync processImages(tempFilePaths) {\n\t\t\tfor (let i = 0; i < tempFilePaths.length; i++) {\n\t\t\t\tconst path = tempFilePaths[i];\n\t\t\t\ttry {\n\t\t\t\t\t// 在后台进行内容安全检测（完全静默，不影响用户体验）\n\t\t\t\t\t// 使用Promise.resolve().then()确保检测在后台进行，不阻塞UI\n\t\t\t\t\tPromise.resolve().then(async () => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 调用内容安全检测API\n\t\t\t\t\t\t\tconst checkResult = await checkImageSecurity(path);\n\n\t\t\t\t\t\t\t// 处理检测结果\n\t\t\t\t\t\t\thandleCheckResult(checkResult);\n\t\t\t\t\t\t\t// 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t// 只在控制台记录错误，不影响用户体验\n\t\t\t\t\t\t\tconsole.error('内容安全检测失败，继续处理图片:', error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tconst [fileInfo, imageInfo] = await Promise.all([\n\t\t\t\t\t\tnew Promise((resolve, reject) => {\n\t\t\t\t\t\t\tconst fs = uni.getFileSystemManager();\n\t\t\t\t\t\t\tfs.getFileInfo({\n\t\t\t\t\t\t\t\tfilePath: path,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}),\n\t\t\t\t\t\tnew Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.getImageInfo({\n\t\t\t\t\t\t\t\tsrc: path,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t]);\n\n\t\t\t\t\tthis.imagesList.push({\n\t\t\t\t\t\tpath: path,\n\t\t\t\t\t\twidth: imageInfo.width,\n\t\t\t\t\t\theight: imageInfo.height,\n\t\t\t\t\t\toriginalSize: Math.round(fileInfo.size / 1024),\n\t\t\t\t\t\tcompressedSize: null\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取图片信息失败:', error);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 处理完图片后，更新自定义尺寸显示\n\t\t\tthis.updateCustomSize();\n\n\t\t\t// 加载完图片后进行预览压缩\n\t\t\tif (this.imagesList.length > 0) {\n\t\t\t\tthis.previewCompression();\n\t\t\t}\n\t\t},\n\n\t\t// 选择图片\n\t\tselectImages() {\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['拍照', '从手机相册选择', '从聊天中选择'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tlet sourceType;\n\t\t\t\t\tswitch(res.tapIndex) {\n\t\t\t\t\t\tcase 0:\n\t\t\t\t\t\t\tsourceType = ['camera'];\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\tsourceType = ['album'];\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 2:\n\t\t\t\t\t\t\t// 从聊天中选择图片\n\t\t\t\t\t\t\tuni.chooseMessageFile({\n\t\t\t\t\t\t\t\tcount: 9,\n\t\t\t\t\t\t\t\ttype: 'image',\n\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\tconst tempFilePaths = res.tempFiles.map(file => file.path);\n\t\t\t\t\t\t\t\t\tthis.processImages(tempFilePaths);\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '选择图片失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tuni.chooseImage({\n\t\t\t\t\t\tcount: 9,\n\t\t\t\t\t\tsourceType: sourceType,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconst tempFilePaths = res.tempFilePaths;\n\t\t\t\t\t\t\tthis.processImages(tempFilePaths);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 删除单张图片\n\t\tdeleteImage(index) {\n\t\t\tthis.imagesList.splice(index, 1);\n\t\t},\n\n\t\t// 清空所有图片\n\t\tclearImages() {\n\t\t\tthis.imagesList = [];\n\t\t},\n\n\t\t// 压缩质量变化\n\t\tonQualityChange(e) {\n\t\t\tthis.quality = e.detail.value;\n\t\t\tif (this.compressTimer) clearTimeout(this.compressTimer);\n\t\t\tthis.compressTimer = setTimeout(() => {\n\t\t\t\t// 对第一张图片进行预览压缩\n\t\t\t\tthis.previewCompression();\n\t\t\t}, 300);\n\t\t},\n\n\t\t// 压缩质量滑动中\n\t\tonQualityChanging(e) {\n\t\t\tthis.quality = e.detail.value;\n\t\t\t// 更新所有图片项的压缩后信息状态\n\t\t\tthis.updateCompressionStatus('计算中...');\n\t\t},\n\n\t\t// 尺寸大小变化\n\t\tonSizeChange(e) {\n\t\t\tthis.compressMode = 'slider';\n\t\t\tthis.size = e.detail.value;\n\t\t\tthis.updateCustomSize();\n\t\t\tif (this.compressTimer) clearTimeout(this.compressTimer);\n\t\t\tthis.compressTimer = setTimeout(() => {\n\t\t\t\t// 对第一张图片进行预览压缩\n\t\t\t\tthis.previewCompression();\n\t\t\t}, 300);\n\t\t},\n\n\t\t// 尺寸滑动中\n\t\tonSizeChanging(e) {\n\t\t\tthis.size = e.detail.value;\n\t\t\tthis.updateCustomSize();\n\t\t\t// 更新所有图片项的压缩后信息状态\n\t\t\tthis.updateCompressionStatus('计算中...');\n\t\t},\n\n\t\t// 更新所有图片项的压缩状态\n\t\tupdateCompressionStatus(status) {\n\t\t\tfor(let i = 0; i < this.imagesList.length; i++) {\n\t\t\t\t// 只更新UI显示，不实际压缩\n\t\t\t\tthis.$set(this.imagesList[i], 'compressedSize', status);\n\t\t\t}\n\t\t},\n\n\t\t// 对第一张图片进行预览压缩\n\t\tasync previewCompression() {\n\t\t\tif(this.imagesList.length === 0) return;\n\t\t\tif(this.compressing) return;\n\n\t\t\ttry {\n\t\t\t\t// 更新状态显示为正在压缩\n\t\t\t\tthis.updateCompressionStatus('计算中...');\n\n\t\t\t\t// 只压缩第一张作为预览\n\t\t\t\tconst item = this.imagesList[0];\n\n\t\t\t\tlet targetWidth, targetHeight;\n\t\t\t\tlet canvas = null;\n\t\t\t\tlet ctx = null;\n\n\t\t\t\tif (this.compressMode === 'custom') {\n\t\t\t\t\t// 自定义尺寸模式，使用自定义输入的尺寸\n\t\t\t\t\ttargetWidth = parseInt(this.customWidth);\n\t\t\t\t\ttargetHeight = parseInt(this.customHeight);\n\n\t\t\t\t\t// 1. 计算宽度和高度的缩放比例\n\t\t\t\t\tconst widthRatio = targetWidth / item.width;\n\t\t\t\t\tconst heightRatio = targetHeight / item.height;\n\n\t\t\t\t\t// 2. 使用较大的缩放比例，确保图片完整显示\n\t\t\t\t\tconst scaleRatio = Math.max(widthRatio, heightRatio);\n\n\t\t\t\t\t// 3. 计算缩放后的尺寸\n\t\t\t\t\tconst scaledWidth = Math.round(item.width * scaleRatio);\n\t\t\t\t\tconst scaledHeight = Math.round(item.height * scaleRatio);\n\n\t\t\t\t\t// 4. 计算裁剪的起始位置（居中裁剪）\n\t\t\t\t\tconst cropX = Math.round((scaledWidth - targetWidth) / 2);\n\t\t\t\t\tconst cropY = Math.round((scaledHeight - targetHeight) / 2);\n\n\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\tthis.canvasWidth = targetWidth;\n\t\t\t\t\tthis.canvasHeight = targetHeight;\n\n\t\t\t\t\t// 等待一帧以确保 canvas 尺寸更新\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\n\n\t\t\t\t\t// 获取 canvas 上下文\n\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\ttry {\n\t\t\t\t\t\tcanvas = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tquery.select('#compressCanvas')\n\t\t\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\t\t\tif (res && res[0] && res[0].node) {\n\t\t\t\t\t\t\t\t\t\tresolve(res[0].node);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\treject(new Error('找不到canvas节点'));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tctx = canvas.getContext('2d');\n\n\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\tcanvas.width = targetWidth;\n\t\t\t\t\t\tcanvas.height = targetHeight;\n\n\t\t\t\t\t\t// 清空画布\n\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t// 创建图片对象\n\t\t\t\t\t\tconst image = canvas.createImage();\n\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\timage.onload = resolve;\n\t\t\t\t\t\t\timage.onerror = reject;\n\t\t\t\t\t\t\timage.src = item.path;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 先缩放，再裁剪\n\t\t\t\t\t\tctx.drawImage(\n\t\t\t\t\t\t\timage,\n\t\t\t\t\t\t\t0, 0, item.width, item.height,  // 源图像区域\n\t\t\t\t\t\t\t-cropX, -cropY, scaledWidth, scaledHeight  // 目标区域，通过调整x和y坐标实现居中裁剪\n\t\t\t\t\t\t);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('Canvas处理失败:', error);\n\t\t\t\t\t\tthrow new Error('Canvas处理失败: ' + error.message);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 滑块压缩模式，根据百分比计算\n\t\t\t\t\ttargetWidth = Math.floor(item.width * (this.size / 100));\n\t\t\t\t\ttargetHeight = Math.floor(item.height * (this.size / 100));\n\n\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\tthis.canvasWidth = targetWidth;\n\t\t\t\t\tthis.canvasHeight = targetHeight;\n\n\t\t\t\t\t// 等待一帧以确保 canvas 尺寸更新\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\n\n\t\t\t\t\t// 获取 canvas 上下文\n\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\ttry {\n\t\t\t\t\t\tcanvas = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tquery.select('#compressCanvas')\n\t\t\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\t\t\tif (res && res[0] && res[0].node) {\n\t\t\t\t\t\t\t\t\t\tresolve(res[0].node);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\treject(new Error('找不到canvas节点'));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tctx = canvas.getContext('2d');\n\n\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\tcanvas.width = targetWidth;\n\t\t\t\t\t\tcanvas.height = targetHeight;\n\n\t\t\t\t\t\t// 清空画布\n\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t// 创建图片对象\n\t\t\t\t\t\tconst image = canvas.createImage();\n\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\timage.onload = resolve;\n\t\t\t\t\t\t\timage.onerror = reject;\n\t\t\t\t\t\t\timage.src = item.path;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 绘制图片\n\t\t\t\t\t\tctx.drawImage(image, 0, 0, targetWidth, targetHeight);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('Canvas处理失败:', error);\n\t\t\t\t\t\tthrow new Error('Canvas处理失败: ' + error.message);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 确保canvas已定义\n\t\t\t\tif (!canvas) {\n\t\t\t\t\tthrow new Error('Canvas未初始化');\n\t\t\t\t}\n\n\t\t\t\t// 导出图片\n\t\t\t\tconst result = await new Promise((resolve, reject) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\t\t\tcanvas: canvas,\n\t\t\t\t\t\t\tfileType: 'jpg',\n\t\t\t\t\t\t\tquality: this.quality / 100,\n\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// 获取压缩后的图片信息\n\t\t\t\tconst compressedInfo = await new Promise((resolve, reject) => {\n\t\t\t\t\tconst fs = uni.getFileSystemManager();\n\t\t\t\t\tfs.getFileInfo({\n\t\t\t\t\t\tfilePath: result.tempFilePath,\n\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\tfail: reject\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\t// 获取压缩后的图片尺寸信息\n\t\t\t\tconst compressedImageInfo = await new Promise((resolve, reject) => {\n\t\t\t\t\tuni.getImageInfo({\n\t\t\t\t\t\tsrc: result.tempFilePath,\n\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\tfail: reject\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\t// 更新第一张图片的压缩后信息\n\t\t\t\tthis.$set(this.imagesList[0], 'compressedSize',\n\t\t\t\t\tMath.round(compressedInfo.size / 1024));\n\t\t\t\tthis.$set(this.imagesList[0], 'compressedPath', result.tempFilePath);\n\t\t\t\tthis.$set(this.imagesList[0], 'compressedWidth', compressedImageInfo.width);\n\t\t\t\tthis.$set(this.imagesList[0], 'compressedHeight', compressedImageInfo.height);\n\n\t\t\t\t// 更新所有其他图片的预估压缩后大小\n\t\t\t\tfor(let i = 1; i < this.imagesList.length; i++) {\n\t\t\t\t\tconst curItem = this.imagesList[i];\n\t\t\t\t\t// 使用第一张图片的压缩比例估算其他图片的压缩后大小\n\t\t\t\t\tconst compressionRatio = this.imagesList[0].compressedSize / this.imagesList[0].originalSize;\n\t\t\t\t\tconst estimatedSize = Math.round(curItem.originalSize * compressionRatio);\n\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedSize',\n\t\t\t\t\t\t`约 ${estimatedSize}`);\n\n\t\t\t\t\tif (this.compressMode === 'custom') {\n\t\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedWidth', targetWidth);\n\t\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedHeight', targetHeight);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedWidth',\n\t\t\t\t\t\t\tMath.floor(curItem.width * (this.size / 100)));\n\t\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedHeight',\n\t\t\t\t\t\t\tMath.floor(curItem.height * (this.size / 100)));\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 记录当前的压缩参数，以便后续判断是否需要重新压缩\n\t\t\t\tthis.lastCompressParams = {\n\t\t\t\t\tquality: this.quality,\n\t\t\t\t\tsize: this.size,\n\t\t\t\t\tcustomWidth: this.customWidth,\n\t\t\t\t\tcustomHeight: this.customHeight,\n\t\t\t\t\tcompressMode: this.compressMode\n\t\t\t\t};\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('预览压缩失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '压缩失败: ' + (error.message || '未知错误'),\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t\tthis.updateCompressionStatus('--');\n\t\t\t}\n\t\t},\n\n\t\t// 根据滑块值更新自定义尺寸\n\t\tupdateCustomSize() {\n\t\t\t// 如果没有图片，无需计算\n\t\t\tif(this.imagesList.length === 0) return;\n\n\t\t\t// 以第一张图片为基准计算预览尺寸\n\t\t\tconst firstImage = this.imagesList[0];\n\t\t\tconst newWidth = Math.floor(firstImage.width * (this.size / 100));\n\t\t\tconst newHeight = Math.floor(firstImage.height * (this.size / 100));\n\n\t\t\tthis.customWidth = newWidth.toString();\n\t\t\tthis.customHeight = newHeight.toString();\n\t\t},\n\n\t\t// 显示尺寸设置弹窗\n\t\tshowSizePopup() {\n\t\t\tthis.tempWidth = '';\n\t\t\tthis.tempHeight = '';\n\t\t\tthis.showSizeInput = true;\n\t\t},\n\n\t\t// 关闭尺寸设置弹窗\n\t\thideSizePopup() {\n\t\t\tthis.showSizeInput = false;\n\t\t\tthis.tempWidth = '';\n\t\t\tthis.tempHeight = '';\n\t\t},\n\n\t\t// 确认自定义尺寸\n\t\tconfirmSize() {\n\t\t\tthis.compressMode = 'custom';\n\t\t\tconst width = parseInt(this.tempWidth);\n\t\t\tconst height = parseInt(this.tempHeight);\n\n\t\t\tif (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入有效的尺寸',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.customWidth = width.toString();\n\t\t\tthis.customHeight = height.toString();\n\n\t\t\tthis.hideSizePopup();\n\n\t\t\t// 确保图片列表不为空\n\t\t\tif (this.imagesList.length > 0) {\n\t\t\t\t// 清除之前的计时器，避免多次调用\n\t\t\t\tif (this.compressTimer) clearTimeout(this.compressTimer);\n\n\t\t\t\t// 显示计算中状态\n\t\t\t\tthis.updateCompressionStatus('计算中...');\n\n\t\t\t\t// 添加一个短暂延迟，确保UI更新后再进行压缩\n\t\t\t\tthis.compressTimer = setTimeout(() => {\n\t\t\t\t\t// 进行预览压缩\n\t\t\t\t\tthis.previewCompression();\n\t\t\t\t}, 100);\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择图片',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 输入框获取焦点时全选文本\n\t\thandleInputFocus(event) {\n\t\t\t// 使用延时确保在输入框获得焦点后执行选中\n\t\t\tsetTimeout(() => {\n\t\t\t\tconst value = event.target.value;\n\t\t\t\tif (value) {\n\t\t\t\t\tthis.selectionStart = 0;\n\t\t\t\t\tthis.selectionEnd = value.toString().length;\n\t\t\t\t}\n\t\t\t}, 100);\n\t\t},\n\n\t\t// 开始压缩\n\t\tasync startCompression() {\n\t\t\tif (this.compressing || this.imagesList.length === 0) return;\n\n\t\t\t// 检查免费次数\n\t\t\tconst freeCount = getFreeCount();\n\t\t\tif (freeCount <= 0) {\n\t\t\t\t// 免费次数用完，显示广告提示\n\t\t\t\tthis.showAdPrompt();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 显示加载提示\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '检查图片中...',\n\t\t\t\tmask: true\n\t\t\t});\n\n\t\t\ttry {\n\t\t\t\t// 批量检查所有图片是否违规，使用本地存储的结果，不查询服务器\n\t\t\t\t// 因为轮询已经在后台持续更新结果\n\t\t\t\tconst imagePaths = this.imagesList.map(item => item.path);\n\t\t\t\tconst checkResults = await batchCheckImages(imagePaths, false);\n\n\t\t\t\t// 检查是否有正在检测中的图片\n\t\t\t\tconst checkingImages = checkResults.filter(result => result.isChecking);\n\t\t\t\tif (checkingImages.length > 0) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '部分图片安全检测尚未完成，请稍后再试',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 检查是否有违规图片\n\t\t\t\tconst riskyImages = checkResults.filter(result => result.isRisky);\n\t\t\t\tif (riskyImages.length > 0) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: `检测到${riskyImages.length}张不合规的图片，无法进行压缩`,\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 隐藏加载提示\n\t\t\t\tuni.hideLoading();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('[安全检测] 批量检查图片失败:', error);\n\t\t\t\tuni.hideLoading();\n\t\t\t\t// 出错时继续执行，不阻止用户操作\n\t\t\t}\n\n\t\t\tthis.compressing = true;\n\t\t\tthis.startTime = Date.now();\n\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '压缩中...',\n\t\t\t\tmask: true\n\t\t\t});\n\n\t\t\ttry {\n\t\t\t\t// 验证当前的压缩参数是否与预览时相同\n\t\t\t\tconst currentParams = {\n\t\t\t\t\tquality: this.quality,\n\t\t\t\t\tsize: this.size,\n\t\t\t\t\tcustomWidth: this.customWidth,\n\t\t\t\t\tcustomHeight: this.customHeight,\n\t\t\t\t\tcompressMode: this.compressMode\n\t\t\t\t};\n\n\t\t\t\tconst paramsChanged = !this.lastCompressParams ||\n\t\t\t\t\tJSON.stringify(currentParams) !== JSON.stringify(this.lastCompressParams);\n\n\t\t\t\t// 如果参数发生变化或者第一张图片没有压缩过，需要重新压缩第一张\n\t\t\t\tif (paramsChanged || !this.imagesList[0].compressedPath) {\n\t\t\t\t\tawait this.previewCompression();\n\t\t\t\t}\n\n\t\t\t\t// 从第二张图片开始压缩，第一张已经在预览时压缩好了\n\t\t\t\tfor (let i = 1; i < this.imagesList.length; i++) {\n\t\t\t\t\tthis.currentIndex = i;\n\t\t\t\t\tconst item = this.imagesList[i];\n\n\t\t\t\t\tlet targetWidth, targetHeight;\n\t\t\t\t\tlet canvas = null;\n\t\t\t\t\tlet ctx = null;\n\n\t\t\t\t\tif (this.compressMode === 'custom') {\n\t\t\t\t\t\t// 自定义尺寸模式，使用自定义输入的尺寸\n\t\t\t\t\t\ttargetWidth = parseInt(this.customWidth);\n\t\t\t\t\t\ttargetHeight = parseInt(this.customHeight);\n\n\t\t\t\t\t\t// 1. 计算宽度和高度的缩放比例\n\t\t\t\t\t\tconst widthRatio = targetWidth / item.width;\n\t\t\t\t\t\tconst heightRatio = targetHeight / item.height;\n\n\t\t\t\t\t\t// 2. 使用较大的缩放比例，确保图片完整显示\n\t\t\t\t\t\tconst scaleRatio = Math.max(widthRatio, heightRatio);\n\n\t\t\t\t\t\t// 3. 计算缩放后的尺寸\n\t\t\t\t\t\tconst scaledWidth = Math.round(item.width * scaleRatio);\n\t\t\t\t\t\tconst scaledHeight = Math.round(item.height * scaleRatio);\n\n\t\t\t\t\t\t// 4. 计算裁剪的起始位置（居中裁剪）\n\t\t\t\t\t\tconst cropX = Math.round((scaledWidth - targetWidth) / 2);\n\t\t\t\t\t\tconst cropY = Math.round((scaledHeight - targetHeight) / 2);\n\n\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\tthis.canvasWidth = targetWidth;\n\t\t\t\t\t\tthis.canvasHeight = targetHeight;\n\n\t\t\t\t\t\t// 等待一帧以确保 canvas 尺寸更新\n\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\n\n\t\t\t\t\t\t// 获取 canvas 上下文\n\t\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tcanvas = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\t\tquery.select('#compressCanvas')\n\t\t\t\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\t\t\t\tif (res && res[0] && res[0].node) {\n\t\t\t\t\t\t\t\t\t\t\tresolve(res[0].node);\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\treject(new Error('找不到canvas节点'));\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tctx = canvas.getContext('2d');\n\n\t\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\t\tcanvas.width = targetWidth;\n\t\t\t\t\t\t\tcanvas.height = targetHeight;\n\n\t\t\t\t\t\t\t// 清空画布\n\t\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t\t// 创建图片对象\n\t\t\t\t\t\t\tconst image = canvas.createImage();\n\t\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\t\timage.onload = resolve;\n\t\t\t\t\t\t\t\timage.onerror = reject;\n\t\t\t\t\t\t\t\timage.src = item.path;\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t// 先缩放，再裁剪\n\t\t\t\t\t\t\tctx.drawImage(\n\t\t\t\t\t\t\t\timage,\n\t\t\t\t\t\t\t\t0, 0, item.width, item.height,  // 源图像区域\n\t\t\t\t\t\t\t\t-cropX, -cropY, scaledWidth, scaledHeight  // 目标区域，通过调整x和y坐标实现居中裁剪\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('Canvas处理失败:', error);\n\t\t\t\t\t\t\tthrow new Error('Canvas处理失败: ' + error.message);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 滑块压缩模式，根据百分比计算\n\t\t\t\t\t\ttargetWidth = Math.floor(item.width * (this.size / 100));\n\t\t\t\t\t\ttargetHeight = Math.floor(item.height * (this.size / 100));\n\n\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\tthis.canvasWidth = targetWidth;\n\t\t\t\t\t\tthis.canvasHeight = targetHeight;\n\n\t\t\t\t\t\t// 等待一帧以确保 canvas 尺寸更新\n\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\n\n\t\t\t\t\t\t// 获取 canvas 上下文\n\t\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tcanvas = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\t\tquery.select('#compressCanvas')\n\t\t\t\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\t\t\t\tif (res && res[0] && res[0].node) {\n\t\t\t\t\t\t\t\t\t\t\tresolve(res[0].node);\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\treject(new Error('找不到canvas节点'));\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tctx = canvas.getContext('2d');\n\n\t\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\t\tcanvas.width = targetWidth;\n\t\t\t\t\t\t\tcanvas.height = targetHeight;\n\n\t\t\t\t\t\t\t// 清空画布\n\t\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t\t// 创建图片对象\n\t\t\t\t\t\t\tconst image = canvas.createImage();\n\t\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\t\timage.onload = resolve;\n\t\t\t\t\t\t\t\timage.onerror = reject;\n\t\t\t\t\t\t\t\timage.src = item.path;\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t// 绘制图片\n\t\t\t\t\t\t\tctx.drawImage(image, 0, 0, targetWidth, targetHeight);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('Canvas处理失败:', error);\n\t\t\t\t\t\t\tthrow new Error('Canvas处理失败: ' + error.message);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 确保canvas已定义\n\t\t\t\t\tif (!canvas) {\n\t\t\t\t\t\tthrow new Error('Canvas未初始化');\n\t\t\t\t\t}\n\n\t\t\t\t\t// 导出图片\n\t\t\t\t\tconst result = await new Promise((resolve, reject) => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\t\t\t\tcanvas: canvas,\n\t\t\t\t\t\t\t\tfileType: 'jpg',\n\t\t\t\t\t\t\t\tquality: this.quality / 100,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t// 获取压缩后的图片信息\n\t\t\t\t\tconst compressedInfo = await new Promise((resolve, reject) => {\n\t\t\t\t\t\tconst fs = uni.getFileSystemManager();\n\t\t\t\t\t\tfs.getFileInfo({\n\t\t\t\t\t\t\tfilePath: result.tempFilePath,\n\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\t// 获取压缩后的图片尺寸信息\n\t\t\t\t\tconst compressedImageInfo = await new Promise((resolve, reject) => {\n\t\t\t\t\t\tuni.getImageInfo({\n\t\t\t\t\t\t\tsrc: result.tempFilePath,\n\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\t// 更新压缩后的信息\n\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedSize',\n\t\t\t\t\t\tMath.round(compressedInfo.size / 1024));\n\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedPath', result.tempFilePath);\n\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedWidth', compressedImageInfo.width);\n\t\t\t\t\tthis.$set(this.imagesList[i], 'compressedHeight', compressedImageInfo.height);\n\n\t\t\t\t\t// 更新压缩进度提示\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: `压缩中(${i+1}/${this.imagesList.length})`,\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tthis.endTime = Date.now();\n\t\t\t\tthis.currentIndex = -1;\n\n\t\t\t\t// 减少免费次数（如果不是无限使用状态）\n\t\t\t\tconst currentFreeCount = getFreeCount();\n\t\t\t\tif (currentFreeCount < 999) {\n\t\t\t\t\tdecreaseFreeCount();\n\t\t\t\t}\n\n\t\t\t\t// 压缩完成，保存图片\n\t\t\t\tawait this.saveCompressedImages();\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 计算耗时\n\t\t\t\tconst timeCost = ((this.endTime - this.startTime) / 1000).toFixed(1);\n\n\t\t\t\t// 显示分享弹窗\n\t\t\t\tthis.showSharePopup = true;\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('压缩失败:', error);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '压缩失败: ' + (error.message || '未知错误'),\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.compressing = false;\n\t\t\t\tthis.currentIndex = -1;\n\t\t\t}\n\t\t},\n\n\t\t// 保存压缩后的图片\n\t\tasync saveCompressedImages() {\n\t\t\tconst savedImages = [];\n\n\t\t\ttry {\n\t\t\t\tfor (const item of this.imagesList) {\n\t\t\t\t\tif (item.compressedPath) {\n\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\t\t\t\tfilePath: item.compressedPath,\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tsavedImages.push(item);\n\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tif (err.errMsg.includes('auth deny')) {\n\t\t\t\t\t\t\t\t\t\t// 权限问题，直接结束循环\n\t\t\t\t\t\t\t\t\t\treject(new Error('需要相册权限'));\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t// 其他错误，继续尝试保存其他图片\n\t\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (savedImages.length === 0 && this.imagesList.some(item => item.compressedPath)) {\n\t\t\t\t\t// 没有成功保存任何图片，但有压缩过的图片，可能是权限问题\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '需要保存相册权限，是否去设置？',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\t// 处理权限问题\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '需要保存相册权限，是否去设置？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tonShareAppMessage(res) {\n\t\t\t// 判断是否有全局分享信息\n\t\t\tif (getApp().globalData && getApp().globalData.shareInfo) {\n\t\t\t\treturn getApp().globalData.shareInfo;\n\t\t\t}\n\t\t\t// 默认分享信息\n\t\t\treturn {\n\t\t\t\ttitle: '推荐这个好用的图片压缩工具！',\n\t\t\t\tpath: '/pages/index/index?from=share',\n\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t};\n\t\t},\n\t\tonShareTimeline() {\n\t\t\t// 判断是否有全局分享信息\n\t\t\tif (getApp().globalData && getApp().globalData.shareInfo) {\n\t\t\t\treturn {\n\t\t\t\t\ttitle: getApp().globalData.shareInfo.title,\n\t\t\t\t\tpath: getApp().globalData.shareInfo.path,\n\t\t\t\t\timageUrl: getApp().globalData.shareInfo.imageUrl\n\t\t\t\t};\n\t\t\t}\n\t\t\t// 默认分享信息\n\t\t\treturn {\n\t\t\t\ttitle: '推荐这个好用的图片压缩工具！',\n\t\t\t\tpath: '/pages/index/index?from=share',\n\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t};\n\t\t},\n\t\t// 隐藏分享弹窗\n\t\thideSharePopup() {\n\t\t\tthis.showSharePopup = false;\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n// 使用uni.scss中定义的主题色变量\n$primary-color: $uni-color-primary; // 主题色：微信绿\n$primary-gradient: $theme-color-primary-gradient;\n$bg-color: $uni-bg-color-grey; // 背景色：微信灰\n$text-primary: $uni-text-color; // 主要文字颜色\n$text-secondary: $theme-text-secondary; // 次要文字颜色\n$text-tertiary: $uni-text-color-grey; // 辅助文字颜色\n$link-color: $theme-color-link; // 链接/高亮文字颜色\n$border-color: $uni-border-color; // 边框颜色\n$shadow-dark: $theme-shadow-dark;\n$shadow-light: $theme-shadow-light;\n\npage {\n\tbackground-color: $bg-color;\n}\n\n// 新拟物风格的混入\n@mixin neumorphism {\n\tbackground: $bg-color;\n\tbox-shadow: 12px 12px 24px $shadow-dark,\n\t\t\t\t-8px -8px 20px $shadow-light,\n\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.5),\n\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\n\tborder: 1px solid rgba(255, 255, 255, 0.8);\n}\n\n@mixin neumorphism-inset {\n\tbackground: $bg-color;\n\tbox-shadow: inset 6px 6px 12px $shadow-dark,\n\t\t\t\tinset -6px -6px 12px $shadow-light;\n}\n\n// 磨砂玻璃风格的混入\n@mixin glassmorphism {\n\tbackground: rgba($bg-color, 0.98);\n\tbackdrop-filter: blur(10px);\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.neumorphism {\n\t@include neumorphism;\n}\n\n.neumorphism-inset {\n\t@include neumorphism-inset;\n}\n\n.glassmorphism {\n\t@include glassmorphism;\n}\n\n.image-compression {\n\tpadding: 0 30rpx 30rpx;\n\tposition: relative;\n\tmin-height: 100vh;\n\tbox-sizing: border-box;\n\tpadding-bottom: 260rpx; /* 为底部区域预留空间 */\n\n\t.custom-nav {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 100;\n\t\tpadding: 0 30rpx;\n\n\t\t.nav-content {\n\t\t\theight: 44px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.back-btn {\n\t\t\t\twidth: 60rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.wx-nav-back {\n\t\t\t\t\twidth: 12px;\n\t\t\t\t\theight: 24px;\n\t\t\t\t\tbackground-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='24' viewBox='0 0 12 24'%3E%3Cpath fill-opacity='.9' fill-rule='evenodd' d='M10 19.438L8.955 20.5l-7.666-7.79a1 1 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z'/%3E%3C/svg%3E\");\n\t\t\t\t\tbackground-size: cover;\n\t\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.nav-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: $text-primary;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t.share-button-container {\n\t\t\t\tmin-width: 36px;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-end;\n\t\t\t\tmargin-right: 180rpx;  /* 增加右边距，让按钮与微信官方按钮保持一定距离 */\n\t\t\t}\n\n\t\t\t.placeholder {\n\t\t\t\twidth: 60rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main-content {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\n\t.image-list {\n\t\tmargin: 20rpx 0;\n\t\tborder-radius: 30rpx;\n\t\tpadding: 30rpx;\n\n\t\t.section-title {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 10rpx 0 20rpx;\n\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\n\t\t\ttext {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: $text-primary;\n\t\t\t}\n\n\t\t\t.clear-btn {\n\t\t\t\tcolor: $primary-color;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tpadding: 6rpx 20rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\t@include glassmorphism;\n\n\t\t\t\t&:active {\n\t\t\t\t\topacity: 0.8;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.image-item {\n\t\t\tposition: relative;\n\t\t\tpadding: 20rpx 0;\n\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\t\t\tdisplay: flex;\n\n\t\t\t.preview-image {\n\t\t\t\twidth: 135rpx;\n\t\t\t\theight: 135rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\n\t\t\t.image-info {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.info-line {\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\t&:first-child {\n\t\t\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\t\t\tmargin-bottom: 2rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.progress-bar {\n\t\t\t\t\theight: 6rpx;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tbackground-color: rgba(7, 193, 96, 0.1);\n\t\t\t\t\tborder-radius: 3rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tmargin-top: 4rpx;\n\n\t\t\t\t\t.progress-inner {\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\t\ttransition: width 0.2s linear;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.delete-btn {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 10rpx;\n\t\t\t\ttop: 20rpx;\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\tcolor: #999;\n\t\t\t\tfont-size: 32rpx;\n\n\t\t\t\t&:active {\n\t\t\t\t\topacity: 0.8;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.tip-text {\n\t\t\tpadding: 20rpx 0;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: $text-secondary;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.empty-state {\n\t\t\tpadding: 50rpx 0;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tcursor: pointer;\n\t\t\tborder-radius: 20rpx;\n\t\t\ttransition: all 0.3s ease;\n\t\t\tbackground: linear-gradient(145deg, #ffffff, #f0f0f0);\n\t\t\tbox-shadow: 20px 20px 40px rgba(0, 0, 0, 0.15),\n\t\t\t\t\t\t-12px -12px 24px rgba(255, 255, 255, 0.95),\n\t\t\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.9),\n\t\t\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\n\t\t\tborder: 1px solid rgba(255, 255, 255, 0.8);\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.98);\n\t\t\t\tbackground: linear-gradient(145deg, #f0f0f0, #ffffff);\n\t\t\t\tbox-shadow: inset 10px 10px 20px rgba(0, 0, 0, 0.1),\n\t\t\t\t\t\t\tinset -10px -10px 20px rgba(255, 255, 255, 0.95);\n\t\t\t}\n\n\t\t\t.upload-icon {\n\t\t\t\twidth: 120rpx;\n\t\t\t\theight: 120rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\topacity: 0.8;\n\t\t\t\tfilter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.1));\n\t\t\t}\n\n\t\t\ttext {\n\t\t\t\tcolor: $text-primary;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\ttext-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);\n\t\t\t}\n\n\t\t\t.click-hint {\n\t\t\t\tcolor: $text-secondary;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\ttext-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);\n\t\t\t}\n\t\t}\n\t}\n\n\t.footer-area {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tpadding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));\n\t\tz-index: 100;\n\n\t\t.compress-settings {\n\t\t\tborder-radius: 20rpx;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.setting-item {\n\t\t\t\tmargin-bottom: 15rpx;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\t.setting-header {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\n\t\t\t\t\t.setting-label {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\n\t\t\t\t\t.size-controls {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tgap: 10rpx;\n\n\t\t\t\t\t\t.size-display {\n\t\t\t\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\tmin-width: 120rpx;\n\n\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.setting-value {\n\t\t\t\t\t\t\tmin-width: 60rpx;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.slider-container {\n\t\t\t\t\tpadding: 30rpx 20rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\n\t\t\t\t\t::v-deep .uni-slider {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t::v-deep .uni-slider-handle {\n\t\t\t\t\t\twidth: 56rpx;\n\t\t\t\t\t\theight: 56rpx;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\t\t\t\t}\n\n\t\t\t\t\t::v-deep .uni-slider-track {\n\t\t\t\t\t\theight: 4rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.button-group {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.btn {\n\t\t\t\twidth: 45%;\n\t\t\t\theight: 88rpx;\n\t\t\t\tline-height: 88rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder-radius: 44rpx;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tborder: none;\n\n\t\t\t\t&.select-btn {\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\tborder: 2rpx solid rgba(7, 193, 96, 0.3);\n\t\t\t\t\tbackground: $bg-color;\n\t\t\t\t\tbox-shadow: 8px 8px 16px $shadow-dark,\n\t\t\t\t\t\t\t\t-6px -6px 12px $shadow-light,\n\t\t\t\t\t\t\t\tinset 1px 1px 2px rgba(255, 255, 255, 0.5);\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t\tbox-shadow: inset 6px 6px 12px $shadow-dark,\n\t\t\t\t\t\t\t\t\tinset -6px -6px 12px $shadow-light;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.compress-btn {\n\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tbox-shadow: 8px 8px 16px rgba(7, 193, 96, 0.2),\n\t\t\t\t\t\t\t\t-4px -4px 12px rgba(255, 255, 255, 0.8),\n\t\t\t\t\t\t\t\tinset 1px 1px 2px rgba(255, 255, 255, 0.3);\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t\tbox-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&[disabled] {\n\t\t\t\t\topacity: 0.5;\n\t\t\t\t\tbackground: #E5E5E5;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.size-popup {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 999;\n\n\t\t.popup-mask {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground: rgba(0, 0, 0, 0.4);\n\t\t\tbackdrop-filter: blur(4px);\n\t\t}\n\n\t\t.popup-content {\n\t\t\tposition: absolute;\n\t\t\tleft: 50%;\n\t\t\ttop: 50%;\n\t\t\ttransform: translate(-50%, -50%);\n\t\t\twidth: 80%;\n\t\t\tmax-width: 600rpx;\n\t\t\tbackground: $bg-color;\n\t\t\tborder-radius: 30rpx;\n\t\t\toverflow: hidden;\n\n\t\t\t.popup-header {\n\t\t\t\tpadding: 20rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tposition: relative;\n\t\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\n\t\t\t\t.popup-title {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t}\n\n\t\t\t\t.popup-close {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 20rpx;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\tpadding: 10rpx;\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\topacity: 0.7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.popup-body {\n\t\t\t\tpadding: 30rpx;\n\n\t\t\t\t.input-group {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\tinput {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\tbackground: #fff;\n\t\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t\t\tpadding: 0 24rpx;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\t\tbox-shadow: inset 2rpx 2rpx 5rpx rgba(0, 0, 0, 0.1);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.popup-buttons {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tgap: 20rpx;\n\t\t\t\t\tmargin-top: 30rpx;\n\n\t\t\t\t\tbutton {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tborder: none;\n\n\t\t\t\t\t\t&.cancel-btn {\n\t\t\t\t\t\t\tbackground: rgba(0, 0, 0, 0.05);\n\t\t\t\t\t\t\tcolor: $text-secondary;\n\n\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.confirm-btn {\n\t\t\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\t\t\tcolor: #fff;\n\n\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.share-popup {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 999;\n\n\t\t.popup-mask {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground: rgba(0, 0, 0, 0.4);\n\t\t\tbackdrop-filter: blur(4px);\n\t\t}\n\n\t\t.popup-content {\n\t\t\tposition: absolute;\n\t\t\tleft: 50%;\n\t\t\ttop: 50%;\n\t\t\ttransform: translate(-50%, -50%);\n\t\t\twidth: 80%;\n\t\t\tmax-width: 600rpx;\n\t\t\tbackground: $bg-color;\n\t\t\tborder-radius: 30rpx;\n\t\t\toverflow: hidden;\n\n\t\t\t.popup-header {\n\t\t\t\tpadding: 20rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tposition: relative;\n\t\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\n\t\t\t\t.popup-title {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t}\n\n\t\t\t\t.popup-close {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 20rpx;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\tpadding: 10rpx;\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\topacity: 0.7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.popup-body {\n\t\t\t\tpadding: 30rpx;\n\n\t\t\t\t.share-content {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 30rpx;\n\n\t\t\t\t\t.share-icon {\n\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.share-title {\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.share-desc {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.share-buttons {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tgap: 20rpx;\n\n\t\t\t\t\t.share-btn {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.iconfont {\n\t\t\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.cancel-btn {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tbackground: rgba(0, 0, 0, 0.03);\n\t\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752290702499\n      var cssReload = require(\"D:/1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}