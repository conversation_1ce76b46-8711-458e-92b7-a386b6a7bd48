const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'danmu_share',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 20,
  queueLimit: 0,
  idleTimeout: 60000,         // 空闲60秒后释放连接
  acquireTimeout: 60000       // 获取连接超时时间60秒
});

// 测试数据库连接
const testConnection = async () => {
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection();
    
    // 执行简单查询测试连接
    await connection.execute('SELECT 1');
    
    console.log('数据库连接测试成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      host: dbConfig.host,
      database: dbConfig.database,
      user: dbConfig.user
    });
    throw error;
  }
};

// 初始化数据库表（使用新的表结构）
const initDatabase = async () => {
  try {
    const connection = await pool.getConnection();

    console.log('正在初始化数据库表...');
    console.log('⚠️  请确保已经执行了 database.sql 文件来创建完整的表结构');
    console.log('这里只检查关键表是否存在...');

    // 检查关键表是否存在
    const tables = ['users', 'danmu_shares', 'products', 'user_memberships', 'orders'];

    for (const tableName of tables) {
      const [rows] = await connection.execute(
        `SELECT COUNT(*) as count FROM information_schema.tables
         WHERE table_schema = DATABASE() AND table_name = ?`,
        [tableName]
      );

      if (rows[0].count === 0) {
        console.warn(`⚠️  表 ${tableName} 不存在，请执行 database.sql 文件`);
      } else {
        console.log(`✅ 表 ${tableName} 存在`);
      }
    }

    console.log('数据库表检查完成');
    connection.release();
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    throw error;
  }
};

// 获取数据库统计信息
const getStats = async () => {
  try {
    const connection = await pool.getConnection();
    
    const [totalRows] = await connection.execute(
      'SELECT COUNT(*) as total FROM configs'
    );
    
    const [activeRows] = await connection.execute(
      'SELECT COUNT(*) as active FROM configs WHERE expires_at IS NULL OR expires_at > NOW()'
    );
    
    const [expiredRows] = await connection.execute(
      'SELECT COUNT(*) as expired FROM configs WHERE expires_at IS NOT NULL AND expires_at <= NOW()'
    );
    
    connection.release();
    
    return {
      total: totalRows[0].total,
      active: activeRows[0].active,
      expired: expiredRows[0].expired
    };
  } catch (error) {
    console.error('获取数据库统计失败:', error);
    throw error;
  }
};

// 获取数据库连接的便捷方法
const getConnection = async () => {
  return await pool.getConnection();
};

module.exports = {
  pool,
  getConnection,
  testConnection,
  initDatabase,
  getStats
};
