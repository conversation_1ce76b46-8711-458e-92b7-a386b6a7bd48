{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/index/index.vue?b554", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/index/index.vue?6fae", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/index/index.vue?9e9f", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/index/index.vue?c0c2", "uni-app:///pages/index/index.vue", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/index/index.vue?4c9f", "webpack:///D:/02_Cursor/5_uniApp/2_imageCompression/02_图像压缩花花版/pages/index/index.vue?483f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "ShareButton", "data", "statusBarHeight", "navHeight", "tempImagePath", "quality", "imageSize", "customWidth", "customHeight", "originalSize", "originalWidth", "originalHeight", "compressedImagePath", "lastCompressParams", "compressMode", "compressProgress", "canvasWidth", "canvasHeight", "compressedSize", "compressedWidth", "compressedHeight", "showSizeInput", "temp<PERSON>idth", "tempHeight", "selectionStart", "selectionEnd", "showSharePopup", "videoAd", "onReady", "windowInfo", "appBaseInfo", "methods", "initRewardedVideoAd", "adUnitId", "console", "uni", "title", "icon", "duration", "showRewardedVideoAd", "then", "catch", "showAdPrompt", "content", "confirmText", "cancelText", "success", "handleProfileClick", "url", "handleShare", "withShareTicket", "menus", "getApp", "path", "imageUrl", "getOriginalImageInfo", "Promise", "fs", "filePath", "fail", "src", "fileInfo", "imageInfo", "chooseImage", "itemList", "res", "sourceType", "count", "type", "tempFilePaths", "checkResult", "onQualityChange", "onQualityChanging", "onSizeChange", "onSizeChanging", "updateCustomSize", "showSizePopup", "hideSizePopup", "confirmSize", "compressImage", "currentParams", "JSON", "targetWidth", "targetHeight", "widthRatio", "heightRatio", "scaleRatio", "scaledWidth", "scaledHeight", "cropX", "cropY", "query", "fields", "node", "size", "exec", "resolve", "canvas", "ctx", "image", "fileType", "result", "compressedInfo", "getImageInfo", "fileSize", "width", "height", "handleCompressClick", "freeCount", "mask", "showCancel", "currentFreeCount", "handleInputFocus", "setTimeout", "hideSharePopup", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,iMAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAouB,CAAgB,mtBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC2JxvB;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAR;QACAC;QACAC;QACAC;QACAM;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAT;MACAU;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAC;cACAC;cACA;cACA;cACA;;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;UACAC;QACA;;QAEA;QACA;UACAC;QACA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACA;UACA;YACA;YACA;;YAEAC;cACAC;cACAC;cACAC;YACA;;YAEA;YACA;UACA;YACA;YACAH;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACA;QACA;UACA;UACA,sBACAC;YAAA;UAAA,GACAC;YACAP;YACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACAP;QACAC;QACAO;QACAC;QACAC;QACAC;UACA;YACA;YACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAZ;QACAa;MACA;IACA;IAEA;IACAC;MACA;QACA;QACAd;UACAe;UACAC;QACA;;QAEA;QACA;UACA9C;UACAC;UACAC;UACAC;UACAM;QACA;;QAEA;QACAsC;QACAA;UACAhB;UACAiB;UACAC;QACA;QAEAnB;UACAC;UACAC;UACAC;QACA;MACA;QACA;QACAH;UACAe;UACAC;QACA;;QAEA;QACAC;QACAA;UACAhB;UACAiB;UACAC;QACA;QAEAnB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC,aACA;kBACA;kBACAC;oBACAC;oBACAZ;oBACAa;kBACA;gBACA,IACA;kBACAxB;oBACAyB;oBACAd;oBACAa;kBACA;gBACA,GACA;cAAA;gBAAA;gBAAA;gBAhBAE;gBAAAC;gBAkBA;gBACA;gBACA;gBACA;gBAAA,kCAEA;kBAAAD;kBAAAC;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3B;kBACAC;kBACAC;gBACA;gBAAA,kCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0B;MAAA;MACA5B;QACA6B;QACAlB;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,eAEAmB;oBAAA,kCACA,yBAGA,yBAGA;oBAAA;kBAAA;oBALAC;oBAAA;kBAAA;oBAGAA;oBAAA;kBAAA;oBAGA;oBACA/B;sBACAgC;sBACAC;sBACAtB;wBAAA;0BAAA;0BAAA;4BAAA;8BAAA;gCAAA;kCAAA,MAEAmB;oCAAA;oCAAA;kCAAA;kCACA;kCACAI;oCAAA;kCAAA;kCACAlC;oCACAa;kCACA;kCAAA;kCAAA;gCAAA;kCAEA;kCACAU,kCAEA;kCACA;kCAAA;kCAAA,OACA;gCAAA;kCACA;kCACA;;kCAEA;kCACAF;oCAAA;oCAAA;sCAAA;wCAAA;0CAAA;4CAAA;4CAAA;4CAAA,OAGA;0CAAA;4CAAAc;4CAEA;4CACA;4CACA;4CAAA;4CAAA;0CAAA;4CAAA;4CAAA;4CAEA;4CACApC;0CAAA;0CAAA;4CAAA;wCAAA;sCAAA;oCAAA;kCAAA,CAEA;gCAAA;gCAAA;kCAAA;8BAAA;4BAAA;0BAAA;wBAAA,CAEA;wBAAA;0BAAA;wBAAA;wBAAA;sBAAA;sBACAyB;wBACAxB;0BACAC;0BACAC;wBACA;sBACA;oBACA;oBAAA;kBAAA;oBAIAF;sBACAgC;sBAAA;sBACAD;sBACApB;wBAAA;0BAAA;0BAAA;4BAAA;8BAAA;gCAAA;kCAAA,MAEAmB;oCAAA;oCAAA;kCAAA;kCACA;kCACA9B;oCACAa;kCACA;kCAAA;kCAAA;gCAAA;kCAEA;kCACAU,iCAEA;kCACA;kCAAA;kCAAA,OACA;gCAAA;kCACA;kCACA;;kCAEA;kCACAF;oCAAA;oCAAA;sCAAA;wCAAA;0CAAA;4CAAA;4CAAA;4CAAA,OAGA;0CAAA;4CAAAc;4CAEA;4CACA;4CACA;4CAAA;4CAAA;0CAAA;4CAAA;4CAAA;4CAEA;4CACApC;0CAAA;0CAAA;4CAAA;wCAAA;sCAAA;oCAAA;kCAAA,CAEA;gCAAA;gCAAA;kCAAA;8BAAA;4BAAA;0BAAA;wBAAA,CAEA;wBAAA;0BAAA;wBAAA;wBAAA;sBAAA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACAqC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MAEA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA3C;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;MAEA;MACA;IACA;IACA0C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;gBACA;gBAEAC;kBACA3E;kBACAC;kBACAC;kBACAC;kBACAM;gBACA;gBAAA,MAEA,8BACAmE;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBACAC;;gBAEA;gBACAC;gBACAC,oDAEA;gBACAC,gDAEA;gBACAC;gBACAC,+DAEA;gBACAC;gBACAC,uDAEA;gBACAC;gBAAA;gBAAA,OACA;kBACAA,gCACAC;oBAAAC;oBAAAC;kBAAA,GACAC;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAQAC,+BAEA;gBACAD;gBACAA;;gBAEA;gBACAC;;gBAEA;gBACAC;gBAAA;gBAAA,OACA;kBACAA;kBACAA;kBACAA;gBACA;cAAA;gBAEA;gBACAD,cACAC,OACA;gBAAA;gBACA;gBAAA,CACA;;gBAEA;gBAAA;gBAAA,OACA;kBACAhE;oBACA8D;oBACAG;oBACA/F;oBACAyC;oBACAa;kBACA;gBACA;cAAA;gBARA0C;gBAUA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAEA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACApB;gBACAC;;gBAEA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAEA;gBACAQ;gBAAA;gBAAA,OACA;kBACAA,iCACAC;oBAAAC;oBAAAC;kBAAA,GACAC;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAQAC,iCAEA;gBACAD;gBACAA;;gBAEA;gBACAC;;gBAEA;gBACAC;gBAAA;gBAAA,OACA;kBACAA;kBACAA;kBACAA;gBACA;cAAA;gBAEA;gBACAD;;gBAEA;gBAAA;gBAAA,OACA;kBACA/D;oBACA8D;oBACAG;oBACA/F;oBACAyC;oBACAa;kBACA;gBACA;cAAA;gBARA0C;gBAUA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAEA;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGApE;gBACA;gBACA;gBACA;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAkE;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA/C,aACA;kBACA;kBACAC;oBACAC;oBACAZ;oBACAa;kBACA;gBACA,IACA;kBACAxB;oBACAyB;oBACAd;oBACAa;kBACA;gBACA,GACA;cAAA;gBAAA;gBAAA;gBAhBAE;gBAAAC;gBAAA,kCAkBA;kBACA0C;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;gBAAA,MAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAzE;kBACAC;kBACAyE;gBACA;;gBAEA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAvC;gBAEA;gBACAnC;;gBAEA;gBAAA,KACAmC;kBAAA;kBAAA;gBAAA;gBACAnC;kBACAC;kBACAO;kBACAmE;gBACA;gBAAA;cAAA;gBAAA,KAKAxC;kBAAA;kBAAA;gBAAA;gBACAnC;kBACAC;kBACAO;kBACAmE;gBACA;gBAAA;cAAA;gBAIA;gBACAC;gBACA;kBACA;gBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAIA;kBACA5E;oBACAuB;oBACAZ;oBACAa;kBACA;gBACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACAxB;kBACAC;kBACAO;kBACAG;oBACA;sBACAX;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA6E;MAAA;MACA;MACAC;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACA/E;QACAiB;QACAC;MACA;IACA;IACA;IACA8D;MACA;MACA;QACA;UACAhF;UACAiB;UACAC;QACA;MACA;MACA;MACA;QACAlB;QACAiB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACh3BA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"image-compress-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-nav glassmorphism\">\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<view class=\"nav-content\">\n\t\t\t\t<view class=\"profile-button-container\">\n\t\t\t\t\t<button class=\"profile-btn\" @tap=\"handleProfileClick\">\n\t\t\t\t\t\t<text class=\"profile-text\">我</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"nav-title\">图片压缩</text>\n\t\t\t\t<view class=\"share-button-container\">\n\t\t\t\t\t<share-button @share=\"handleShare\" :showText=\"false\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 主要内容区域 -->\n\t\t<view class=\"main-content\" :style=\"{ paddingTop: navHeight + 'px' }\">\n\t\t\t<!-- 图片上传区域 -->\n\t\t\t<view class=\"upload-section\">\n\t\t\t\t<view class=\"upload-box neumorphism\" @tap=\"chooseImage\" v-if=\"!tempImagePath\">\n\t\t\t\t\t<image src=\"/static/upload.svg\" mode=\"aspectFit\" class=\"upload-icon\"></image>\n\t\t\t\t\t<text class=\"upload-text\">点击上传图片</text>\n\t\t\t\t\t<text class=\"upload-desc\">(支持批量压缩)</text>\n\t\t\t\t</view>\n\t\t\t\t<image v-else :src=\"tempImagePath\" mode=\"aspectFit\" class=\"preview-image neumorphism\" @tap=\"chooseImage\"></image>\n\t\t\t</view>\n\n\t\t\t<!-- 图片信息显示区域 -->\n\t\t\t<view class=\"image-info neumorphism\" v-if=\"tempImagePath\">\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text>压缩前大小: {{originalSize}}</text>\n\t\t\t\t\t<text>压缩后大小: {{compressedSize || '-'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t<text>压缩前尺寸: {{originalWidth}} × {{originalHeight}}</text>\n\t\t\t\t\t<text>压缩后尺寸: {{compressedWidth || '-'}} × {{compressedHeight || '-'}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 压缩设置区域 -->\n\t\t\t<view class=\"compress-settings neumorphism\">\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-label\">压缩质量</text>\n\t\t\t\t\t\t<view class=\"size-controls\">\n\t\t\t\t\t\t\t<text class=\"setting-value glassmorphism\">{{quality}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"slider-container neumorphism-inset\">\n\t\t\t\t\t\t<slider :value=\"quality\" @change=\"onQualityChange\" @changing=\"onQualityChanging\" min=\"0\" max=\"100\"\n\t\t\t\t\t\t\tactiveColor=\"#07C160\" backgroundColor=\"rgba(7, 193, 96, 0.1)\"\n\t\t\t\t\t\t\tblock-color=\"#ffffff\" block-size=\"28\" step=\"1\"></slider>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-label\">尺寸大小</text>\n\t\t\t\t\t\t<view class=\"size-controls\">\n\t\t\t\t\t\t\t<view class=\"size-display glassmorphism\" @tap=\"showSizePopup\" v-if=\"tempImagePath\">\n\t\t\t\t\t\t\t\t<text>{{customWidth}} × {{customHeight}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"setting-value glassmorphism\">{{imageSize}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"slider-container neumorphism-inset\">\n\t\t\t\t\t\t<slider :value=\"imageSize\" @change=\"onSizeChange\" @changing=\"onSizeChanging\" min=\"5\" max=\"100\"\n\t\t\t\t\t\t\tactiveColor=\"#07C160\" backgroundColor=\"rgba(7, 193, 96, 0.1)\"\n\t\t\t\t\t\t\tblock-color=\"#ffffff\" block-size=\"28\" step=\"1\"></slider>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 底部按钮 -->\n\t\t\t<view class=\"bottom-buttons\">\n\t\t\t\t<button class=\"btn select-btn neumorphism\" @tap=\"chooseImage\">选择图片</button>\n\t\t\t\t<button class=\"btn compress-btn glassmorphism\" @tap=\"handleCompressClick\" :disabled=\"!tempImagePath\">开始压缩</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 添加隐藏的canvas -->\n\t\t<canvas\n\t\t\ttype=\"2d\"\n\t\t\tid=\"compressCanvas\"\n\t\t\t:style=\"{\n\t\t\t\twidth: `${canvasWidth}px`,\n\t\t\t\theight: `${canvasHeight}px`,\n\t\t\t\tposition: 'fixed',\n\t\t\t\tleft: '-9999px'\n\t\t\t}\"\n\t\t></canvas>\n\n\t\t<!-- 添加用于图片预处理的隐藏canvas -->\n\t\t<canvas\n\t\t\ttype=\"2d\"\n\t\t\tid=\"uploadPreprocessCanvas\"\n\t\t\tstyle=\"position: fixed; left: -9999px; width: 300px; height: 300px;\"\n\t\t></canvas>\n\n\t\t<!-- 尺寸输入弹出层 -->\n\t\t<view class=\"size-popup\" v-if=\"showSizeInput\">\n\t\t\t<view class=\"popup-mask\" @tap=\"hideSizePopup\"></view>\n\t\t\t<view class=\"popup-content neumorphism\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">设置图片尺寸</text>\n\t\t\t\t\t<text class=\"popup-close\" @tap=\"hideSizePopup\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text>宽度</text>\n\t\t\t\t\t\t<input type=\"number\" v-model=\"tempWidth\" placeholder=\"输入宽度\" :selection-start=\"0\" :selection-end=\"-1\" @focus=\"handleInputFocus\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text>高度</text>\n\t\t\t\t\t\t<input type=\"number\" v-model=\"tempHeight\" placeholder=\"输入高度\" :selection-start=\"0\" :selection-end=\"-1\" @focus=\"handleInputFocus\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup-buttons\">\n\t\t\t\t\t\t<button class=\"cancel-btn\" @tap=\"hideSizePopup\">取消</button>\n\t\t\t\t\t\t<button class=\"confirm-btn\" @tap=\"confirmSize\">确定</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 分享弹窗 -->\n\t\t<view class=\"share-popup\" v-if=\"showSharePopup\">\n\t\t\t<view class=\"popup-mask\" @tap=\"hideSharePopup\"></view>\n\t\t\t<view class=\"popup-content neumorphism\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text class=\"popup-title\">压缩成功</text>\n\t\t\t\t\t<text class=\"popup-close\" @tap=\"hideSharePopup\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"popup-body\">\n\t\t\t\t\t<view class=\"share-content\">\n\t\t\t\t\t\t<image src=\"/static/share-icon.png\" mode=\"aspectFit\" class=\"share-icon\"></image>\n\t\t\t\t\t\t<text class=\"share-title\">图片已保存到相册</text>\n\t\t\t\t\t\t<text class=\"share-desc\">分享给好友一起使用吧</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"share-buttons\">\n\t\t\t\t\t\t<button class=\"share-btn\" open-type=\"share\">\n\t\t\t\t\t\t\t<text class=\"iconfont icon-wechat\"></text>\n\t\t\t\t\t\t\t<text>分享给好友</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<button class=\"cancel-btn\" @tap=\"hideSharePopup\">关闭</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport ShareButton from '@/components/shareButton.vue';\n\timport { checkImageSecurity, handleCheckResult } from '@/utils/security.js';\n\timport { isImageRisky } from '@/utils/securityStore.js';\n\timport { getFreeCount, decreaseFreeCount, increaseFreeCount } from '@/utils/adCounter.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tShareButton\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatusBarHeight: 0,\n\t\t\t\tnavHeight: 0,\n\t\t\t\ttempImagePath: '',\n\t\t\t\tquality: 80,\n\t\t\t\timageSize: 100,\n\t\t\t\tcustomWidth: '',\n\t\t\t\tcustomHeight: '',\n\t\t\t\toriginalSize: '',\n\t\t\t\toriginalWidth: 0,\n\t\t\t\toriginalHeight: 0,\n\t\t\t\tcompressedImagePath: '',\n\t\t\t\tlastCompressParams: {\n\t\t\t\t\tquality: 0,\n\t\t\t\t\timageSize: 0,\n\t\t\t\t\tcustomWidth: '',\n\t\t\t\t\tcustomHeight: '',\n\t\t\t\t\tcompressMode: null\n\t\t\t\t},\n\t\t\t\tcompressProgress: 0,\n\t\t\t\tcanvasWidth: 0,\n\t\t\t\tcanvasHeight: 0,\n\t\t\t\tcompressedSize: '-',\n\t\t\t\tcompressedWidth: '-',\n\t\t\t\tcompressedHeight: '-',\n\t\t\t\tshowSizeInput: false,\n\t\t\t\ttempWidth: '',\n\t\t\t\ttempHeight: '',\n\t\t\t\tcompressMode: null,\n\t\t\t\tselectionStart: 0,\n\t\t\t\tselectionEnd: 0,\n\t\t\t\tshowSharePopup: false,\n\t\t\t\t// 广告相关\n\t\t\t\tvideoAd: null\n\t\t\t}\n\t\t},\n\t\tasync onReady() {\n\t\t\t// 获取状态栏高度\n\t\t\tconst windowInfo = uni.getWindowInfo()\n\t\t\tconst appBaseInfo = uni.getAppBaseInfo()\n\t\t\tthis.statusBarHeight = windowInfo.statusBarHeight\n\t\t\t// 导航栏总高度 = 状态栏高度 + 44（导航内容高度）\n\t\t\tthis.navHeight = this.statusBarHeight + 44\n\n\t\t\t// 初始化激励视频广告\n\t\t\tthis.initRewardedVideoAd();\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化激励视频广告\n\t\t\tinitRewardedVideoAd() {\n\t\t\t\t// 仅在支持的环境下初始化广告\n\t\t\t\tif (wx.createRewardedVideoAd) {\n\t\t\t\t\tthis.videoAd = wx.createRewardedVideoAd({\n\t\t\t\t\t\tadUnitId: 'adunit-b7bbea52a631e115'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 监听加载事件\n\t\t\t\t\tthis.videoAd.onLoad(() => {\n\t\t\t\t\t\tconsole.log('激励视频广告加载成功');\n\t\t\t\t\t});\n\n\t\t\t\t\t// 监听错误事件\n\t\t\t\t\tthis.videoAd.onError((err) => {\n\t\t\t\t\t\tconsole.error('激励视频广告加载失败', err);\n\t\t\t\t\t});\n\n\t\t\t\t\t// 监听关闭事件\n\t\t\t\t\tthis.videoAd.onClose((res) => {\n\t\t\t\t\t\t// 用户完整观看广告\n\t\t\t\t\t\tif (res && res.isEnded) {\n\t\t\t\t\t\t\t// 标记用户当天已观看广告，可以无限使用\n\t\t\t\t\t\t\tincreaseFreeCount(3); // 这个函数内部会调用 markAdWatchedToday()\n\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '今日可无限压缩，请点击开始压缩',\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t// 不再自动触发压缩，让用户手动点击按钮\n\t\t\t\t\t\t\t// 移除了自动调用 continueCompression() 的代码\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 用户提前关闭广告，不给予奖励\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '需完整观看广告才能获得奖励',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 显示激励视频广告\n\t\t\tshowRewardedVideoAd() {\n\t\t\t\tif (this.videoAd) {\n\t\t\t\t\tthis.videoAd.show().catch(() => {\n\t\t\t\t\t\t// 失败重试\n\t\t\t\t\t\tthis.videoAd.load()\n\t\t\t\t\t\t\t.then(() => this.videoAd.show())\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tconsole.error('激励视频广告显示失败', err);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '广告加载失败，请稍后重试',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 显示广告提示弹窗\n\t\t\tshowAdPrompt() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '免费次数已用完',\n\t\t\t\t\tcontent: '观看一个短视频，今日可无限压缩',\n\t\t\t\t\tconfirmText: '观看视频',\n\t\t\t\t\tcancelText: '稍后再说',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 用户点击\"观看视频\"\n\t\t\t\t\t\t\tthis.showRewardedVideoAd();\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 用户点击\"稍后再说\"，不做任何操作\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 处理\"我\"按钮点击\n\t\t\thandleProfileClick() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/profile/index'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 处理分享按钮点击\n\t\t\thandleShare() {\n\t\t\t\tif (this.compressedImagePath) {\n\t\t\t\t\t// 如果有压缩后的图片，分享压缩后的图片\n\t\t\t\t\tuni.showShareMenu({\n\t\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t\t\t\t\t});\n\n\t\t\t\t\t// 更新当前页面的分享信息\n\t\t\t\t\tconst currentParams = encodeURIComponent(JSON.stringify({\n\t\t\t\t\t\tquality: this.quality,\n\t\t\t\t\t\timageSize: this.imageSize,\n\t\t\t\t\t\tcustomWidth: this.customWidth,\n\t\t\t\t\t\tcustomHeight: this.customHeight,\n\t\t\t\t\t\tcompressMode: this.compressMode\n\t\t\t\t\t}));\n\n\t\t\t\t\t// 保存当前分享为全局状态\n\t\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\t\tgetApp().globalData.shareInfo = {\n\t\t\t\t\t\ttitle: '我用图片压缩工具压缩了图片，效果不错！',\n\t\t\t\t\t\tpath: `/pages/index/index?from=share&params=${currentParams}`,\n\t\t\t\t\t\timageUrl: this.compressedImagePath\n\t\t\t\t\t};\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请点击右上角分享',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 没有压缩过的图片，分享小程序\n\t\t\t\t\tuni.showShareMenu({\n\t\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\n\t\t\t\t\t});\n\n\t\t\t\t\t// 更新当前页面的分享信息\n\t\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\t\tgetApp().globalData.shareInfo = {\n\t\t\t\t\t\ttitle: '推荐这个好用的图片压缩工具！',\n\t\t\t\t\t\tpath: '/pages/index/index?from=share',\n\t\t\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t\t\t};\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请点击右上角分享',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取原始图片信息\n\t\t\tasync getOriginalImageInfo(filePath) {\n\t\t\t\ttry {\n\t\t\t\t\tconst [fileInfo, imageInfo] = await Promise.all([\n\t\t\t\t\t\tnew Promise((resolve, reject) => {\n\t\t\t\t\t\t\tconst fs = uni.getFileSystemManager();\n\t\t\t\t\t\t\tfs.getFileInfo({\n\t\t\t\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}),\n\t\t\t\t\t\tnew Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.getImageInfo({\n\t\t\t\t\t\t\t\tsrc: filePath,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t]);\n\n\t\t\t\t\t// 更新原始图片信息\n\t\t\t\t\tthis.originalSize = (fileInfo.size / 1024).toFixed(2) + ' KB';\n\t\t\t\t\tthis.originalWidth = imageInfo.width;\n\t\t\t\t\tthis.originalHeight = imageInfo.height;\n\n\t\t\t\t\treturn { fileInfo, imageInfo };\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取图片信息失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 选择图片\n\t\t\tchooseImage() {\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: ['拍照', '从手机相册选择', '从聊天中选择'],\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\tlet sourceType;\n\t\t\t\t\t\tswitch(res.tapIndex) {\n\t\t\t\t\t\t\tcase 0:\n\t\t\t\t\t\t\t\tsourceType = ['camera'];\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\t\tsourceType = ['album'];\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 2:\n\t\t\t\t\t\t\t\t// 从聊天中选择图片\n\t\t\t\t\t\t\t\tuni.chooseMessageFile({\n\t\t\t\t\t\t\t\t\tcount: 999,\n\t\t\t\t\t\t\t\t\ttype: 'image',\n\t\t\t\t\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\t\t\t\t\t// 判断选择的图片数量\n\t\t\t\t\t\t\t\t\t\tif (res.tempFiles.length > 1) {\n\t\t\t\t\t\t\t\t\t\t\t// 多张图片，跳转到批量处理页面\n\t\t\t\t\t\t\t\t\t\t\tconst tempFilePaths = res.tempFiles.map(file => file.path);\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/batchImageCompression/index?images=' + encodeURIComponent(JSON.stringify(tempFilePaths))\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t// 单张图片，在当前页面处理\n\t\t\t\t\t\t\t\t\t\t\tconst filePath = res.tempFiles[0].path;\n\n\t\t\t\t\t\t\t\t\t\t\t// 设置图片路径并获取信息\n\t\t\t\t\t\t\t\t\t\t\tthis.tempImagePath = filePath;\n\t\t\t\t\t\t\t\t\t\t\tawait this.getOriginalImageInfo(this.tempImagePath);\n\t\t\t\t\t\t\t\t\t\t\tthis.customWidth = this.originalWidth.toString();\n\t\t\t\t\t\t\t\t\t\t\tthis.customHeight = this.originalHeight.toString();\n\n\t\t\t\t\t\t\t\t\t\t\t// 在后台进行内容安全检测（完全静默，不影响用户体验）\n\t\t\t\t\t\t\t\t\t\t\tPromise.resolve().then(async () => {\n\t\t\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 调用内容安全检测API\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst checkResult = await checkImageSecurity(filePath);\n\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 处理检测结果\n\t\t\t\t\t\t\t\t\t\t\t\t\thandleCheckResult(checkResult);\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果\n\t\t\t\t\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 只在控制台记录错误，不影响用户体验\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.error('内容安全检测失败:', error);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '选择图片失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.chooseImage({\n\t\t\t\t\t\t\tcount: 999, // 设置一个较大的数字，实际上不限制数量\n\t\t\t\t\t\t\tsourceType: sourceType,\n\t\t\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\t\t\t// 判断选择的图片数量\n\t\t\t\t\t\t\t\tif (res.tempFilePaths.length > 1) {\n\t\t\t\t\t\t\t\t\t// 多张图片，跳转到批量处理页面\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/batchImageCompression/index?images=' + encodeURIComponent(JSON.stringify(res.tempFilePaths))\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t// 单张图片，在当前页面处理\n\t\t\t\t\t\t\t\t\tconst filePath = res.tempFilePaths[0];\n\n\t\t\t\t\t\t\t\t\t// 设置图片路径并获取信息\n\t\t\t\t\t\t\t\t\tthis.tempImagePath = filePath;\n\t\t\t\t\t\t\t\t\tawait this.getOriginalImageInfo(this.tempImagePath);\n\t\t\t\t\t\t\t\t\tthis.customWidth = this.originalWidth.toString();\n\t\t\t\t\t\t\t\t\tthis.customHeight = this.originalHeight.toString();\n\n\t\t\t\t\t\t\t\t\t// 在后台进行内容安全检测（完全静默，不影响用户体验）\n\t\t\t\t\t\t\t\t\tPromise.resolve().then(async () => {\n\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\t// 调用内容安全检测API\n\t\t\t\t\t\t\t\t\t\t\tconst checkResult = await checkImageSecurity(filePath);\n\n\t\t\t\t\t\t\t\t\t\t\t// 处理检测结果\n\t\t\t\t\t\t\t\t\t\t\thandleCheckResult(checkResult);\n\t\t\t\t\t\t\t\t\t\t\t// 注意：我们不在这里拦截，只在用户点击压缩按钮时检查结果\n\t\t\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\t\t\t// 只在控制台记录错误，不影响用户体验\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('内容安全检测失败:', error);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tonQualityChange(e) {\n\t\t\t\tthis.quality = e.detail.value;\n\t\t\t\tif (this.compressTimer) clearTimeout(this.compressTimer);\n\t\t\t\tthis.compressTimer = setTimeout(() => {\n\t\t\t\t\tthis.compressImage();\n\t\t\t\t}, 300);\n\t\t\t},\n\t\t\tonQualityChanging(e) {\n\t\t\t\tthis.quality = e.detail.value;\n\t\t\t\tthis.compressedSize = '计算中...';\n\t\t\t\tthis.compressedWidth = '-';\n\t\t\t\tthis.compressedHeight = '-';\n\t\t\t},\n\t\t\tonSizeChange(e) {\n\t\t\t\tthis.compressMode = 'slider';\n\t\t\t\tthis.imageSize = e.detail.value;\n\t\t\t\tthis.updateCustomSize();\n\t\t\t\tif (this.compressTimer) clearTimeout(this.compressTimer);\n\t\t\t\tthis.compressTimer = setTimeout(() => {\n\t\t\t\t\tthis.compressImage();\n\t\t\t\t}, 300);\n\t\t\t},\n\t\t\tonSizeChanging(e) {\n\t\t\t\tthis.imageSize = e.detail.value;\n\t\t\t\tthis.updateCustomSize();\n\t\t\t\tthis.compressedSize = '计算中...';\n\t\t\t\tthis.compressedWidth = '-';\n\t\t\t\tthis.compressedHeight = '-';\n\t\t\t},\n\t\t\tupdateCustomSize() {\n\t\t\t\tconst newWidth = Math.floor(this.originalWidth * (this.imageSize / 100));\n\t\t\t\tconst newHeight = Math.floor(this.originalHeight * (this.imageSize / 100));\n\n\t\t\t\tthis.customWidth = newWidth.toString();\n\t\t\t\tthis.customHeight = newHeight.toString();\n\t\t\t},\n\t\t\tshowSizePopup() {\n\t\t\t\tthis.tempWidth = '';\n\t\t\t\tthis.tempHeight = '';\n\t\t\t\tthis.showSizeInput = true;\n\t\t\t},\n\t\t\thideSizePopup() {\n\t\t\t\tthis.showSizeInput = false;\n\t\t\t\tthis.tempWidth = '';\n\t\t\t\tthis.tempHeight = '';\n\t\t\t},\n\t\t\tconfirmSize() {\n\t\t\t\tthis.compressMode = 'custom';\n\t\t\t\tconst width = parseInt(this.tempWidth);\n\t\t\t\tconst height = parseInt(this.tempHeight);\n\n\t\t\t\tif (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入有效的尺寸',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.customWidth = width.toString();\n\t\t\t\tthis.customHeight = height.toString();\n\n\t\t\t\tthis.hideSizePopup();\n\t\t\t\tthis.compressImage();\n\t\t\t},\n\t\t\tasync compressImage() {\n\t\t\t\tif (!this.tempImagePath) return;\n\n\t\t\t\ttry {\n\t\t\t\t\tthis.compressedSize = '压缩中...';\n\t\t\t\t\tthis.compressedWidth = '-';\n\t\t\t\t\tthis.compressedHeight = '-';\n\n\t\t\t\t\tconst currentParams = {\n\t\t\t\t\t\tquality: this.quality,\n\t\t\t\t\t\timageSize: this.imageSize,\n\t\t\t\t\t\tcustomWidth: this.customWidth,\n\t\t\t\t\t\tcustomHeight: this.customHeight,\n\t\t\t\t\t\tcompressMode: this.compressMode\n\t\t\t\t\t};\n\n\t\t\t\t\tif (this.compressedImagePath &&\n\t\t\t\t\t\tJSON.stringify(currentParams) === JSON.stringify(this.lastCompressParams)) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tlet targetWidth, targetHeight;\n\t\t\t\t\tif (this.compressMode === 'custom') {\n\t\t\t\t\t\t// 手动输入尺寸模式\n\t\t\t\t\t\ttargetWidth = parseInt(this.customWidth);\n\t\t\t\t\t\ttargetHeight = parseInt(this.customHeight);\n\n\t\t\t\t\t\t// 1. 计算宽度和高度的缩放比例\n\t\t\t\t\t\tconst widthRatio = targetWidth / this.originalWidth;\n\t\t\t\t\t\tconst heightRatio = targetHeight / this.originalHeight;\n\n\t\t\t\t\t\t// 2. 使用较大的缩放比例，确保图片完整显示\n\t\t\t\t\t\tconst scaleRatio = Math.max(widthRatio, heightRatio);\n\n\t\t\t\t\t\t// 3. 计算缩放后的尺寸\n\t\t\t\t\t\tconst scaledWidth = Math.round(this.originalWidth * scaleRatio);\n\t\t\t\t\t\tconst scaledHeight = Math.round(this.originalHeight * scaleRatio);\n\n\t\t\t\t\t\t// 4. 计算裁剪的起始位置（居中裁剪）\n\t\t\t\t\t\tconst cropX = Math.round((scaledWidth - targetWidth) / 2);\n\t\t\t\t\t\tconst cropY = Math.round((scaledHeight - targetHeight) / 2);\n\n\t\t\t\t\t\t// 获取 canvas 上下文\n\t\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\t\tconst canvas = await new Promise(resolve => {\n\t\t\t\t\t\t\tquery.select('#compressCanvas')\n\t\t\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\t\t\tresolve(res[0].node);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tconst ctx = canvas.getContext('2d');\n\n\t\t\t\t\t\t// 设置 canvas 尺寸为目标尺寸\n\t\t\t\t\t\tcanvas.width = targetWidth;\n\t\t\t\t\t\tcanvas.height = targetHeight;\n\n\t\t\t\t\t\t// 清空画布\n\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t// 创建图片对象\n\t\t\t\t\t\tconst image = canvas.createImage();\n\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\timage.onload = resolve;\n\t\t\t\t\t\t\timage.onerror = reject;\n\t\t\t\t\t\t\timage.src = this.tempImagePath;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 先缩放，再裁剪\n\t\t\t\t\t\tctx.drawImage(\n\t\t\t\t\t\t\timage,\n\t\t\t\t\t\t\t0, 0, this.originalWidth, this.originalHeight,  // 源图像区域\n\t\t\t\t\t\t\t-cropX, -cropY, scaledWidth, scaledHeight  // 目标区域，通过调整x和y坐标实现居中裁剪\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// 导出图片\n\t\t\t\t\t\tconst result = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\t\t\t\tcanvas: canvas,\n\t\t\t\t\t\t\t\tfileType: 'jpg',\n\t\t\t\t\t\t\t\tquality: this.quality / 100,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tthis.compressedImagePath = result.tempFilePath;\n\t\t\t\t\t\tthis.lastCompressParams = currentParams;\n\n\t\t\t\t\t\tconst compressedInfo = await this.getImageInfo(this.compressedImagePath);\n\n\t\t\t\t\t\t// 更新压缩后的信息\n\t\t\t\t\t\tthis.compressedSize = (compressedInfo.fileSize / 1024).toFixed(2) + ' KB';\n\t\t\t\t\t\tthis.compressedWidth = compressedInfo.width;\n\t\t\t\t\t\tthis.compressedHeight = compressedInfo.height;\n\n\t\t\t\t\t\treturn;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 滑块压缩模式\n\t\t\t\t\t\ttargetWidth = Math.floor(this.originalWidth * (this.imageSize / 100));\n\t\t\t\t\t\ttargetHeight = Math.floor(this.originalHeight * (this.imageSize / 100));\n\n\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\tthis.canvasWidth = targetWidth;\n\t\t\t\t\t\tthis.canvasHeight = targetHeight;\n\n\t\t\t\t\t\t// 等待一帧以确保 canvas 尺寸更新\n\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 50));\n\n\t\t\t\t\t\t// 获取 canvas 上下文\n\t\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\t\tconst canvas = await new Promise(resolve => {\n\t\t\t\t\t\t\tquery.select('#compressCanvas')\n\t\t\t\t\t\t\t\t.fields({ node: true, size: true })\n\t\t\t\t\t\t\t\t.exec((res) => {\n\t\t\t\t\t\t\t\t\tresolve(res[0].node);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tconst ctx = canvas.getContext('2d');\n\n\t\t\t\t\t\t// 设置 canvas 尺寸\n\t\t\t\t\t\tcanvas.width = targetWidth;\n\t\t\t\t\t\tcanvas.height = targetHeight;\n\n\t\t\t\t\t\t// 清空画布\n\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t// 创建图片对象\n\t\t\t\t\t\tconst image = canvas.createImage();\n\t\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\t\timage.onload = resolve;\n\t\t\t\t\t\t\timage.onerror = reject;\n\t\t\t\t\t\t\timage.src = this.tempImagePath;\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 绘制图片\n\t\t\t\t\t\tctx.drawImage(image, 0, 0, targetWidth, targetHeight);\n\n\t\t\t\t\t\t// 导出图片\n\t\t\t\t\t\tconst result = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.canvasToTempFilePath({\n\t\t\t\t\t\t\t\tcanvas: canvas,\n\t\t\t\t\t\t\t\tfileType: 'jpg',\n\t\t\t\t\t\t\t\tquality: this.quality / 100,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tthis.compressedImagePath = result.tempFilePath;\n\t\t\t\t\t\tthis.lastCompressParams = currentParams;\n\n\t\t\t\t\t\tconst compressedInfo = await this.getImageInfo(this.compressedImagePath);\n\n\t\t\t\t\t\t// 更新压缩后的信息\n\t\t\t\t\t\tthis.compressedSize = (compressedInfo.fileSize / 1024).toFixed(2) + ' KB';\n\t\t\t\t\t\tthis.compressedWidth = compressedInfo.width;\n\t\t\t\t\t\tthis.compressedHeight = compressedInfo.height;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('压缩失败:', error);\n\t\t\t\t\tthis.compressedSize = '-';\n\t\t\t\t\tthis.compressedWidth = '-';\n\t\t\t\t\tthis.compressedHeight = '-';\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '压缩失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync getImageInfo(filePath) {\n\t\t\t\ttry {\n\t\t\t\t\tconst [fileInfo, imageInfo] = await Promise.all([\n\t\t\t\t\t\tnew Promise((resolve, reject) => {\n\t\t\t\t\t\t\tconst fs = uni.getFileSystemManager();\n\t\t\t\t\t\t\tfs.getFileInfo({\n\t\t\t\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}),\n\t\t\t\t\t\tnew Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.getImageInfo({\n\t\t\t\t\t\t\t\tsrc: filePath,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t]);\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tfileSize: fileInfo.size,\n\t\t\t\t\t\twidth: imageInfo.width,\n\t\t\t\t\t\theight: imageInfo.height\n\t\t\t\t\t};\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthrow new Error('获取图片信息失败');\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 修改压缩按钮点击事件\n\t\t\tasync handleCompressClick() {\n\t\t\t\ttry {\n\t\t\t\t\t// 检查免费次数\n\t\t\t\t\tconst freeCount = getFreeCount();\n\t\t\t\t\tif (freeCount <= 0) {\n\t\t\t\t\t\t// 免费次数用完，显示广告提示\n\t\t\t\t\t\tthis.showAdPrompt();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 显示加载提示\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '检查图片中...',\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\n\t\t\t\t\t// 检查图片是否违规，使用本地存储的结果，不查询服务器\n\t\t\t\t\t// 因为轮询已经在后台持续更新结果\n\t\t\t\t\tconst checkResult = await isImageRisky(this.tempImagePath, false);\n\n\t\t\t\t\t// 隐藏加载提示\n\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\t// 检测中\n\t\t\t\t\tif (checkResult.isChecking) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: checkResult.message,\n\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 违规内容\n\t\t\t\t\tif (checkResult.isRisky) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: checkResult.message,\n\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 减少免费次数（如果不是无限使用状态）\n\t\t\t\t\tconst currentFreeCount = getFreeCount();\n\t\t\t\t\tif (currentFreeCount < 999) {\n\t\t\t\t\t\tdecreaseFreeCount();\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!this.compressedImagePath) {\n\t\t\t\t\t\tawait this.compressImage();\n\t\t\t\t\t}\n\n\t\t\t\t\t// 保存到相册\n\t\t\t\t\tawait new Promise((resolve, reject) => {\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: this.compressedImagePath,\n\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\t// 直接显示分享弹窗\n\t\t\t\t\tthis.showSharePopup = true;\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 如果失败可能是权限问题，提示用户授权\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '需要保存相册权限，是否去设置？',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleInputFocus(event) {\n\t\t\t\t// 使用延时确保在输入框获得焦点后执行选中\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tconst value = event.target.value;\n\t\t\t\t\tif (value) {\n\t\t\t\t\t\tthis.selectionStart = 0;\n\t\t\t\t\t\tthis.selectionEnd = value.toString().length;\n\t\t\t\t\t}\n\t\t\t\t}, 100);\n\t\t\t},\n\t\t\t// 隐藏分享弹窗\n\t\t\thideSharePopup() {\n\t\t\t\tthis.showSharePopup = false;\n\t\t\t},\n\t\t\t// 分享给好友\n\t\t\tonShareAppMessage() {\n\t\t\t\t// 判断是否有全局分享信息\n\t\t\t\tif (getApp().globalData && getApp().globalData.shareInfo) {\n\t\t\t\t\treturn getApp().globalData.shareInfo;\n\t\t\t\t}\n\t\t\t\t// 默认分享信息\n\t\t\t\treturn {\n\t\t\t\t\ttitle: '压缩图片助手小花版 - 一键压缩图片,节省存储空间',\n\t\t\t\t\tpath: '/pages/index/index',\n\t\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 分享到朋友圈\n\t\t\tonShareTimeline() {\n\t\t\t\t// 判断是否有全局分享信息\n\t\t\t\tif (getApp().globalData && getApp().globalData.shareInfo) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttitle: getApp().globalData.shareInfo.title,\n\t\t\t\t\t\tpath: getApp().globalData.shareInfo.path,\n\t\t\t\t\t\timageUrl: getApp().globalData.shareInfo.imageUrl\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t// 默认分享信息\n\t\t\t\treturn {\n\t\t\t\t\ttitle: '压缩图片助手小花版 - 一键压缩图片,节省存储空间',\n\t\t\t\t\tpath: '/pages/index/index',\n\t\t\t\t\timageUrl: '/static/share-icon.png'\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n// 使用uni.scss中定义的主题色变量\n$primary-color: $uni-color-primary; // 主题色：微信绿\n$primary-gradient: $theme-color-primary-gradient;\n$bg-color: $uni-bg-color-grey; // 背景色：微信灰\n$text-primary: $uni-text-color; // 主要文字颜色\n$text-secondary: $theme-text-secondary; // 次要文字颜色\n$text-tertiary: $uni-text-color-grey; // 辅助文字颜色\n$link-color: $theme-color-link; // 链接/高亮文字颜色\n$border-color: $uni-border-color; // 边框颜色\n$shadow-dark: $theme-shadow-dark;\n$shadow-light: $theme-shadow-light;\n\npage {\n\tbackground-color: $bg-color;\n}\n\n// 新拟物风格的混入\n@mixin neumorphism {\n\tbackground: $bg-color;\n\tbox-shadow: 12px 12px 24px $shadow-dark,\n\t\t\t\t-8px -8px 20px $shadow-light,\n\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.5),\n\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\n\tborder: 1px solid rgba(255, 255, 255, 0.8);\n}\n\n@mixin neumorphism-inset {\n\tbackground: $bg-color;\n\tbox-shadow: inset 6px 6px 12px $shadow-dark,\n\t\t\t\tinset -6px -6px 12px $shadow-light;\n}\n\n// 磨砂玻璃风格的混入\n@mixin glassmorphism {\n\tbackground: rgba($bg-color, 0.98);\n\tbackdrop-filter: blur(10px);\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.neumorphism {\n\t@include neumorphism;\n}\n\n.neumorphism-inset {\n\t@include neumorphism-inset;\n}\n\n.glassmorphism {\n\t@include glassmorphism;\n}\n\n.image-compress-container {\n\tpadding: 30rpx;\n\n\t.custom-nav {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 100;\n\t\tpadding: 0 30rpx;\n\n\t\t.nav-content {\n\t\t\theight: 44px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.profile-button-container {\n\t\t\t\tmin-width: 36px;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tmargin-left: 0;\n\n\t\t\t\t.profile-btn {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\twidth: 32px;\n\t\t\t\t\theight: 32px;\n\t\t\t\t\tborder-radius: 16px;\n\t\t\t\t\tbackground-color: rgba(255, 255, 255, 0.9);\n\t\t\t\t\tbox-shadow: 0 1px 2px $theme-shadow-dark;\n\t\t\t\t\tborder: none;\n\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t}\n\n\t\t\t\t\t.profile-text {\n\t\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\t\tcolor: $uni-text-color;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.nav-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: $text-primary;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t.share-button-container {\n\t\t\t\tmin-width: 36px;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-end;\n\t\t\t\tmargin-right: 180rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.main-content {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\tpadding-bottom: calc(140rpx + env(safe-area-inset-bottom));\n\n\t\t.upload-section {\n\t\t\tmargin: 20rpx 0 40rpx;\n\n\t\t\t.upload-box {\n\t\t\t\theight: 400rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tbackground: linear-gradient(145deg, #ffffff, #f0f0f0);\n\t\t\t\tbox-shadow: 20px 20px 40px rgba(0, 0, 0, 0.15),\n\t\t\t\t\t\t\t-12px -12px 24px rgba(255, 255, 255, 0.95),\n\t\t\t\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.9),\n\t\t\t\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\n\t\t\t\tborder: 1px solid rgba(255, 255, 255, 0.8);\n\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\tbackground: linear-gradient(145deg, #f0f0f0, #ffffff);\n\t\t\t\t\tbox-shadow: inset 10px 10px 20px rgba(0, 0, 0, 0.1),\n\t\t\t\t\t\t\t\tinset -10px -10px 20px rgba(255, 255, 255, 0.95);\n\t\t\t\t}\n\n\t\t\t\t.upload-icon {\n\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\theight: 120rpx;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\topacity: 0.8;\n\t\t\t\t\tfilter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.1));\n\t\t\t\t}\n\n\t\t\t\t.upload-text {\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\ttext-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);\n\t\t\t\t}\n\n\t\t\t\t.upload-desc {\n\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\ttext-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.preview-image {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 400rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tbox-shadow: 20px 20px 40px rgba(0, 0, 0, 0.15),\n\t\t\t\t\t\t\t-12px -12px 24px rgba(255, 255, 255, 0.95),\n\t\t\t\t\t\t\tinset 2px 2px 4px rgba(255, 255, 255, 0.9),\n\t\t\t\t\t\t\tinset -2px -2px 4px rgba(0, 0, 0, 0.05);\n\t\t\t\tborder: 1px solid rgba(255, 255, 255, 0.8);\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\tbox-shadow: inset 10px 10px 20px rgba(0, 0, 0, 0.1),\n\t\t\t\t\t\t\t\tinset -10px -10px 20px rgba(255, 255, 255, 0.95);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.image-info {\n\t\t\tmargin: 20rpx 0;\n\t\t\tpadding: 20rpx;\n\t\t\tborder-radius: 20rpx;\n\n\t\t\t.info-row {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: $text-secondary;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\ttext {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tpadding: 0 10rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.compress-settings {\n\t\t\tpadding: 40rpx;\n\t\t\tborder-radius: 30rpx;\n\n\t\t\t.setting-item {\n\t\t\t\tmargin-bottom: 40rpx;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\t.setting-header {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t\t.setting-label {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\n\t\t\t\t\t.size-controls {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tgap: 10rpx;\n\n\t\t\t\t\t\t.size-display {\n\t\t\t\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\tmin-width: 120rpx;\n\n\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.setting-value {\n\t\t\t\t\t\t\tmin-width: 60rpx;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.slider-container {\n\t\t\t\t\tpadding: 30rpx 20rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\n\t\t\t\t\t::v-deep .uni-slider {\n\t\t\t\t\t\tmargin: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t::v-deep .uni-slider-handle {\n\t\t\t\t\t\twidth: 56rpx;\n\t\t\t\t\t\theight: 56rpx;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\t\t\t\t}\n\n\t\t\t\t\t::v-deep .uni-slider-track {\n\t\t\t\t\t\theight: 4rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.bottom-buttons {\n\t\t\tposition: fixed;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 20rpx 40rpx calc(20rpx + env(safe-area-inset-bottom));\n\n\t\t\t.btn {\n\t\t\t\twidth: 45%;\n\t\t\t\theight: 88rpx;\n\t\t\t\tline-height: 88rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder-radius: 44rpx;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tborder: none;\n\n\t\t\t\t&.select-btn {\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\tborder: 2rpx solid rgba(7, 193, 96, 0.3);\n\t\t\t\t\tbackground: $bg-color;\n\t\t\t\t\tbox-shadow: 8px 8px 16px $shadow-dark,\n\t\t\t\t\t\t\t\t-6px -6px 12px $shadow-light,\n\t\t\t\t\t\t\t\tinset 1px 1px 2px rgba(255, 255, 255, 0.5);\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t\tbox-shadow: inset 6px 6px 12px $shadow-dark,\n\t\t\t\t\t\t\t\t\tinset -6px -6px 12px $shadow-light;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.compress-btn {\n\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tbox-shadow: 8px 8px 16px rgba(7, 193, 96, 0.2),\n\t\t\t\t\t\t\t\t-4px -4px 12px rgba(255, 255, 255, 0.8),\n\t\t\t\t\t\t\t\tinset 1px 1px 2px rgba(255, 255, 255, 0.3);\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\t\tbox-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&[disabled] {\n\t\t\t\t\topacity: 0.5;\n\t\t\t\t\tbackground: #E5E5E5;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.size-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 999;\n\n\t.popup-mask {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.4);\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.popup-content {\n\t\tposition: absolute;\n\t\tleft: 50%;\n\t\ttop: 50%;\n\t\ttransform: translate(-50%, -50%);\n\t\twidth: 80%;\n\t\tmax-width: 600rpx;\n\t\tbackground: $bg-color;\n\t\tborder-radius: 30rpx;\n\t\toverflow: hidden;\n\n\t\t.popup-header {\n\t\t\tpadding: 20rpx;\n\t\t\ttext-align: center;\n\t\t\tposition: relative;\n\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\n\t\t\t.popup-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: $text-primary;\n\t\t\t}\n\n\t\t\t.popup-close {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 20rpx;\n\t\t\t\ttop: 50%;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t\tfont-size: 40rpx;\n\t\t\t\tcolor: $text-secondary;\n\t\t\t\tpadding: 10rpx;\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t&:active {\n\t\t\t\t\topacity: 0.7;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.popup-body {\n\t\t\tpadding: 30rpx;\n\n\t\t\t.input-group {\n\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\ttext {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t}\n\n\t\t\t\tinput {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tbackground: #fff;\n\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t\tpadding: 0 24rpx;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tbox-shadow: inset 2rpx 2rpx 5rpx rgba(0, 0, 0, 0.05);\n\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tbox-shadow: inset 2rpx 2rpx 5rpx rgba(7, 193, 96, 0.1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.popup-buttons {\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 20rpx;\n\t\t\t\tmargin-top: 30rpx;\n\n\t\t\t\tbutton {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tborder: none;\n\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t&.cancel-btn {\n\t\t\t\t\t\tbackground: rgba(0, 0, 0, 0.03);\n\t\t\t\t\t\tcolor: $text-secondary;\n\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.confirm-btn {\n\t\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\t\tcolor: #fff;\n\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.share-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 999;\n\n\t.popup-mask {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.4);\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.popup-content {\n\t\tposition: absolute;\n\t\tleft: 50%;\n\t\ttop: 50%;\n\t\ttransform: translate(-50%, -50%);\n\t\twidth: 80%;\n\t\tmax-width: 600rpx;\n\t\tbackground: $bg-color;\n\t\tborder-radius: 30rpx;\n\t\toverflow: hidden;\n\n\t\t.popup-header {\n\t\t\tpadding: 20rpx;\n\t\t\ttext-align: center;\n\t\t\tposition: relative;\n\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\n\n\t\t\t.popup-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: $text-primary;\n\t\t\t}\n\n\t\t\t.popup-close {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 20rpx;\n\t\t\t\ttop: 50%;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t\tfont-size: 40rpx;\n\t\t\t\tcolor: $text-secondary;\n\t\t\t\tpadding: 10rpx;\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t&:active {\n\t\t\t\t\topacity: 0.7;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.popup-body {\n\t\t\tpadding: 30rpx;\n\n\t\t\t.share-content {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-bottom: 30rpx;\n\n\t\t\t\t.share-icon {\n\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\theight: 120rpx;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.share-title {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t}\n\n\t\t\t\t.share-desc {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.share-buttons {\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 20rpx;\n\n\t\t\t\t.share-btn {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tbackground: $primary-gradient;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\tborder: none;\n\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t}\n\n\t\t\t\t\t.iconfont {\n\t\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.cancel-btn {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 40rpx;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.03);\n\t\t\t\t\tcolor: $text-secondary;\n\t\t\t\t\tborder: none;\n\t\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752290702475\n      var cssReload = require(\"D:/1_program/12_Uniapp/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}