const axios = require('axios');

// 测试分享额度功能
async function testShareQuota() {
  const baseURL = 'http://localhost:8850';
  
  console.log('🧪 开始测试分享额度功能...\n');
  
  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ 健康检查成功:', healthResponse.data.status);
    
    // 2. 查看数据库中的用户数据
    console.log('\n2. 查看数据库中的用户数据...');
    try {
      // 这里需要你提供一个真实的openid，或者我们查看所有用户
      const testOpenid = 'test_openid_001'; // 请替换为你的真实openid
      
      // 先尝试登录获取用户信息
      const loginData = {
        code: 'test_code_123',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      };
      
      const loginResponse = await axios.post(`${baseURL}/api/user/login`, loginData);
      console.log('✅ 用户登录/注册:', JSON.stringify(loginResponse.data, null, 2));
      
      if (loginResponse.data.success) {
        const userId = loginResponse.data.data.userInfo.userId;
        
        // 3. 获取用户额度信息
        console.log('\n3. 获取用户额度信息...');
        const quotaResponse = await axios.get(`${baseURL}/api/user/quota/${userId}`);
        console.log('✅ 用户额度:', JSON.stringify(quotaResponse.data, null, 2));
        
        // 4. 测试分享功能（模拟前端分享）
        console.log('\n4. 测试分享功能...');
        const shareData = {
          userId: userId,
          openid: loginResponse.data.data.userInfo.openid,
          danmuText: '测试分享弹幕文本',
          styleType: 'blue-yellow',
          speedClass: 'speed-normal',
          sizeClass: 'size-normal',
          fontClass: 'font-default',
          runningMode: 'normal'
        };
        
        // 连续测试3次分享
        for (let i = 1; i <= 4; i++) {
          console.log(`\n--- 第${i}次分享测试 ---`);
          try {
            const shareResponse = await axios.post(`${baseURL}/api/danmu/save`, shareData);
            console.log(`✅ 第${i}次分享成功:`, JSON.stringify(shareResponse.data, null, 2));
            
            // 分享成功后再次查看额度
            const updatedQuotaResponse = await axios.get(`${baseURL}/api/user/quota/${userId}`);
            console.log(`📊 分享后额度:`, updatedQuotaResponse.data.data);
            
          } catch (error) {
            console.log(`❌ 第${i}次分享失败:`, error.response?.data || error.message);
            
            // 如果是额度不足错误，这是预期的
            if (error.response?.data?.code === 'INSUFFICIENT_SHARE_COUNT') {
              console.log('🎯 成功触发分享次数限制！');
            }
          }
        }
        
        // 5. 最终查看用户状态
        console.log('\n5. 最终用户状态...');
        const finalQuotaResponse = await axios.get(`${baseURL}/api/user/quota/${userId}`);
        console.log('📊 最终用户额度:', JSON.stringify(finalQuotaResponse.data, null, 2));
      }
      
    } catch (error) {
      console.log('⚠️  用户操作失败:', error.response?.data || error.message);
    }
    
    console.log('\n🎉 分享额度测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testShareQuota();
}

module.exports = testShareQuota;
