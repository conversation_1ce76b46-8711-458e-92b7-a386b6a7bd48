-- 图像压缩小程序数据库初始化脚本
-- 适用于 MySQL 8.0.36+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS image_compression_service
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE image_compression_service;

-- 1. 创建商户配置表
CREATE TABLE IF NOT EXISTS merchants (
    merchant_id VARCHAR(32) PRIMARY KEY COMMENT '商户ID',
    app_id VARCHAR(64) UNIQUE NOT NULL COMMENT '小程序AppID',
    business_name VARCHAR(100) NOT NULL COMMENT '业务名称',
    wechat_app_id VARCHAR(64) COMMENT '微信AppID',
    wechat_app_secret VARCHAR(128) COMMENT '微信AppSecret',
    wechat_mch_id VARCHAR(32) COMMENT '微信商户号',
    wechat_api_key VARCHAR(128) COMMENT '微信支付密钥',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_app_id (app_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='商户配置表';

-- 2. 创建用户表
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信openid',
    app_id VARCHAR(64) NOT NULL COMMENT '小程序AppID',
    nickname VARCHAR(100) COMMENT '用户昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    daily_free_count INT DEFAULT 1 COMMENT '每日免费次数',
    last_daily_reset DATE DEFAULT (CURDATE()) COMMENT '上次每日重置日期',
    total_usage_count INT DEFAULT 0 COMMENT '总使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    UNIQUE KEY unique_user (openid, app_id),
    INDEX idx_openid (openid),
    INDEX idx_app_id (app_id),
    INDEX idx_last_daily_reset (last_daily_reset),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户信息表';

-- 3. 创建商品配置表
CREATE TABLE IF NOT EXISTS products (
    product_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    merchant_id VARCHAR(32) NOT NULL COMMENT '商户ID',
    product_code ENUM('day_card', 'permanent') NOT NULL COMMENT '商品代码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '价格（元）',
    duration_hours INT COMMENT '有效期小时数，永久为NULL',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_product_code (product_code),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='商品配置表';

-- 4. 创建会员表
CREATE TABLE IF NOT EXISTS memberships (
    membership_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会员记录ID',
    user_id INT NOT NULL COMMENT '用户ID',
    membership_type ENUM('day_card', 'permanent') NOT NULL COMMENT '会员类型',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    expire_time TIMESTAMP NULL COMMENT '过期时间，永久为NULL',
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active' COMMENT '状态',
    order_id VARCHAR(32) COMMENT '关联订单ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_expire_time (expire_time),
    INDEX idx_membership_type (membership_type),
    INDEX idx_order_id (order_id)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户会员表';

-- 5. 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    order_id VARCHAR(32) PRIMARY KEY COMMENT '订单ID',
    user_id INT NOT NULL COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信openid',
    app_id VARCHAR(64) NOT NULL COMMENT '小程序AppID',
    merchant_id VARCHAR(32) NOT NULL COMMENT '商户ID',
    product_code ENUM('day_card', 'permanent') NOT NULL COMMENT '商品代码',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额（元）',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending' COMMENT '支付状态',
    payment_method VARCHAR(50) COMMENT '支付方式',
    transaction_id VARCHAR(100) COMMENT '微信支付交易号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
    paid_at TIMESTAMP NULL COMMENT '支付完成时间',
    expire_at TIMESTAMP NULL COMMENT '订单过期时间',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_app_id (app_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at),
    INDEX idx_paid_at (paid_at)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='订单表';

-- 插入默认商户配置（示例）
INSERT INTO merchants (merchant_id, app_id, business_name, is_active) VALUES
('default_merchant', 'wx_default_app_id', '默认商户', TRUE);

-- 插入默认商品配置
INSERT INTO products (merchant_id, product_code, product_name, description, price, duration_hours, sort_order) VALUES
('default_merchant', 'day_card', '日卡会员', '24小时内无限制压缩图片', 4.99, 24, 1),
('default_merchant', 'permanent', '永久会员', '永久无限制压缩图片', 19.99, NULL, 2);

-- 创建每日重置免费次数的存储过程
DELIMITER //
CREATE PROCEDURE ResetDailyFreeCount()
BEGIN
    UPDATE users
    SET
        daily_free_count = 1,
        last_daily_reset = CURDATE()
    WHERE last_daily_reset < CURDATE();

    SELECT ROW_COUNT() as reset_count;
END //
DELIMITER ;

-- 创建检查和更新会员状态的存储过程
DELIMITER //
CREATE PROCEDURE UpdateMembershipStatus()
BEGIN
    -- 更新过期的会员记录
    UPDATE memberships
    SET status = 'expired'
    WHERE status = 'active'
    AND expire_time IS NOT NULL
    AND expire_time < NOW();

    SELECT ROW_COUNT() as expired_memberships;
END //
DELIMITER ;

-- 查看创建的表
SHOW TABLES;

-- 查看表结构
DESCRIBE merchants;
DESCRIBE users;
DESCRIBE products;
DESCRIBE memberships;
DESCRIBE orders;
