const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const AppManager = require('../services/MerchantManager');

/**
 * 获取商品列表（根据AppID）
 */
router.get('/products', async (req, res) => {
  try {
    const { appId } = req.query;

    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'appId不能为空'
      });
    }

    // 使用小程序管理器获取商品列表
    const products = AppManager.getProductsByAppId(appId);

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        error: '未找到对应的小程序配置或商品'
      });
    }

    // 获取小程序信息
    const appConfig = AppManager.getAppByAppId(appId);
    const merchantId = appConfig ? appConfig.merchant?.merchantId : 'unknown';

    res.json({
      success: true,
      data: {
        appId,
        appName: appConfig ? appConfig.appName : 'Unknown',
        businessName: appConfig ? appConfig.businessName : 'Unknown',
        merchantId,
        products: products
      }
    });

  } catch (error) {
    console.error('获取商品列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户会员状态
 */
router.get('/status', async (req, res) => {
  try {
    const { openid, appId } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户ID
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userId = user[0].user_id;

    // 获取当前有效的会员信息
    const membership = await query(
      `SELECT * FROM memberships 
       WHERE user_id = ? AND status = 'active' 
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userId]
    );

    if (membership.length === 0) {
      return res.json({
        success: true,
        data: {
          isActive: false,
          type: null,
          startTime: null,
          expireTime: null,
          remainingTime: null
        }
      });
    }

    const memberData = membership[0];
    let remainingTime = null;

    // 计算剩余时间（仅对日卡会员）
    if (memberData.membership_type === 'day_card' && memberData.expire_time) {
      const now = new Date();
      const expireTime = new Date(memberData.expire_time);
      remainingTime = Math.max(0, Math.floor((expireTime - now) / 1000)); // 剩余秒数
    }

    res.json({
      success: true,
      data: {
        isActive: true,
        type: memberData.membership_type,
        startTime: memberData.start_time,
        expireTime: memberData.expire_time,
        remainingTime,
        isPermanent: memberData.membership_type === 'permanent'
      }
    });

  } catch (error) {
    console.error('获取会员状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户会员历史
 */
router.get('/history', async (req, res) => {
  try {
    const { openid, appId, page = 1, limit = 10 } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户ID
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userId = user[0].user_id;
    const offset = (page - 1) * limit;

    // 获取会员历史记录
    const memberships = await query(
      `SELECT m.*, o.amount, o.transaction_id 
       FROM memberships m
       LEFT JOIN orders o ON m.order_id = o.order_id
       WHERE m.user_id = ? 
       ORDER BY m.created_at DESC 
       LIMIT ? OFFSET ?`,
      [userId, parseInt(limit), offset]
    );

    // 获取总数
    const totalResult = await query(
      'SELECT COUNT(*) as total FROM memberships WHERE user_id = ?',
      [userId]
    );

    res.json({
      success: true,
      data: {
        memberships: memberships.map(m => ({
          membershipId: m.membership_id,
          type: m.membership_type,
          startTime: m.start_time,
          expireTime: m.expire_time,
          status: m.status,
          orderId: m.order_id,
          amount: m.amount ? parseFloat(m.amount) : null,
          transactionId: m.transaction_id,
          createdAt: m.created_at
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          totalPages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取会员历史失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 检查会员权限（用于替代广告逻辑）
 */
router.post('/check-permission', async (req, res) => {
  try {
    const { openid, appId } = req.body;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户信息
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userData = user[0];

    // 检查会员状态
    const membership = await query(
      `SELECT * FROM memberships
       WHERE user_id = ? AND status = 'active'
       AND (expire_time IS NULL OR expire_time > NOW())
       LIMIT 1`,
      [userData.user_id]
    );

    const hasActiveMembership = membership.length > 0;

    res.json({
      success: true,
      data: {
        hasActiveMembership,
        membershipType: hasActiveMembership ? membership[0].membership_type : null,
        expireTime: hasActiveMembership ? membership[0].expire_time : null,
        // 如果有会员，可以替代广告；如果没有会员，显示购买会员选项
        canReplaceAd: hasActiveMembership,
        shouldShowMembershipOffer: !hasActiveMembership
      }
    });

  } catch (error) {
    console.error('检查会员权限失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
