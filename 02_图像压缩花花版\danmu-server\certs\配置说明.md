# 微信支付 API v3 配置说明

## ✅ **正确的配置参数**

根据微信支付官方文档，API v3 只需要以下参数：

### 必需参数
1. **mchid** - 商户号
2. **appid** - 小程序AppID
3. **api_v3_key** - API v3密钥（32位字符）
4. **serial_no** - 商户API证书序列号（或微信支付公钥ID）
5. **商户API证书文件** - 用于请求签名

### 文件要求
- `apiclient_cert.pem` - 商户API证书
- `apiclient_key.pem` - 商户API私钥
- `wechatpay_public_key.pem` - 微信支付公钥（仅公钥验签需要）

## ❌ **不需要的参数**

- **APIv2密钥** - API v3完全不需要
- **其他v2相关参数**

## 🔍 **验证商户身份的方式**

### API v3 使用商户API证书验证身份
- 使用商户API证书私钥对请求进行签名
- 微信支付使用商户API证书公钥验证签名
- **不涉及APIv2密钥**

### 回调验签方式
1. **平台证书验签**：使用微信支付平台证书公钥
2. **微信支付公钥验签**：使用微信支付公钥

## 📋 **三个商户的配置**

### guofuli1700692997 (公钥验签)
```json
{
  "mchid": "商户号",
  "appid": "wxbd7109f36cd77b2b",
  "api_v3_key": "32位API_v3密钥",
  "serial_no": "PUB_KEY_ID_...",
  "verifyMethod": "public_key"
}
```

### haocaihua1717453427 (平台证书验签)
```json
{
  "mchid": "商户号",
  "appid": "wxbd7109f36cd77b2b", 
  "api_v3_key": "32位API_v3密钥",
  "serial_no": "证书序列号",
  "verifyMethod": "platform_cert"
}
```

### lixiang1717867742 (平台证书验签)
```json
{
  "mchid": "商户号",
  "appid": "wxbd7109f36cd77b2b",
  "api_v3_key": "32位API_v3密钥",
  "serial_no": "证书序列号",
  "verifyMethod": "platform_cert"
}
```

## 🎯 **关键要点**

1. **API v3 与 API v2 完全隔离**
2. **只需要 API v3 密钥，不需要 API v2 密钥**
3. **身份验证使用商户API证书，不是密钥**
4. **回调验签可选择平台证书或微信支付公钥**

## 📞 **参考文档**

- [微信支付 API v3 概述](https://pay.weixin.qq.com/doc/v3/merchant/4012081606)
- [什么是APIv3密钥？](https://pay.weixin.qq.com/doc/v3/merchant/4013053267)
- [商户API证书说明](https://pay.weixin.qq.com/doc/v3/merchant/4013053053)
