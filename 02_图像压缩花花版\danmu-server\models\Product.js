const { pool } = require('../config/database');

class Product {
  /**
   * 获取所有活跃商品
   * @returns {Array} 商品列表
   */
  static async getActiveProducts() {
    const [rows] = await pool.execute(
      `SELECT 
        product_id, product_code, product_name, description, 
        price, product_type, duration_days, sort_order
       FROM products 
       WHERE is_active = TRUE 
       ORDER BY sort_order ASC, product_id ASC`
    );

    return rows;
  }

  /**
   * 根据商品代码获取商品
   * @param {string} productCode 商品代码
   * @returns {object|null} 商品信息
   */
  static async getByCode(productCode) {
    const [rows] = await pool.execute(
      `SELECT 
        product_id, product_code, product_name, description, 
        price, product_type, duration_days, is_active
       FROM products 
       WHERE product_code = ? AND is_active = TRUE`,
      [productCode]
    );

    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 根据商品ID获取商品
   * @param {number} productId 商品ID
   * @returns {object|null} 商品信息
   */
  static async getById(productId) {
    const [rows] = await pool.execute(
      `SELECT 
        product_id, product_code, product_name, description, 
        price, product_type, duration_days, is_active
       FROM products 
       WHERE product_id = ?`,
      [productId]
    );

    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 根据商品类型获取商品
   * @param {string} productType 商品类型
   * @returns {object|null} 商品信息
   */
  static async getByType(productType) {
    const [rows] = await pool.execute(
      `SELECT 
        product_id, product_code, product_name, description, 
        price, product_type, duration_days, is_active
       FROM products 
       WHERE product_type = ? AND is_active = TRUE`,
      [productType]
    );

    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 更新商品价格
   * @param {number} productId 商品ID
   * @param {number} newPrice 新价格
   * @returns {boolean} 是否成功
   */
  static async updatePrice(productId, newPrice) {
    const [result] = await pool.execute(
      'UPDATE products SET price = ?, updated_at = NOW() WHERE product_id = ?',
      [newPrice, productId]
    );

    return result.affectedRows > 0;
  }

  /**
   * 获取商品统计信息
   * @returns {object} 统计信息
   */
  static async getStats() {
    const connection = await pool.getConnection();

    try {
      const [totalProducts] = await connection.execute(
        'SELECT COUNT(*) as count FROM products'
      );

      const [activeProducts] = await connection.execute(
        'SELECT COUNT(*) as count FROM products WHERE is_active = TRUE'
      );

      const [productTypes] = await connection.execute(
        `SELECT 
          product_type, 
          COUNT(*) as count,
          AVG(price) as avg_price
         FROM products 
         WHERE is_active = TRUE
         GROUP BY product_type`
      );

      return {
        total: totalProducts[0].count,
        active: activeProducts[0].count,
        types: productTypes
      };
    } finally {
      connection.release();
    }
  }
}

module.exports = Product;
