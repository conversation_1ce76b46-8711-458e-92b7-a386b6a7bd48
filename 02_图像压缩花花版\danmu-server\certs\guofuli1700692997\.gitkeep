# 商户 guofuli1700692997 证书文件放置说明

请将以下文件放置在此目录：

1. apiclient_cert.pem - 微信支付API证书（公钥）
2. apiclient_key.pem - 微信支付API私钥
3. wechatpay_public_key.pem - 微信支付公钥（用于验签）
4. apiclient_cert.p12 - PKCS12格式证书（可选）

配置步骤：
1. 编辑 config.json 文件，填写正确的商户信息和微信支付公钥ID
2. 从微信商户平台下载微信支付公钥，重命名为 wechatpay_public_key.pem
3. 将商户API证书文件放置到此目录
4. 设置正确的文件权限：chmod 600 *.pem *.p12
5. 调用 API 验证配置：GET /api/certs/validate/guofuli1700692997

⚠️ 重要：此商户使用微信支付公钥验签方式
- serialNo 字段应填写公钥ID（格式：PUB_KEY_ID_...）
- verifyMethod 已设置为 "public_key"

注意：
- 证书文件不会被提交到Git仓库
- 请确保文件权限设置为 600
- 定期检查证书有效期
