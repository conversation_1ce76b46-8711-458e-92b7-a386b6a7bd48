/**
 * 微信支付API测试脚本
 * 用于验证微信支付配置和API调用是否正常
 */

const WechatPayService = require('./services/wechatPayService');

async function testWechatPayAPI() {
  try {
    console.log('🧪 开始测试微信支付API配置...\n');

    // 1. 测试服务初始化
    console.log('1️⃣ 测试服务初始化...');
    const payService = new WechatPayService('guofuli1700692997');
    console.log('✅ 微信支付服务初始化成功');
    console.log('📝 商户配置:', {
      mchid: payService.config.mchid,
      appid: payService.config.appid,
      notifyUrl: payService.config.notifyUrl,
      businessName: payService.config.businessName
    });
    console.log('');

    // 2. 测试创建订单
    console.log('2️⃣ 测试创建支付订单...');
    const testOrderInfo = {
      orderId: `test_order_${Date.now()}`,
      amount: 0.01, // 测试金额：1分钱
      description: '测试订单-弹幕小程序',
      openid: 'test_openid_123456' // 测试用的openid
    };

    console.log('📝 测试订单信息:', testOrderInfo);

    try {
      const orderResult = await payService.createOrder(testOrderInfo);
      console.log('✅ 创建订单成功:', {
        prepay_id: orderResult.prepay_id,
        out_trade_no: orderResult.out_trade_no
      });

      // 3. 测试生成支付参数
      console.log('\n3️⃣ 测试生成小程序支付参数...');
      const payParams = payService.generateMiniProgramPayParams(orderResult.prepay_id);
      console.log('✅ 支付参数生成成功:', {
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign ? '已生成' : '未生成'
      });

      // 4. 测试查询订单
      console.log('\n4️⃣ 测试查询订单状态...');
      try {
        const queryResult = await payService.queryOrder(testOrderInfo.orderId);
        console.log('✅ 查询订单成功:', {
          trade_state: queryResult.trade_state,
          out_trade_no: queryResult.out_trade_no
        });
      } catch (queryError) {
        console.log('⚠️ 查询订单失败（预期行为，因为是测试订单）:', queryError.message);
      }

      // 5. 测试关闭订单
      console.log('\n5️⃣ 测试关闭订单...');
      try {
        const closeResult = await payService.closeOrder(testOrderInfo.orderId);
        console.log('✅ 关闭订单成功:', closeResult);
      } catch (closeError) {
        console.log('⚠️ 关闭订单失败（可能订单不存在）:', closeError.message);
      }

    } catch (createError) {
      console.error('❌ 创建订单失败:', createError.message);
      
      // 分析错误原因
      if (createError.message.includes('INVALID_REQUEST')) {
        console.log('\n🔍 错误分析: 请求参数错误');
        console.log('   - 检查商户号、AppID是否正确');
        console.log('   - 检查API密钥是否正确');
        console.log('   - 检查openid格式是否正确');
      } else if (createError.message.includes('NOAUTH')) {
        console.log('\n🔍 错误分析: 权限不足');
        console.log('   - 检查商户号是否有效');
        console.log('   - 检查API证书是否正确');
        console.log('   - 检查商户是否开通JSAPI支付');
      } else if (createError.message.includes('商户私钥文件不存在')) {
        console.log('\n🔍 错误分析: 私钥文件缺失');
        console.log('   - 请确保apiclient_key.pem文件存在');
        console.log('   - 文件路径:', `certs/guofuli1700692997/apiclient_key.pem`);
      } else if (createError.message.includes('网络请求失败')) {
        console.log('\n🔍 错误分析: 网络连接问题');
        console.log('   - 检查网络连接');
        console.log('   - 检查防火墙设置');
      }
    }

    console.log('\n🎉 微信支付API测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testWechatPayAPI();
}

module.exports = { testWechatPayAPI };
