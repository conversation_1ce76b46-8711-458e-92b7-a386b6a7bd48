const express = require('express');
const ContentSecurityService = require('../services/contentSecurityService');
const WechatService = require('../services/wechatService');
const {
  formatError,
  formatSuccess
} = require('../utils/helpers');

const router = express.Router();

/**
 * 文本内容安全检测
 * POST /api/security/check
 */
router.post('/check', async (req, res) => {
  try {
    const { content, openid, appId } = req.body;

    // 参数验证
    if (!content || typeof content !== 'string') {
      return res.status(400).json(formatError(
        '缺少文本内容参数',
        'MISSING_CONTENT'
      ));
    }

    if (!openid || typeof openid !== 'string') {
      return res.status(400).json(formatError(
        '缺少用户openid参数',
        'MISSING_OPENID'
      ));
    }

    // 文本长度检查
    if (content.length > 2500) {
      return res.status(400).json(formatError(
        '文本内容过长，最多支持2500字符',
        'CONTENT_TOO_LONG'
      ));
    }

    if (content.trim().length === 0) {
      return res.status(400).json(formatError(
        '文本内容不能为空',
        'CONTENT_EMPTY'
      ));
    }

    console.log('🔍 收到文本安全检测请求:', {
      contentLength: content.length,
      openid: openid.substring(0, 8) + '***',
      appId: appId || 'default'
    });

    // 获取access_token
    const accessToken = await WechatService.getAccessToken(appId);
    
    // 执行安全检测
    const securityService = new ContentSecurityService();
    const result = await securityService.checkText(content, openid, accessToken);

    console.log('✅ 文本安全检测完成:', {
      safe: result.safe,
      suggest: result.suggest,
      hasError: !!result.error
    });

    res.json(formatSuccess({
      safe: result.safe,
      suggest: result.suggest,
      label: result.label,
      trace_id: result.trace_id
    }));

  } catch (error) {
    console.error('❌ 文本安全检测失败:', error);
    
    // 根据错误类型返回不同的响应
    if (error.message.includes('微信API错误')) {
      return res.status(500).json(formatError(
        '微信安全检测服务异常，请稍后重试',
        'WECHAT_API_ERROR'
      ));
    }
    
    if (error.message.includes('网络')) {
      return res.status(500).json(formatError(
        '网络连接异常，请检查网络后重试',
        'NETWORK_ERROR'
      ));
    }

    // 其他错误默认允许通过，避免影响用户体验
    res.json(formatSuccess({
      safe: true,
      error: '检测服务异常，已允许通过'
    }));
  }
});

module.exports = router;
