@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义主题色变量 - 微信风格 */
/* 辅助色 */
/* 背景和文字颜色 */
page {
  background-color: #F7F7F7;
}
.neumorphism {
  background: #F7F7F7;
  box-shadow: 12px 12px 24px rgba(0, 0, 0, 0.1), -8px -8px 20px rgba(255, 255, 255, 0.9), inset 2px 2px 4px rgba(255, 255, 255, 0.5), inset -2px -2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
}
.glassmorphism {
  background: rgba(247, 247, 247, 0.98);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.profile-container {
  padding: 30rpx;
}
.profile-container .custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  padding: 0 30rpx;
}
.profile-container .custom-nav .nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.profile-container .custom-nav .nav-content .back-button-container {
  min-width: 44px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.profile-container .custom-nav .nav-content .back-button-container .back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 0;
  margin: 0;
  outline: none;
  transition: opacity 0.2s ease;
}
.profile-container .custom-nav .nav-content .back-button-container .back-btn::after {
  display: none !important;
}
.profile-container .custom-nav .nav-content .back-button-container .back-btn:active {
  opacity: 0.6;
  background: transparent !important;
  background-color: transparent !important;
}
.profile-container .custom-nav .nav-content .back-button-container .back-btn .back-icon {
  font-size: 36px;
  color: #000000;
  font-weight: normal;
  line-height: 1;
  margin-left: -2px;
}
.profile-container .custom-nav .nav-content .nav-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
.profile-container .custom-nav .nav-content .nav-right-buttons {
  /* 保留容器用于布局平衡 */
  min-width: 44px;
}
.profile-container .main-content {
  position: relative;
  width: 100%;
  padding-bottom: 40rpx;
  margin-top: -40rpx;
}
.profile-container .main-content .user-info-card {
  border-radius: 30rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  color: #333333;
}
.profile-container .main-content .user-info-card .user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.profile-container .main-content .user-info-card .user-header .user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 30rpx;
}
.profile-container .main-content .user-info-card .user-header .user-avatar .avatar-image {
  width: 100%;
  height: 100%;
}
.profile-container .main-content .user-info-card .user-header .user-details {
  flex: 1;
}
.profile-container .main-content .user-info-card .user-header .user-details .user-name {
  font-size: 36rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.profile-container .main-content .user-info-card .user-header .user-details .member-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  font-weight: 500;
}
.profile-container .main-content .user-info-card .user-header .member-icon .crown-image {
  width: 90rpx;
  height: 90rpx;
}
.profile-container .main-content .user-info-card .member-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 30rpx;
}
.profile-container .main-content .user-info-card .member-section .member-header {
  text-align: center;
  margin-bottom: 20rpx;
}
.profile-container .main-content .user-info-card .member-section .member-header .member-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.profile-container .main-content .user-info-card .member-section .member-header .member-subtitle {
  font-size: 28rpx;
  color: #666666;
}
.profile-container .main-content .user-info-card .member-section .divider-line {
  width: 100%;
  height: 1px;
  border-top: 1px solid #f0f0f0;
  margin: 20rpx 0;
}
.profile-container .main-content .user-info-card .member-section .privileges-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
  align-items: center;
  justify-items: center;
  padding: 0 20rpx;
}
.profile-container .main-content .user-info-card .member-section .privileges-grid .privilege-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding-left: 60rpx;
}
.profile-container .main-content .user-info-card .member-section .privileges-grid .privilege-item .check-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  background: linear-gradient(135deg, #07C160, #06AD56);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.profile-container .main-content .user-info-card .member-section .privileges-grid .privilege-item .check-icon .check-mark {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}
.profile-container .main-content .user-info-card .member-section .privileges-grid .privilege-item .privilege-text {
  font-size: 26rpx;
  text-align: left;
  line-height: 1.4;
  white-space: nowrap;
  flex: 1;
}
.profile-container .main-content .user-info-card .member-section .member-validity {
  text-align: center;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.profile-container .main-content .user-info-card .member-section .renew-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #07C160, #06AD56);
  border: none;
  border-radius: 40rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);
}
.profile-container .main-content .user-info-card .member-section .renew-btn:active {
  background: linear-gradient(135deg, #06AD56, #059C4F);
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.profile-container .main-content .menu-section {
  margin: 30rpx 0;
}
.profile-container .main-content .menu-section .menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.profile-container .main-content .menu-section .menu-item:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.profile-container .main-content .menu-section .menu-item:last-child {
  margin-bottom: 0;
}
.profile-container .main-content .menu-section .menu-item .menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}
.profile-container .main-content .menu-section .menu-item .menu-icon .icon-image {
  width: 48rpx;
  height: 48rpx;
}
.profile-container .main-content .menu-section .menu-item .menu-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}
