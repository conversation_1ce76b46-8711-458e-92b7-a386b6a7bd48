const express = require('express');
const router = express.Router();
const CertManager = require('../services/certManager');
const { formatSuccess, formatError } = require('../utils/helpers');

/**
 * 获取所有商户状态
 * GET /api/certs/status
 */
router.get('/status', async (req, res) => {
  try {
    const status = CertManager.getAllMerchantsStatus();
    
    res.json(formatSuccess(status, '获取商户状态成功'));
    
  } catch (error) {
    console.error('获取商户状态失败:', error);
    res.status(500).json(formatError(
      '获取商户状态失败',
      'GET_MERCHANTS_STATUS_FAILED',
      error.message
    ));
  }
});

/**
 * 获取活跃商户列表
 * GET /api/certs/merchants
 */
router.get('/merchants', async (req, res) => {
  try {
    const merchants = CertManager.getActiveMerchants();
    
    res.json(formatSuccess(merchants, '获取活跃商户列表成功'));
    
  } catch (error) {
    console.error('获取活跃商户列表失败:', error);
    res.status(500).json(formatError(
      '获取活跃商户列表失败',
      'GET_ACTIVE_MERCHANTS_FAILED',
      error.message
    ));
  }
});

/**
 * 验证指定商户配置
 * GET /api/certs/validate/:merchantId
 */
router.get('/validate/:merchantId', async (req, res) => {
  try {
    const { merchantId } = req.params;
    
    const validation = CertManager.validateMerchant(merchantId);
    
    res.json(formatSuccess(validation, `商户 ${merchantId} 验证完成`));
    
  } catch (error) {
    console.error('验证商户配置失败:', error);
    res.status(500).json(formatError(
      '验证商户配置失败',
      'VALIDATE_MERCHANT_FAILED',
      error.message
    ));
  }
});

/**
 * 获取商户配置信息（脱敏）
 * GET /api/certs/config/:merchantId
 */
router.get('/config/:merchantId', async (req, res) => {
  try {
    const { merchantId } = req.params;
    
    const config = CertManager.getMerchantConfig(merchantId);
    
    // 脱敏处理
    const safeConfig = {
      merchantId: config.merchantId,
      appId: config.appId,
      description: config.description,
      isActive: config.isActive,
      notifyUrl: config.notifyUrl,
      products: config.products,
      // 敏感信息脱敏
      apiKey: config.apiKey ? '***' + config.apiKey.slice(-4) : '未配置',
      apiV3Key: config.apiV3Key ? '***' + config.apiV3Key.slice(-4) : '未配置',
      serialNo: config.serialNo ? config.serialNo.slice(0, 8) + '***' : '未配置'
    };
    
    res.json(formatSuccess(safeConfig, `获取商户 ${merchantId} 配置成功`));
    
  } catch (error) {
    console.error('获取商户配置失败:', error);
    res.status(500).json(formatError(
      '获取商户配置失败',
      'GET_MERCHANT_CONFIG_FAILED',
      error.message
    ));
  }
});

/**
 * 检查商户证书文件状态
 * GET /api/certs/files/:merchantId
 */
router.get('/files/:merchantId', async (req, res) => {
  try {
    const { merchantId } = req.params;
    
    const certStatus = CertManager.checkCertFiles(merchantId);
    
    res.json(formatSuccess(certStatus, `获取商户 ${merchantId} 证书文件状态成功`));
    
  } catch (error) {
    console.error('检查证书文件状态失败:', error);
    res.status(500).json(formatError(
      '检查证书文件状态失败',
      'CHECK_CERT_FILES_FAILED',
      error.message
    ));
  }
});

/**
 * 重新加载证书配置
 * POST /api/certs/reload
 */
router.post('/reload', async (req, res) => {
  try {
    CertManager.reload();
    
    const status = CertManager.getAllMerchantsStatus();
    
    res.json(formatSuccess(status, '证书配置重新加载成功'));
    
  } catch (error) {
    console.error('重新加载证书配置失败:', error);
    res.status(500).json(formatError(
      '重新加载证书配置失败',
      'RELOAD_CERTS_FAILED',
      error.message
    ));
  }
});

/**
 * 获取证书管理帮助信息
 * GET /api/certs/help
 */
router.get('/help', async (req, res) => {
  try {
    const help = {
      description: '微信支付证书管理系统',
      supportedMerchants: ['guofuli1700692997', 'lixiang1717867742', 'haocaihua1717453427'],
      requiredFiles: [
        'apiclient_cert.pem - 微信支付API证书（公钥）',
        'apiclient_key.pem - 微信支付API私钥',
        'config.json - 商户配置文件'
      ],
      optionalFiles: [
        'apiclient_cert.p12 - PKCS12格式证书'
      ],
      configFields: {
        required: ['merchantId', 'appId', 'apiKey', 'apiV3Key', 'serialNo'],
        optional: ['description', 'isActive', 'notifyUrl', 'products']
      },
      securityNotes: [
        '证书文件不会被提交到Git仓库',
        '请确保证书文件权限设置为600',
        '定期检查证书有效期',
        '备份重要的证书文件'
      ],
      apis: {
        'GET /api/certs/status': '获取所有商户状态',
        'GET /api/certs/merchants': '获取活跃商户列表',
        'GET /api/certs/validate/:merchantId': '验证商户配置',
        'GET /api/certs/config/:merchantId': '获取商户配置（脱敏）',
        'GET /api/certs/files/:merchantId': '检查证书文件状态',
        'POST /api/certs/reload': '重新加载配置',
        'GET /api/certs/help': '获取帮助信息'
      }
    };
    
    res.json(formatSuccess(help, '获取证书管理帮助信息成功'));
    
  } catch (error) {
    console.error('获取帮助信息失败:', error);
    res.status(500).json(formatError(
      '获取帮助信息失败',
      'GET_HELP_FAILED',
      error.message
    ));
  }
});

module.exports = router;
