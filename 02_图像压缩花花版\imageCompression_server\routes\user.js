const express = require('express');
const router = express.Router();
const { query, transaction } = require('../config/database');
const moment = require('moment');

/**
 * 用户登录/注册
 */
router.post('/login', async (req, res) => {
  try {
    const { openid, appId, userInfo } = req.body;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 检查用户是否存在
    let user = await query(
      'SELECT * FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      // 创建新用户
      const insertResult = await query(
        `INSERT INTO users (openid, app_id, nickname, avatar_url, last_login_at)
         VALUES (?, ?, ?, ?, NOW())`,
        [openid, appId, userInfo?.nickname || '', userInfo?.avatar_url || '']
      );

      // 获取新创建的用户
      user = await query(
        'SELECT * FROM users WHERE user_id = ?',
        [insertResult.insertId]
      );
    } else {
      // 更新最后登录时间和用户信息
      await query(
        `UPDATE users SET
         last_login_at = NOW(),
         nickname = COALESCE(?, nickname),
         avatar_url = COALESCE(?, avatar_url)
         WHERE openid = ? AND app_id = ?`,
        [userInfo?.nickname, userInfo?.avatar_url, openid, appId]
      );

      user = await query(
        'SELECT * FROM users WHERE openid = ? AND app_id = ?',
        [openid, appId]
      );
    }

    const userData = user[0];

    // 获取用户会员状态
    const membership = await query(
      `SELECT * FROM memberships 
       WHERE user_id = ? AND status = 'active' 
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userData.user_id]
    );

    res.json({
      success: true,
      data: {
        user: {
          userId: userData.user_id,
          openid: userData.openid,
          appId: userData.app_id,
          nickname: userData.nickname,
          avatarUrl: userData.avatar_url,
          createdAt: userData.created_at
        },
        // 前端自己管理免费次数，后端不需要提供
        membership: membership.length > 0 ? {
          isActive: true,
          type: membership[0].membership_type,
          startTime: membership[0].start_time,
          expireTime: membership[0].expire_time
        } : {
          isActive: false,
          type: null,
          startTime: null,
          expireTime: null
        }
      },
      message: user.length === 1 ? '登录成功' : '注册成功'
    });

  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户状态（主要是会员状态）
 */
router.get('/status', async (req, res) => {
  try {
    const { openid, appId } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户信息
    const user = await query(
      'SELECT * FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userData = user[0];

    // 检查会员状态
    const membership = await query(
      `SELECT * FROM memberships
       WHERE user_id = ? AND status = 'active'
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userData.user_id]
    );

    res.json({
      success: true,
      data: {
        userId: userData.user_id,
        membership: membership.length > 0 ? {
          isActive: true,
          type: membership[0].membership_type,
          expireTime: membership[0].expire_time
        } : {
          isActive: false,
          type: null,
          expireTime: null
        }
      }
    });

  } catch (error) {
    console.error('获取用户状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 检查会员权限（替代广告功能）
 */
router.post('/check-membership', async (req, res) => {
  try {
    const { openid, appId } = req.body;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户信息
    const user = await query(
      'SELECT user_id FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userId = user[0].user_id;

    // 检查会员状态
    const membership = await query(
      `SELECT * FROM memberships
       WHERE user_id = ? AND status = 'active'
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userId]
    );

    const hasActiveMembership = membership.length > 0;

    res.json({
      success: true,
      data: {
        hasActiveMembership,
        membershipType: hasActiveMembership ? membership[0].membership_type : null,
        expireTime: hasActiveMembership ? membership[0].expire_time : null,
        // 前端可以根据这个状态决定是显示广告还是引导购买会员
        canReplaceAd: hasActiveMembership
      }
    });

  } catch (error) {
    console.error('检查会员权限失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户统计信息
 */
router.get('/stats/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // 获取用户基本信息
    const user = await query(
      'SELECT * FROM users WHERE user_id = ?',
      [userId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    // 获取订单统计
    const orderStats = await query(
      `SELECT
         COUNT(*) as total_orders,
         COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
         SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_amount
       FROM orders WHERE user_id = ?`,
      [userId]
    );

    // 获取会员历史
    const membershipHistory = await query(
      `SELECT membership_type, start_time, expire_time, status
       FROM memberships WHERE user_id = ?
       ORDER BY created_at DESC`,
      [userId]
    );

    res.json({
      success: true,
      data: {
        user: user[0],
        stats: orderStats[0],
        membershipHistory
      }
    });

  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
