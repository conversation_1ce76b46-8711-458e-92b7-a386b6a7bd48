const express = require('express');
const router = express.Router();
const { query, transaction } = require('../config/database');
const moment = require('moment');

/**
 * 用户登录/注册
 */
router.post('/login', async (req, res) => {
  try {
    const { openid, appId, userInfo } = req.body;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 检查用户是否存在
    let user = await query(
      'SELECT * FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      // 创建新用户
      const insertResult = await query(
        `INSERT INTO users (openid, app_id, nickname, avatar_url, last_login_at) 
         VALUES (?, ?, ?, ?, NOW())`,
        [openid, appId, userInfo?.nickname || '', userInfo?.avatar_url || '']
      );

      // 获取新创建的用户
      user = await query(
        'SELECT * FROM users WHERE user_id = ?',
        [insertResult.insertId]
      );
    } else {
      // 更新最后登录时间和用户信息
      await query(
        `UPDATE users SET 
         last_login_at = NOW(),
         nickname = COALESCE(?, nickname),
         avatar_url = COALESCE(?, avatar_url)
         WHERE openid = ? AND app_id = ?`,
        [userInfo?.nickname, userInfo?.avatar_url, openid, appId]
      );
      
      user = await query(
        'SELECT * FROM users WHERE openid = ? AND app_id = ?',
        [openid, appId]
      );
    }

    const userData = user[0];

    // 检查是否需要重置每日免费次数
    const today = moment().format('YYYY-MM-DD');
    const lastReset = moment(userData.last_daily_reset).format('YYYY-MM-DD');
    
    if (today !== lastReset) {
      await query(
        'UPDATE users SET daily_free_count = 1, last_daily_reset = CURDATE() WHERE user_id = ?',
        [userData.user_id]
      );
      userData.daily_free_count = 1;
    }

    // 获取用户会员状态
    const membership = await query(
      `SELECT * FROM memberships 
       WHERE user_id = ? AND status = 'active' 
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userData.user_id]
    );

    res.json({
      success: true,
      data: {
        user: {
          userId: userData.user_id,
          openid: userData.openid,
          appId: userData.app_id,
          nickname: userData.nickname,
          avatarUrl: userData.avatar_url,
          createdAt: userData.created_at
        },
        quota: {
          dailyFreeCount: userData.daily_free_count,
          totalUsageCount: userData.total_usage_count,
          lastDailyReset: userData.last_daily_reset
        },
        membership: membership.length > 0 ? {
          isActive: true,
          type: membership[0].membership_type,
          startTime: membership[0].start_time,
          expireTime: membership[0].expire_time
        } : {
          isActive: false,
          type: null,
          startTime: null,
          expireTime: null
        }
      },
      message: user.length === 1 ? '登录成功' : '注册成功'
    });

  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户状态
 */
router.get('/status', async (req, res) => {
  try {
    const { openid, appId } = req.query;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    // 获取用户信息
    const user = await query(
      'SELECT * FROM users WHERE openid = ? AND app_id = ?',
      [openid, appId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    const userData = user[0];

    // 检查会员状态
    const membership = await query(
      `SELECT * FROM memberships 
       WHERE user_id = ? AND status = 'active' 
       AND (expire_time IS NULL OR expire_time > NOW())
       ORDER BY created_at DESC LIMIT 1`,
      [userData.user_id]
    );

    // 检查是否可以使用压缩功能
    const canUseCompression = membership.length > 0 || userData.daily_free_count > 0;

    res.json({
      success: true,
      data: {
        userId: userData.user_id,
        canUseCompression,
        quota: {
          dailyFreeCount: userData.daily_free_count,
          totalUsageCount: userData.total_usage_count
        },
        membership: membership.length > 0 ? {
          isActive: true,
          type: membership[0].membership_type,
          expireTime: membership[0].expire_time
        } : {
          isActive: false,
          type: null,
          expireTime: null
        }
      }
    });

  } catch (error) {
    console.error('获取用户状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 使用免费次数
 */
router.post('/use-free', async (req, res) => {
  try {
    const { openid, appId } = req.body;

    if (!openid || !appId) {
      return res.status(400).json({
        success: false,
        error: 'openid和appId不能为空'
      });
    }

    const result = await transaction(async (connection) => {
      // 获取用户信息（加锁）
      const [users] = await connection.execute(
        'SELECT * FROM users WHERE openid = ? AND app_id = ? FOR UPDATE',
        [openid, appId]
      );

      if (users.length === 0) {
        throw new Error('用户不存在');
      }

      const user = users[0];

      // 检查会员状态
      const [memberships] = await connection.execute(
        `SELECT * FROM memberships 
         WHERE user_id = ? AND status = 'active' 
         AND (expire_time IS NULL OR expire_time > NOW())
         LIMIT 1`,
        [user.user_id]
      );

      // 如果是会员，直接允许使用
      if (memberships.length > 0) {
        // 更新总使用次数
        await connection.execute(
          'UPDATE users SET total_usage_count = total_usage_count + 1 WHERE user_id = ?',
          [user.user_id]
        );

        return {
          success: true,
          usedFree: false,
          remainingFree: user.daily_free_count,
          membershipType: memberships[0].membership_type
        };
      }

      // 检查免费次数
      if (user.daily_free_count <= 0) {
        throw new Error('今日免费次数已用完，请开通会员');
      }

      // 扣除免费次数
      await connection.execute(
        'UPDATE users SET daily_free_count = daily_free_count - 1, total_usage_count = total_usage_count + 1 WHERE user_id = ?',
        [user.user_id]
      );

      return {
        success: true,
        usedFree: true,
        remainingFree: user.daily_free_count - 1,
        membershipType: null
      };
    });

    res.json({
      success: true,
      data: result,
      message: '使用成功'
    });

  } catch (error) {
    console.error('使用免费次数失败:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取用户统计信息
 */
router.get('/stats/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // 获取用户基本信息
    const user = await query(
      'SELECT * FROM users WHERE user_id = ?',
      [userId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    // 获取订单统计
    const orderStats = await query(
      `SELECT
         COUNT(*) as total_orders,
         COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
         SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_amount
       FROM orders WHERE user_id = ?`,
      [userId]
    );

    // 获取会员历史
    const membershipHistory = await query(
      `SELECT membership_type, start_time, expire_time, status
       FROM memberships WHERE user_id = ?
       ORDER BY created_at DESC`,
      [userId]
    );

    res.json({
      success: true,
      data: {
        user: user[0],
        stats: orderStats[0],
        membershipHistory
      }
    });

  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
