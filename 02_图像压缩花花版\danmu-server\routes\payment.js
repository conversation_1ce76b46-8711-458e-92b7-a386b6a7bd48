const express = require('express');
const router = express.Router();
const WechatPayService = require('../services/wechatPayService');
const Order = require('../models/Order');
const { formatSuccess, formatError } = require('../utils/helpers');

/**
 * 微信支付回调处理
 * POST /api/payment/notify
 */
router.post('/notify', async (req, res) => {
  try {
    console.log('🔔 收到微信支付回调');
    console.log('Headers:', req.headers);
    console.log('Body:', req.body);

    // 获取原始请求体（用于签名验证）
    const rawBody = JSON.stringify(req.body);
    
    // 创建微信支付服务实例
    const payService = new WechatPayService();
    
    // 处理支付回调
    const result = await payService.handlePaymentNotify(req.headers, rawBody);
    
    if (result.success && result.data) {
      const { out_trade_no, transaction_id, trade_state } = result.data;
      
      if (trade_state === 'SUCCESS') {
        console.log('✅ 支付成功，处理订单:', out_trade_no);
        
        // 处理订单支付成功
        await Order.processPaymentSuccess(out_trade_no, {
          transactionId: transaction_id,
          paymentMethod: 'wechat_pay'
        });
        
        console.log('✅ 订单处理完成:', out_trade_no);
      } else {
        console.log('⚠️ 支付状态异常:', trade_state);
        
        // 更新订单状态为失败
        await Order.updatePaymentStatus(out_trade_no, 'failed');
      }
    }

    // 返回微信要求的成功响应
    res.json({
      code: 'SUCCESS',
      message: '成功'
    });

  } catch (error) {
    console.error('❌ 微信支付回调处理失败:', error);
    
    // 返回微信要求的失败响应
    res.json({
      code: 'FAIL',
      message: error.message || '处理失败'
    });
  }
});

/**
 * 创建支付订单
 * POST /api/payment/create
 */
router.post('/create', async (req, res) => {
  try {
    console.log('🚀 开始创建支付订单...');
    console.log('📝 请求参数:', req.body);

    const { orderId, merchantId } = req.body;

    if (!orderId) {
      console.log('❌ 缺少订单ID');
      return res.status(400).json(formatError(
        '缺少订单ID',
        'MISSING_ORDER_ID'
      ));
    }

    console.log('🔍 查询订单信息, orderId:', orderId);

    // 获取订单信息
    const order = await Order.getOrder(orderId);
    if (!order) {
      console.log('❌ 订单不存在:', orderId);
      return res.status(404).json(formatError(
        '订单不存在',
        'ORDER_NOT_FOUND'
      ));
    }

    console.log('✅ 订单信息获取成功:', {
      orderId: order.order_id,
      amount: order.amount,
      productType: order.product_type,
      paymentStatus: order.payment_status,
      openid: order.openid
    });

    if (order.payment_status === 'paid') {
      console.log('❌ 订单已支付:', orderId);
      return res.status(400).json(formatError(
        '订单已支付',
        'ORDER_ALREADY_PAID'
      ));
    }

    console.log('🏪 创建微信支付服务实例, merchantId:', merchantId || 'default');

    // 创建微信支付服务实例
    const payService = new WechatPayService(merchantId);

    console.log('💰 开始创建微信支付订单...');
    const payOrderData = {
      orderId: order.order_id,
      amount: order.amount,
      description: `弹幕小程序-${order.product_type === 'temp_vip' ? '24小时会员' : '终身会员'}`,
      openid: order.openid
    };
    console.log('💰 支付订单数据:', payOrderData);

    // 创建微信支付订单
    const payResult = await payService.createOrder(payOrderData);

    console.log('✅ 微信支付订单创建成功:', {
      prepayId: payResult.prepay_id,
      orderId: payResult.out_trade_no || order.order_id
    });

    console.log('🔧 生成小程序支付参数...');

    // 生成小程序支付参数
    const payParams = payService.generateMiniProgramPayParams(payResult.prepay_id);

    console.log('✅ 支付参数生成成功:', {
      timeStamp: payParams.timeStamp,
      nonceStr: payParams.nonceStr,
      package: payParams.package,
      signType: payParams.signType,
      paySign: payParams.paySign ? '***' : undefined
    });

    const responseData = {
      orderId: order.order_id,
      payParams: payParams,
      prepayId: payResult.prepay_id
    };

    console.log('📤 发送支付创建成功响应...');
    res.json(formatSuccess(responseData, '支付订单创建成功'));
    console.log('✅ 支付订单创建完成!');

  } catch (error) {
    console.error('❌ 创建支付订单失败:', error);
    console.error('❌ 错误堆栈:', error.stack);

    res.status(500).json(formatError(
      '创建支付订单失败',
      'CREATE_PAYMENT_FAILED',
      error.message
    ));
  }
});

/**
 * 查询支付状态
 * GET /api/payment/query/:orderId
 */
router.get('/query/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    const { merchantId } = req.query;

    // 获取订单信息
    const order = await Order.getOrder(orderId);
    if (!order) {
      return res.status(404).json(formatError(
        '订单不存在',
        'ORDER_NOT_FOUND'
      ));
    }

    // 如果订单已支付，直接返回状态
    if (order.payment_status === 'paid') {
      return res.json(formatSuccess({
        orderId: order.order_id,
        paymentStatus: 'paid',
        transactionId: order.transaction_id,
        paidAt: order.paid_at
      }, '订单已支付'));
    }

    // 查询微信支付状态
    const payService = new WechatPayService(merchantId);
    const payResult = await payService.queryOrder(orderId);

    if (payResult.trade_state === 'SUCCESS') {
      // 支付成功，更新订单状态
      await Order.processPaymentSuccess(orderId, {
        transactionId: payResult.transaction_id,
        paymentMethod: 'wechat_pay'
      });

      return res.json(formatSuccess({
        orderId: orderId,
        paymentStatus: 'paid',
        transactionId: payResult.transaction_id
      }, '支付成功'));
    } else {
      return res.json(formatSuccess({
        orderId: orderId,
        paymentStatus: order.payment_status,
        tradeState: payResult.trade_state
      }, '查询支付状态成功'));
    }

  } catch (error) {
    console.error('查询支付状态失败:', error);
    res.status(500).json(formatError(
      '查询支付状态失败',
      'QUERY_PAYMENT_FAILED',
      error.message
    ));
  }
});

/**
 * 关闭支付订单
 * POST /api/payment/close
 */
router.post('/close', async (req, res) => {
  try {
    const { orderId, merchantId } = req.body;

    if (!orderId) {
      return res.status(400).json(formatError(
        '缺少订单ID',
        'MISSING_ORDER_ID'
      ));
    }

    // 获取订单信息
    const order = await Order.getOrder(orderId);
    if (!order) {
      return res.status(404).json(formatError(
        '订单不存在',
        'ORDER_NOT_FOUND'
      ));
    }

    if (order.payment_status === 'paid') {
      return res.status(400).json(formatError(
        '订单已支付，无法关闭',
        'ORDER_ALREADY_PAID'
      ));
    }

    // 关闭微信支付订单
    const payService = new WechatPayService(merchantId);
    await payService.closeOrder(orderId);

    // 更新订单状态
    await Order.updatePaymentStatus(orderId, 'cancelled');

    res.json(formatSuccess({
      orderId: orderId,
      paymentStatus: 'cancelled'
    }, '订单已关闭'));

  } catch (error) {
    console.error('关闭支付订单失败:', error);
    res.status(500).json(formatError(
      '关闭支付订单失败',
      'CLOSE_PAYMENT_FAILED',
      error.message
    ));
  }
});

/**
 * 获取支付配置信息
 * GET /api/payment/config
 */
router.get('/config', async (req, res) => {
  try {
    const { merchantId } = req.query;
    
    const payService = new WechatPayService(merchantId);
    const config = payService.config;

    res.json(formatSuccess({
      merchantId: config.merchantId,
      appId: config.appId,
      description: config.description,
      isActive: config.isActive,
      products: config.products
    }, '获取支付配置成功'));

  } catch (error) {
    console.error('获取支付配置失败:', error);
    res.status(500).json(formatError(
      '获取支付配置失败',
      'GET_PAYMENT_CONFIG_FAILED',
      error.message
    ));
  }
});

module.exports = router;
