const express = require('express');
const router = express.Router();
const WechatService = require('../services/wechatService');
const User = require('../models/User');
const { formatSuccess, formatError } = require('../utils/helpers');

/**
 * 微信小程序登录
 * POST /api/auth/login
 */
router.post('/login', async (req, res) => {
  try {
    const { code, userInfo, appId } = req.body;

    if (!code) {
      return res.status(400).json(formatError(
        '缺少登录code',
        'MISSING_LOGIN_CODE'
      ));
    }

    console.log('🚀 开始微信登录流程:', {
      code: code,
      appId: appId || '未提供'
    });

    // 1. 通过code获取openid（支持多AppID）
    const wechatResult = await WechatService.getOpenidByCode(code, appId);
    const { openid, session_key } = wechatResult;
    
    if (!openid) {
      return res.status(400).json(formatError(
        '获取openid失败',
        'GET_OPENID_FAILED'
      ));
    }
    
    console.log('✅ 获取openid成功:', openid);

    // 2. 获取或创建用户
    const user = await User.getOrCreateUser(openid);

    // 构造用户信息（使用默认值）
    const responseUserInfo = {
      userId: user.user_id,
      openid: user.openid,
      nickname: '微信用户',
      avatarUrl: '',
      createdAt: user.created_at
    };

    console.log('✅ 用户信息获取成功:', responseUserInfo.nickname);

    // 3. 获取用户额度信息
    console.log('🔍 开始获取用户额度, userId:', user.user_id);
    const quota = await User.getUserQuota(user.user_id);

    console.log('✅ 用户额度获取成功:', {
      freeShareCount: quota.free_share_count,
      dailyFullscreenCount: quota.daily_fullscreen_count,
      hasVipAccess: quota.has_vip_access
    });

    console.log('🔍 完整quota对象:', JSON.stringify(quota, null, 2));

    // 4. 构造返回数据
    const responseData = {
      user: responseUserInfo,
      quota: {
        freeShareCount: quota.free_share_count || 3,
        dailyFullscreenCount: quota.daily_fullscreen_count || 3,
        totalShareCount: quota.total_share_count || 0,
        totalFullscreenCount: quota.total_fullscreen_count || 0,
        hasVipAccess: quota.has_vip_access || false,
        isVip: quota.is_vip || false,
        vipExpireTime: quota.vip_expire_time || null,
        tempVipExpireTime: quota.temp_vip_expire_time || null
      }
    };

    console.log('🔍 响应数据构造完成:', JSON.stringify(responseData, null, 2));

    // 5. 格式化响应
    console.log('🔍 开始格式化响应...');
    const result = formatSuccess(responseData, '登录成功');
    console.log('🔍 格式化响应完成:', JSON.stringify(result, null, 2));

    // 6. 发送响应
    console.log('📤 开始发送响应...');
    res.json(result);
    console.log('📤 响应发送完成!');
    
  } catch (error) {
    console.error('❌ 微信登录失败:', error);
    
    // 根据错误类型返回不同的错误信息
    if (error.message.includes('微信API错误')) {
      res.status(400).json(formatError(
        '微信登录失败，请重试',
        'WECHAT_API_ERROR',
        error.message
      ));
    } else {
      res.status(500).json(formatError(
        '登录失败',
        'LOGIN_FAILED',
        error.message
      ));
    }
  }
});

/**
 * 刷新用户session
 * POST /api/auth/refresh
 */
router.post('/refresh', async (req, res) => {
  try {
    const { openid } = req.body;
    
    if (!openid) {
      return res.status(400).json(formatError(
        '缺少openid',
        'MISSING_OPENID'
      ));
    }
    
    // 获取用户信息
    const user = await User.getByOpenid(openid);
    if (!user) {
      return res.status(404).json(formatError(
        '用户不存在',
        'USER_NOT_FOUND'
      ));
    }
    
    // 获取最新的额度信息
    const quota = await User.getUserQuota(user.user_id);
    
    res.json(formatSuccess({
      user: {
        userId: user.user_id,
        openid: user.openid,
        nickname: '微信用户',
        avatarUrl: '',
        createdAt: user.created_at
      },
      quota: {
        freeShareCount: quota.free_share_count,
        dailyFullscreenCount: quota.daily_fullscreen_count,
        totalShareCount: quota.total_share_count,
        totalFullscreenCount: quota.total_fullscreen_count,
        hasVipAccess: quota.has_vip_access,
        isVip: quota.is_vip,
        vipExpireTime: quota.vip_expire_time,
        tempVipExpireTime: quota.temp_vip_expire_time
      }
    }, '刷新成功'));
    
  } catch (error) {
    console.error('刷新用户session失败:', error);
    res.status(500).json(formatError(
      '刷新失败',
      'REFRESH_FAILED',
      error.message
    ));
  }
});

module.exports = router;
