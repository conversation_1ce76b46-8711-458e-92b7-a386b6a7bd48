# 弹幕分享服务器部署指南

本文档详细说明如何在宝塔面板环境下部署弹幕分享服务器（包含用户额度管理系统）。

## 🚀 快速部署

### 前置条件
- Node.js 14.0+
- MySQL 8.0.36+
- PM2 (可选，用于进程管理)

### 1. 上传文件
将整个 `danmu-server` 文件夹上传到服务器，推荐路径：
```
/www/wwwroot/danmu-server/
```

### 2. 创建数据库
```sql
CREATE DATABASE danmu_share CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 执行数据库脚本
在 phpMyAdmin 或命令行中执行 `database.sql` 文件内容。

### 4. 配置环境变量
```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

**最小配置示例：**
```env
# 服务器端口
PORT=8850

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=你的数据库密码
DB_NAME=danmu_share

# 运行环境
NODE_ENV=production
```

### 5. 安装依赖
```bash
cd /www/wwwroot/danmu-server
npm install
```

### 6. 测试启动
```bash
npm start
```

看到以下输出表示成功：
```
======================================
   弹幕分享服务器 (danmu-server)
======================================

服务器已启动:
- 端口: 8850
- 环境: production
- 数据库: danmu_share

API地址: http://localhost:8850
健康检查: http://localhost:8850/health
```

### 7. 使用 PM2 管理（推荐）
```bash
# 安装 PM2
npm install -g pm2

# 创建日志目录
mkdir logs

# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 设置开机自启
pm2 startup
pm2 save
```

### 8. 配置 Nginx 反向代理
```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8850;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔧 配置说明

### 环境变量详解

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `PORT` | 8850 | API服务端口 |
| `NODE_ENV` | production | 运行环境 |
| `DB_HOST` | localhost | 数据库主机 |
| `DB_USER` | root | 数据库用户 |
| `DB_PASSWORD` | (空) | 数据库密码 |
| `DB_NAME` | danmu_share | 数据库名 |
| `CONFIG_EXPIRE_DAYS` | 30 | 分享过期天数 |
| `MAX_CONFIG_SIZE` | 10240 | 最大配置大小(字节) |

### API 接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/` | GET | 服务信息 |
| `/health` | GET | 健康检查 |
| **弹幕分享** | | |
| `/api/danmu/save` | POST | 保存分享（含额度验证） |
| `/api/danmu/get/:shareId` | GET | 获取分享 |
| `/api/danmu/stats` | GET | 统计信息 |
| `/api/danmu/popular` | GET | 热门分享 |
| `/api/danmu/history` | GET | 分享历史 |
| **用户管理** | | |
| `/api/user/login` | POST | 用户登录/注册 |
| `/api/user/quota/:userId` | GET | 获取用户额度 |
| `/api/user/check-share` | POST | 检查分享权限 |
| `/api/user/check-fullscreen` | POST | 检查全屏权限 |
| `/api/user/use-share` | POST | 使用分享次数 |
| `/api/user/use-fullscreen` | POST | 使用全屏次数 |
| **订单管理** | | |
| `/api/order/products` | GET | 获取产品配置 |
| `/api/order/create` | POST | 创建订单 |
| `/api/order/:orderId` | GET | 获取订单详情 |
| `/api/order/user/:userId` | GET | 获取用户订单 |
| `/api/order/pay-success` | POST | 模拟支付成功 |
| **全屏功能** | | |
| `/api/fullscreen/check` | POST | 检查全屏权限 |
| `/api/fullscreen/use` | POST | 使用全屏功能 |
| `/api/fullscreen/stats` | GET | 全屏使用统计 |

## 🛠️ 常用命令

### PM2 管理
```bash
# 查看应用列表
pm2 list

# 重启应用
pm2 restart danmu-server

# 停止应用
pm2 stop danmu-server

# 查看日志
pm2 logs danmu-server

# 监控
pm2 monit
```

### 数据库维护
```bash
# 清理过期分享
curl -X DELETE http://localhost:8850/api/danmu/cleanup

# 查看统计
curl http://localhost:8850/api/danmu/stats
```

## 🔍 故障排除

### 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :8850

# 杀死进程
kill -9 进程ID
```

### 数据库连接失败
1. 检查数据库服务状态
2. 验证用户名密码
3. 确认数据库名称
4. 检查防火墙设置

### 权限问题
```bash
# 修改文件权限
chown -R www:www /www/wwwroot/danmu-server
chmod -R 755 /www/wwwroot/danmu-server
```

## 📊 性能优化

### 数据库优化
- 定期清理过期数据
- 添加适当索引
- 监控查询性能

### 服务器优化
- 使用 PM2 集群模式
- 配置 Nginx 缓存
- 启用 gzip 压缩

## 🔒 安全建议

1. **数据库安全**
   - 创建专用数据库用户
   - 设置强密码
   - 限制访问权限

2. **服务器安全**
   - 定期更新依赖
   - 配置防火墙
   - 启用 HTTPS

3. **应用安全**
   - 限制请求频率
   - 验证输入数据
   - 记录访问日志

## 📞 技术支持

如遇问题，请检查：
1. 服务器日志：`pm2 logs danmu-server`
2. 数据库连接
3. 端口是否开放
4. 环境变量配置
