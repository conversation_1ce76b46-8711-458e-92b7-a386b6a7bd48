const cron = require('node-cron');
const { query } = require('../config/database');
const moment = require('moment');

class CronJobs {
  constructor() {
    this.jobs = [];
  }

  /**
   * 启动所有定时任务
   */
  start() {
    console.log('🕐 启动定时任务...');

    // 每小时检查会员过期状态
    this.jobs.push(
      cron.schedule('30 * * * *', async () => {
        await this.updateMembershipStatus();
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      })
    );

    // 每小时清理过期订单
    this.jobs.push(
      cron.schedule('0 * * * *', async () => {
        await this.cleanExpiredOrders();
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      })
    );

    // 每周日凌晨3点清理旧数据
    this.jobs.push(
      cron.schedule('0 3 * * 0', async () => {
        await this.cleanOldData();
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      })
    );

    // 启动所有任务
    this.jobs.forEach(job => job.start());
    console.log(`✅ ${this.jobs.length} 个定时任务启动成功`);
  }

  /**
   * 停止所有定时任务
   */
  stop() {
    console.log('🛑 停止定时任务...');
    this.jobs.forEach(job => job.stop());
    this.jobs = [];
    console.log('✅ 所有定时任务已停止');
  }

  /**
   * 清理过期的临时数据（预留方法）
   */
  async cleanTempData() {
    try {
      console.log('🔄 开始清理临时数据...');

      // 这里可以添加清理临时数据的逻辑
      // 比如清理过期的缓存、临时文件等

      console.log('✅ 临时数据清理完成');
    } catch (error) {
      console.error('❌ 清理临时数据失败:', error);
    }
  }

  /**
   * 更新会员过期状态
   */
  async updateMembershipStatus() {
    try {
      console.log('🔄 开始检查会员过期状态...');
      
      const result = await query(`
        UPDATE memberships 
        SET status = 'expired' 
        WHERE status = 'active' 
        AND expire_time IS NOT NULL 
        AND expire_time < NOW()
      `);

      if (result.affectedRows > 0) {
        console.log(`✅ 更新过期会员状态完成，影响记录数: ${result.affectedRows}`);
      }
    } catch (error) {
      console.error('❌ 更新会员过期状态失败:', error);
    }
  }

  /**
   * 清理过期订单
   */
  async cleanExpiredOrders() {
    try {
      console.log('🔄 开始清理过期订单...');
      
      // 将过期的待支付订单标记为失败
      const result = await query(`
        UPDATE orders 
        SET payment_status = 'failed' 
        WHERE payment_status = 'pending' 
        AND expire_at IS NOT NULL 
        AND expire_at < NOW()
      `);

      if (result.affectedRows > 0) {
        console.log(`✅ 清理过期订单完成，影响订单数: ${result.affectedRows}`);
      }
    } catch (error) {
      console.error('❌ 清理过期订单失败:', error);
    }
  }

  /**
   * 清理旧数据
   */
  async cleanOldData() {
    try {
      console.log('🔄 开始清理旧数据...');
      
      // 清理90天前的失败订单
      const orderResult = await query(`
        DELETE FROM orders 
        WHERE payment_status = 'failed' 
        AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
      `);

      // 清理180天前的过期会员记录
      const membershipResult = await query(`
        DELETE FROM memberships 
        WHERE status = 'expired' 
        AND updated_at < DATE_SUB(NOW(), INTERVAL 180 DAY)
      `);

      console.log(`✅ 清理旧数据完成:`);
      console.log(`   - 清理失败订单: ${orderResult.affectedRows} 条`);
      console.log(`   - 清理过期会员记录: ${membershipResult.affectedRows} 条`);
    } catch (error) {
      console.error('❌ 清理旧数据失败:', error);
    }
  }

  /**
   * 手动执行清理临时数据
   */
  async manualCleanTempData() {
    await this.cleanTempData();
  }

  /**
   * 手动执行会员状态更新
   */
  async manualUpdateMembershipStatus() {
    await this.updateMembershipStatus();
  }

  /**
   * 手动执行清理过期订单
   */
  async manualCleanExpiredOrders() {
    await this.cleanExpiredOrders();
  }

  /**
   * 手动执行清理旧数据
   */
  async manualCleanOldData() {
    await this.cleanOldData();
  }

  /**
   * 获取定时任务状态
   */
  getStatus() {
    return {
      totalJobs: this.jobs.length,
      runningJobs: this.jobs.filter(job => job.running).length,
      jobs: this.jobs.map((job, index) => ({
        id: index,
        running: job.running,
        lastDate: job.lastDate,
        nextDate: job.nextDate
      }))
    };
  }

  /**
   * 获取系统统计信息
   */
  async getSystemStats() {
    try {
      const stats = await query(`
        SELECT 
          (SELECT COUNT(*) FROM users) as total_users,
          (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as new_users_today,
          (SELECT COUNT(*) FROM orders WHERE payment_status = 'paid') as total_paid_orders,
          (SELECT COUNT(*) FROM orders WHERE DATE(paid_at) = CURDATE() AND payment_status = 'paid') as paid_orders_today,
          (SELECT COUNT(*) FROM memberships WHERE status = 'active') as active_memberships,
          (SELECT COUNT(*) FROM memberships WHERE status = 'active' AND membership_type = 'permanent') as permanent_members,
          (SELECT COUNT(*) FROM memberships WHERE status = 'active' AND membership_type = 'day_card') as day_card_members,
          (SELECT SUM(amount) FROM orders WHERE payment_status = 'paid') as total_revenue,
          (SELECT SUM(amount) FROM orders WHERE DATE(paid_at) = CURDATE() AND payment_status = 'paid') as revenue_today
      `);

      return {
        ...stats[0],
        total_revenue: parseFloat(stats[0].total_revenue || 0),
        revenue_today: parseFloat(stats[0].revenue_today || 0),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取系统统计失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const cronJobs = new CronJobs();

module.exports = cronJobs;
