const crypto = require('crypto');
const CertManager = require('./certManager');
const WechatCertService = require('./wechatCertService');
const MerchantMatcher = require('./merchantMatcher');

/**
 * 微信支付服务
 */
class WechatPayService {
  constructor(merchantId = null, appId = null) {
    // 智能匹配商户：优先使用传入的merchantId，否则根据appId匹配
    if (!merchantId && appId) {
      merchantId = MerchantMatcher.getMerchantIdByAppId(appId);
      console.log(`🎯 根据AppID ${appId} 自动匹配商户: ${merchantId}`);
    } else if (!merchantId) {
      merchantId = MerchantMatcher.getDefaultMerchant();
      console.log(`🔧 使用默认商户: ${merchantId}`);
    }

    this.merchantId = merchantId;
    this.config = CertManager.getMerchantConfig(merchantId);
    this.certService = new WechatCertService(merchantId);

    console.log(`💳 初始化微信支付服务 - 商户: ${merchantId} (${this.config.businessName || this.config.description})`);
  }

  /**
   * 静态方法：根据AppID创建支付服务实例
   * @param {string} appId 小程序AppID
   * @returns {WechatPayService} 支付服务实例
   */
  static createByAppId(appId) {
    return new WechatPayService(null, appId);
  }

  /**
   * 静态方法：根据商户ID创建支付服务实例
   * @param {string} merchantId 商户ID
   * @returns {WechatPayService} 支付服务实例
   */
  static createByMerchantId(merchantId) {
    return new WechatPayService(merchantId);
  }

  /**
   * 获取微信支付平台证书公钥
   * @param {string} serialNo 证书序列号
   * @returns {Promise<string|null>} 平台证书公钥
   */
  async getWechatPlatformPublicKey(serialNo) {
    try {
      return await this.certService.getPlatformPublicKey(serialNo);
    } catch (error) {
      console.error('获取微信支付平台证书失败:', error);
      return null;
    }
  }

  /**
   * 验证微信支付回调签名（支持两种方式）
   * @param {string} timestamp 时间戳
   * @param {string} nonce 随机字符串
   * @param {string} body 请求体
   * @param {string} signature 签名（Base64编码）
   * @param {string} serialNo 证书序列号或公钥ID
   * @returns {Promise<boolean>} 验证结果
   */
  async verifyNotifySignature(timestamp, nonce, body, signature, serialNo) {
    try {
      // 构造验证签名的字符串
      const message = `${timestamp}\n${nonce}\n${body}\n`;

      let publicKey;
      let verifyMethod;

      // 根据配置的验签方式选择验签方法
      if (this.config.verifyMethod === 'public_key') {
        // 微信支付公钥验签
        verifyMethod = 'public_key';
        const publicKeyId = this.config.public_key_id || serialNo;
        publicKey = await this.getWechatPublicKey(publicKeyId);
        console.log('🔑 使用微信支付公钥验签:', publicKeyId);
      } else {
        // 平台证书验签
        verifyMethod = 'platform_cert';
        publicKey = await this.getWechatPlatformPublicKey(serialNo);
        console.log('📜 使用平台证书验签:', serialNo);
      }

      if (!publicKey) {
        console.error(`❌ 无法获取${verifyMethod === 'public_key' ? '微信支付公钥' : '平台证书公钥'}`);
        return false;
      }

      // 使用RSA-SHA256验证签名
      const verify = crypto.createVerify('RSA-SHA256');
      verify.update(message, 'utf8');

      // 验证签名（signature是Base64编码的）
      const isValid = verify.verify(publicKey, signature, 'base64');

      console.log(isValid ? '✅ 回调签名验证成功' : '❌ 回调签名验证失败');
      return isValid;

    } catch (error) {
      console.error('❌ 验证回调签名失败:', error);
      return false;
    }
  }

  /**
   * 获取微信支付公钥（新方式）
   * @param {string} publicKeyId 公钥ID
   * @returns {Promise<string|null>} 公钥
   */
  async getWechatPublicKey(publicKeyId) {
    try {
      const fs = require('fs');
      const path = require('path');

      // 优先使用配置文件中指定的公钥路径
      let publicKeyPath;
      if (this.config.public_key_path) {
        // 如果是绝对路径，直接使用；如果是相对路径，相对于证书目录
        if (path.isAbsolute(this.config.public_key_path)) {
          publicKeyPath = this.config.public_key_path;
        } else {
          publicKeyPath = path.join(__dirname, '../certs', this.merchantId, this.config.public_key_path);
        }
      } else {
        // 默认路径
        publicKeyPath = path.join(__dirname, '../certs', this.merchantId, 'pub_key.pem');
      }

      console.log('🔍 查找微信支付公钥文件:', publicKeyPath);

      if (fs.existsSync(publicKeyPath)) {
        const publicKey = fs.readFileSync(publicKeyPath, 'utf8');
        console.log('✅ 从本地文件加载微信支付公钥成功');
        return publicKey;
      }

      console.warn('⚠️ 未找到微信支付公钥文件:', publicKeyPath);
      console.warn('请确保公钥文件存在，或检查配置文件中的 public_key_path 设置');
      return null;

    } catch (error) {
      console.error('❌ 获取微信支付公钥失败:', error);
      return null;
    }
  }

  /**
   * 临时的签名验证方法（开发测试用）
   * 注意：生产环境必须使用平台证书验签
   */
  verifyNotifySignatureForDev(timestamp, nonce, body, signature) {
    try {
      console.warn('⚠️ 使用开发模式签名验证，生产环境请使用平台证书');

      // 开发环境可以暂时跳过签名验证，或使用简化验证
      // 但这不安全，仅用于开发测试

      return true; // 临时返回true，实际应该实现正确的验证

    } catch (error) {
      console.error('开发模式签名验证失败:', error);
      return false;
    }
  }

  /**
   * 解密微信支付回调数据
   * @param {object} encryptedData 加密数据
   * @returns {object} 解密后的数据
   */
  decryptNotifyData(encryptedData) {
    try {
      const { ciphertext, nonce, associated_data } = encryptedData;

      // 使用AES-256-GCM解密
      const apiV3Key = this.config.api_v3_key || this.config.apiV3Key;
      console.log('🔑 使用API v3密钥解密回调数据...');

      if (!apiV3Key) {
        throw new Error('API v3密钥未配置');
      }

      // 解码密文和认证标签
      const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
      const authTagLength = 16; // GCM认证标签长度为16字节
      const authTag = ciphertextBuffer.slice(-authTagLength);
      const encryptedDataBuffer = ciphertextBuffer.slice(0, -authTagLength);

      console.log('🔍 解密参数:', {
        ciphertextLength: ciphertextBuffer.length,
        authTagLength: authTag.length,
        encryptedDataLength: encryptedDataBuffer.length,
        nonceLength: nonce.length
      });

      const decipher = crypto.createDecipheriv('aes-256-gcm', apiV3Key, nonce);
      decipher.setAuthTag(authTag);
      decipher.setAAD(Buffer.from(associated_data, 'utf8'));

      let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('解密回调数据失败:', error);
      throw new Error('解密回调数据失败');
    }
  }

  /**
   * 处理微信支付回调
   * @param {object} headers 请求头
   * @param {string} body 请求体
   * @returns {object} 处理结果
   */
  async handlePaymentNotify(headers, body) {
    try {
      // 获取签名相关信息
      const timestamp = headers['wechatpay-timestamp'];
      const nonce = headers['wechatpay-nonce'];
      const signature = headers['wechatpay-signature'];
      const serial = headers['wechatpay-serial'];

      if (!timestamp || !nonce || !signature || !serial) {
        throw new Error('缺少必要的签名信息');
      }

      // 验证签名（使用平台证书）
      const signatureValid = await this.verifyNotifySignature(timestamp, nonce, body, signature, serial);
      if (!signatureValid) {
        // 如果平台证书验证失败，在开发环境可以使用临时验证
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ 平台证书验证失败，使用开发模式验证');
          if (!this.verifyNotifySignatureForDev(timestamp, nonce, body, signature)) {
            throw new Error('签名验证失败');
          }
        } else {
          throw new Error('签名验证失败');
        }
      }

      // 解析回调数据
      const notifyData = JSON.parse(body);
      
      if (notifyData.event_type !== 'TRANSACTION.SUCCESS') {
        console.log('非支付成功事件:', notifyData.event_type);
        return { code: 'SUCCESS', message: '成功' };
      }

      // 解密资源数据
      const decryptedData = this.decryptNotifyData(notifyData.resource);
      
      console.log('✅ 微信支付回调验证成功:', {
        out_trade_no: decryptedData.out_trade_no,
        transaction_id: decryptedData.transaction_id,
        trade_state: decryptedData.trade_state
      });

      return {
        success: true,
        data: decryptedData
      };

    } catch (error) {
      console.error('❌ 处理微信支付回调失败:', error);
      throw error;
    }
  }

  /**
   * 创建统一下单请求
   * @param {object} orderInfo 订单信息
   * @returns {object} 下单结果
   */
  async createOrder(orderInfo) {
    try {
      const { orderId, amount, description, openid } = orderInfo;

      // 验证必要参数
      if (!orderId || !amount || !description || !openid) {
        throw new Error('缺少必要的订单参数');
      }

      // 验证金额格式（确保是正数且不超过限制）
      const totalAmount = Math.round(amount * 100); // 转换为分
      if (totalAmount <= 0 || totalAmount > 100000000) { // 最大1000万分=10万元
        throw new Error(`订单金额无效: ${amount}元`);
      }

      const orderData = {
        appid: this.config.appid,
        mchid: this.config.mchid,
        description: description,
        out_trade_no: orderId,
        notify_url: this.config.notifyUrl,
        amount: {
          total: totalAmount,
          currency: 'CNY'
        },
        payer: {
          openid: openid
        }
      };

      console.log('📝 创建微信支付订单请求数据:', {
        ...orderData,
        payer: { openid: '***' } // 隐藏敏感信息
      });

      // 调用真实的微信支付API
      console.log('🌐 正在调用微信支付统一下单API...');
      const result = await this.callWechatPayAPI('/v3/pay/transactions/jsapi', 'POST', orderData);

      console.log('✅ 微信支付统一下单成功:', {
        prepay_id: result.prepay_id,
        out_trade_no: orderId
      });

      // 返回真实的API响应
      return {
        prepay_id: result.prepay_id,
        out_trade_no: orderId
      };

    } catch (error) {
      console.error('❌ 创建微信支付订单失败:', error);

      // 提供更详细的错误信息
      if (error.message && error.message.includes('INVALID_REQUEST')) {
        throw new Error('微信支付请求参数错误，请检查商户配置');
      } else if (error.message && error.message.includes('NOAUTH')) {
        throw new Error('微信支付权限不足，请检查商户号和API密钥');
      } else if (error.message && error.message.includes('ORDERPAID')) {
        throw new Error('订单已支付，请勿重复支付');
      } else if (error.message && error.message.includes('OUT_TRADE_NO_USED')) {
        throw new Error('商户订单号重复，请使用新的订单号');
      }

      throw error;
    }
  }

  /**
   * 生成小程序支付参数
   * @param {string} prepayId 预支付ID
   * @returns {object} 支付参数
   */
  generateMiniProgramPayParams(prepayId) {
    try {
      console.log('🔧 开始生成小程序支付参数...');
      console.log('📝 prepayId:', prepayId);

      const timeStamp = Math.floor(Date.now() / 1000).toString();
      const nonceStr = crypto.randomBytes(16).toString('hex');
      const packageStr = `prepay_id=${prepayId}`;

      console.log('📝 支付参数基础信息:', {
        appid: this.config.appid,
        timeStamp,
        nonceStr,
        package: packageStr
      });

      // 构造签名字符串 (API v3格式)
      const message = `${this.config.appid}\n${timeStamp}\n${nonceStr}\n${packageStr}\n`;
      console.log('📝 签名原文:', message);

      // 使用商户私钥进行RSA-SHA256签名
      let paySign;
      try {
        const fs = require('fs');
        const path = require('path');

        // 加载商户私钥
        const privateKeyPath = path.join(__dirname, '../certs', this.merchantId, 'apiclient_key.pem');

        if (fs.existsSync(privateKeyPath)) {
          const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
          console.log('✅ 商户私钥加载成功');

          // 使用RSA-SHA256签名
          const sign = crypto.createSign('RSA-SHA256');
          sign.update(message, 'utf8');
          paySign = sign.sign(privateKey, 'base64');

          console.log('✅ RSA签名生成成功');
        } else {
          console.warn('⚠️ 商户私钥文件不存在，使用HMAC-SHA256签名 (兼容模式)');

          // 降级使用HMAC-SHA256 (API v2兼容)
          paySign = crypto
            .createHmac('sha256', this.config.apiV3Key || this.config.api_v3_key)
            .update(message, 'utf8')
            .digest('base64');
        }
      } catch (keyError) {
        console.warn('⚠️ RSA签名失败，使用HMAC-SHA256签名:', keyError.message);

        // 降级使用HMAC-SHA256
        paySign = crypto
          .createHmac('sha256', this.config.apiV3Key || this.config.api_v3_key)
          .update(message, 'utf8')
          .digest('base64');
      }

      const payParams = {
        timeStamp,
        nonceStr,
        package: packageStr,
        signType: 'RSA',
        paySign
      };

      console.log('✅ 小程序支付参数生成完成:', {
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign ? '***' : undefined
      });

      return payParams;

    } catch (error) {
      console.error('❌ 生成小程序支付参数失败:', error);
      throw new Error('生成支付参数失败: ' + error.message);
    }
  }

  /**
   * 查询订单状态
   * @param {string} outTradeNo 商户订单号
   * @returns {object} 订单状态
   */
  async queryOrder(outTradeNo) {
    try {
      if (!outTradeNo) {
        throw new Error('缺少商户订单号');
      }

      console.log('🔍 查询微信支付订单状态:', outTradeNo);

      // 调用微信支付查询订单API
      const queryUrl = `/v3/pay/transactions/out-trade-no/${outTradeNo}?mchid=${this.config.mchid}`;
      const result = await this.callWechatPayAPI(queryUrl, 'GET');

      console.log('✅ 订单查询成功:', {
        out_trade_no: result.out_trade_no,
        trade_state: result.trade_state,
        transaction_id: result.transaction_id
      });

      return {
        trade_state: result.trade_state,
        transaction_id: result.transaction_id,
        out_trade_no: result.out_trade_no,
        trade_state_desc: result.trade_state_desc,
        amount: result.amount,
        success_time: result.success_time
      };

    } catch (error) {
      console.error('❌ 查询订单状态失败:', error);

      // 处理特定的微信支付错误
      if (error.message && error.message.includes('ORDERNOTEXIST')) {
        throw new Error('订单不存在或已过期');
      }

      throw error;
    }
  }

  /**
   * 关闭订单
   * @param {string} outTradeNo 商户订单号
   * @returns {boolean} 是否成功
   */
  async closeOrder(outTradeNo) {
    try {
      if (!outTradeNo) {
        throw new Error('缺少商户订单号');
      }

      console.log('❌ 关闭微信支付订单:', outTradeNo);

      // 调用微信支付关闭订单API
      const closeUrl = `/v3/pay/transactions/out-trade-no/${outTradeNo}/close`;
      const closeData = {
        mchid: this.config.mchid
      };

      await this.callWechatPayAPI(closeUrl, 'POST', closeData);

      console.log('✅ 订单关闭成功:', outTradeNo);
      return true;

    } catch (error) {
      console.error('❌ 关闭订单失败:', error);

      // 处理特定的微信支付错误
      if (error.message && error.message.includes('ORDERNOTEXIST')) {
        console.warn('⚠️ 订单不存在，视为关闭成功');
        return true;
      } else if (error.message && error.message.includes('ORDERPAID')) {
        throw new Error('订单已支付，无法关闭');
      }

      throw error;
    }
  }

  /**
   * 调用微信支付API
   * @param {string} url API路径
   * @param {string} method HTTP方法
   * @param {object} data 请求数据
   * @returns {object} API响应
   */
  async callWechatPayAPI(url, method, data = null) {
    try {
      const https = require('https');
      const fs = require('fs');
      const path = require('path');

      // 构建完整URL
      const fullUrl = `https://api.mch.weixin.qq.com${url}`;
      console.log('🌐 调用微信支付API:', fullUrl);

      // 准备请求数据
      const requestBody = data ? JSON.stringify(data) : '';
      const timestamp = Math.floor(Date.now() / 1000);
      const nonce = crypto.randomBytes(16).toString('hex');

      // 构建签名字符串 (微信支付API v3格式)
      const signatureString = `${method}\n${url}\n${timestamp}\n${nonce}\n${requestBody}\n`;
      console.log('📝 API签名原文:', signatureString.replace(/\n/g, '\\n'));

      // 加载商户私钥进行签名
      const privateKeyPath = path.join(__dirname, '../certs', this.merchantId, 'apiclient_key.pem');

      if (!fs.existsSync(privateKeyPath)) {
        throw new Error(`商户私钥文件不存在: ${privateKeyPath}`);
      }

      const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

      // 验证私钥格式
      if (!privateKey.includes('-----BEGIN PRIVATE KEY-----') && !privateKey.includes('-----BEGIN RSA PRIVATE KEY-----')) {
        throw new Error('商户私钥格式错误，请确保使用正确的PEM格式私钥文件');
      }

      const sign = crypto.createSign('RSA-SHA256');
      sign.update(signatureString, 'utf8');
      const signature = sign.sign(privateKey, 'base64');

      console.log('✅ API请求签名生成成功');

      // 构建Authorization头 (微信支付API v3格式)
      const authorization = `WECHATPAY2-SHA256-RSA2048 mchid="${this.config.mchid}",nonce_str="${nonce}",timestamp="${timestamp}",serial_no="${this.config.serial_no}",signature="${signature}"`;

      // 准备请求选项
      const options = {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': authorization,
          'User-Agent': 'danmu-server/1.0.0'
        }
      };

      if (data) {
        options.headers['Content-Length'] = Buffer.byteLength(requestBody);
      }

      console.log('📤 发送微信支付API请求...', {
        method,
        url,
        headers: {
          ...options.headers,
          'Authorization': 'WECHATPAY2-SHA256-RSA2048 ***' // 隐藏敏感信息
        }
      });

      // 发送HTTPS请求
      return new Promise((resolve, reject) => {
        const req = https.request(fullUrl, options, (res) => {
          let responseData = '';

          res.on('data', (chunk) => {
            responseData += chunk;
          });

          res.on('end', () => {
            console.log('📥 微信支付API响应状态:', res.statusCode);
            console.log('📥 微信支付API响应头:', res.headers);

            try {
              if (res.statusCode === 200 || res.statusCode === 201) {
                // 成功响应
                const result = responseData ? JSON.parse(responseData) : {};
                console.log('✅ 微信支付API调用成功:', {
                  prepay_id: result.prepay_id || '未返回',
                  status: res.statusCode
                });
                resolve(result);
              } else {
                // 错误响应
                console.error('❌ 微信支付API错误响应:', responseData);
                let errorMessage = `微信支付API错误 ${res.statusCode}`;

                try {
                  const errorData = JSON.parse(responseData);
                  errorMessage = `${errorData.code || 'UNKNOWN_ERROR'}: ${errorData.message || responseData}`;
                } catch (parseError) {
                  errorMessage = `${errorMessage}: ${responseData}`;
                }

                reject(new Error(errorMessage));
              }
            } catch (parseError) {
              console.error('❌ 解析微信支付API响应失败:', parseError);
              reject(new Error(`解析微信支付API响应失败: ${parseError.message}`));
            }
          });
        });

        req.on('error', (error) => {
          console.error('❌ 微信支付API网络请求失败:', error);
          reject(new Error(`网络请求失败: ${error.message}`));
        });

        // 设置请求超时
        req.setTimeout(30000, () => {
          req.destroy();
          reject(new Error('微信支付API请求超时'));
        });

        if (data) {
          req.write(requestBody);
        }

        req.end();
      });

    } catch (error) {
      console.error('❌ 调用微信支付API失败:', error);
      throw error;
    }
  }
}

module.exports = WechatPayService;
